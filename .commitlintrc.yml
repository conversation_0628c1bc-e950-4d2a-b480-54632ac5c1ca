rules:
  # Enforce commit message type to be one of the allowed values
  type-enum:
    - 2
    - always
    - [
      'feat',
      'fix',
      'chore',
      'docs',
      'style',
      'refactor',
      'perf',
      'test',
      'ci'
    ]

  # Enforce commit message type to be non-empty
  type-empty:
    - 2
    - never

  # Enforce lowercase for scope
  scope-case:
    - 2
    - always
    - 'lower-case'

  # Enforce commit message subject (description) to be non-empty
  subject-empty:
    - 2
    - never

  # Allow any case for the subject (description)
  subject-case:
    - 0
    - never
    - []

  # Enforce no period at the end of the subject
  subject-full-stop:
    - 2
    - never
    - '.'

  # Enforce maximum length of the header to 72 characters
  header-max-length:
    - 2
    - always
    - 72

  # Enforce maximum length of each line in the body to 100 characters
  body-max-length:
    - 2
    - always
    - 100

  # Enforce maximum length of the footer to 100 characters
  footer-max-length:
    - 2
    - always
    - 100
