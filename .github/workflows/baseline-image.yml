name: Baseline Image Build

on:
  pull_request:
    paths:
      - 'baseline/**'
      - '.github/workflows/baseline-image.yml'

permissions:
  id-token: write
  contents: read
  pull-requests: read

jobs:
  build-images:
    runs-on: ubuntu-latest-m
    strategy:
      matrix:
        variant: [cpu, gpu]
      fail-fast: false

    steps:
#      - name: Free Disk Space (Ubuntu)
#        uses: jlumbroso/free-disk-space@main
#        with:
#          # this might remove tools that are actually needed,
#          # if set to "true" but frees about 6 GB
#          tool-cache: false
#
#          # all of these default to true, but feel free to set to
#          # "false" if necessary for your workflow
#          large-packages: true
#          docker-images: true
#          swap-storage: true

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up tags
        id: tags
        run: |
          VERSION=$(grep -m1 "version = " baseline/images/pyproject.toml | cut -d'"' -f2)
          PR_NUMBER=$(echo $GITHUB_REF | sed 's:refs/pull/::' | sed 's:/merge::')
          TAG="${VERSION}-pr${PR_NUMBER}-${GITHUB_SHA::7}"
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "tag=${TAG}" >> $GITHUB_OUTPUT
          echo "Generated tag format: <version>-pr<number>-<sha>"
          echo "Example tag: $TAG"

      - name: Configure AWS credentials for CodeArtifact
        uses: gametimesf/github-actions/actions/auth-to-aws@v0
        with:
          role-arn: arn:aws:iam::728489771660:role/github-actions-codeartifact
          region: us-west-2

      - name: Generate AWS CodeArtifact token
        id: codeartifact-token
        run: |
          TOKEN=$(aws codeartifact get-authorization-token \
            --domain ml-artifacts \
            --domain-owner 728489771660 \
            --region us-west-2 \
            --query authorizationToken \
            --output text)
          echo "token=${TOKEN}" >> $GITHUB_OUTPUT
          echo "CodeArtifact token generated successfully"

      - name: Configure AWS credentials for ECR
        uses: gametimesf/github-actions/actions/auth-to-aws@v0
        with:
          role-arn: arn:aws:iam::728489771660:role/internal/github/mlplatform.ecr
          region: us-west-1

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr
        with:
          mask-password: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-flags: --debug

      - name: Cache Docker layers and UV
        uses: actions/cache@v4
        with:
          path: |
            /tmp/.buildx-cache
            /tmp/.uv-cache
          key: ${{ runner.os }}-buildx-${{ matrix.variant }}-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-${{ matrix.variant }}-
            ${{ runner.os }}-buildx-

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          # disabling provenance stops empty image builds: https://github.com/docker/build-push-action/issues/840
          provenance: false
          context: baseline/images
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/baseline-production:${{ steps.tags.outputs.tag }}-${{ matrix.variant }}
          cache-from: |
            type=local,src=/tmp/.buildx-cache
            type=local,src=/tmp/.uv-cache
          cache-to: |
            type=local,dest=/tmp/.buildx-cache-new,mode=max
            type=local,dest=/tmp/.uv-cache-new,mode=max
          build-args: |
            BASE_IMAGE=${{ matrix.variant == 'gpu' && 'public.ecr.aws/sagemaker/sagemaker-distribution:2.3.1-gpu' || 'public.ecr.aws/sagemaker/sagemaker-distribution:2.3.1-cpu' }}
            UV_INDEX_CODEARTIFACT_USERNAME=aws
            UV_INDEX_CODEARTIFACT_PASSWORD=${{ steps.codeartifact-token.outputs.token }}

      - name: Replace Existing Cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache
          rm -rf /tmp/.uv-cache
          mv /tmp/.uv-cache-new /tmp/.uv-cache
