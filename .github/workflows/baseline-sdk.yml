name: 🧪 Baseline SDK CI Pipeline

on:
  workflow_dispatch:
  pull_request:
    paths:
      - 'baseline/sdk/**'
      - '.github/workflows/baseline-sdk.yml'
  push:
    branches:
      - main
    paths:
      - 'baseline/sdk/**'
      - '.github/workflows/baseline-sdk.yml'

permissions:
  id-token: write
  contents: read

jobs:
  test-baseline-sdk:
    name: 🧪 Test Baseline SDK Package
    uses: gametimesf/github-actions-python/.github/workflows/pyproject-uv-tests.yml@main
    with:
      project_path: 'baseline/sdk'
      python_version: '3.10'
      test_command: 'pytest tests/unit -v'
      optional_dependencies: 'dev'
      use_codeartifact: true
      role_arn: 'arn:aws:iam::728489771660:role/internal/github/mlplatform.codeartifact'
      region: 'us-west-2'
      codeartifact_domain: 'ml-artifacts'
      codeartifact_domain_owner: '728489771660'
