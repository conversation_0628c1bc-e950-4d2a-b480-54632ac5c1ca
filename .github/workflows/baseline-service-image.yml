name: Baseline Service Image Build

on:
  pull_request:
    paths:
      - 'baseline/service/**'
      - 'mlutils/**'
      - '.github/workflows/baseline-service-image.yml'

permissions:
  id-token: write
  contents: read
  pull-requests: read

jobs:
  build-service-image:
    name: Build Service Image
    runs-on: ubuntu-latest-m

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up tags
        id: tags
        run: |
          VERSION=$(grep -m1 "version = " baseline/service/pyproject.toml | cut -d'"' -f2)
          PR_NUMBER=$(echo $GITHUB_REF | sed 's:refs/pull/::' | sed 's:/merge::')
          TAG="${VERSION}-pr${PR_NUMBER}-${GITHUB_SHA::7}"
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "tag=${TAG}" >> $GITHUB_OUTPUT
          echo "Generated tag format: <version>-pr<number>-<sha>"
          echo "Example tag: $TAG"

      - name: Get CodeArtifact token
        id: codeartifact-token
        uses: gametimesf/github-actions-python/actions/codeartifact-token@main
        with:
          role-arn: arn:aws:iam::728489771660:role/internal/github/mlplatform.build
          region: us-west-2
          codeartifact-domain: ml-artifacts
          codeartifact-domain-owner: 728489771660

      - name: Set up CodeArtifact authentication
        uses: gametimesf/github-actions-python/actions/codeartifact-auth@main
        with:
          token: ${{ steps.codeartifact-token.outputs.token }}
          repository-endpoint: ${{ steps.codeartifact-token.outputs.repository-endpoint }}
          codeartifact-domain: ml-artifacts
          codeartifact-domain-owner: 728489771660
          region: us-west-2


      - name: Configure AWS credentials for ECR
        uses: gametimesf/github-actions/actions/auth-to-aws@v0
        with:
          role-arn: arn:aws:iam::728489771660:role/internal/github/mlplatform.build
          region: us-west-1

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr
        with:
          mask-password: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          buildkitd-flags: --debug

      - name: Cache Docker layers and UV
        uses: actions/cache@v4
        with:
          path: |
            /tmp/.buildx-cache
            /tmp/.uv-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and push service image
        uses: docker/build-push-action@v5
        with:
          # disabling provenance stops empty image builds: https://github.com/docker/build-push-action/issues/840
          provenance: false
          context: baseline/service
          push: true
          secrets: |
            CODEARTIFACT_AUTH_TOKEN=${{ steps.codeartifact-token.outputs.token }}
          tags: ${{ steps.login-ecr.outputs.registry }}/baseline-service-production:${{ steps.tags.outputs.tag }}
          cache-from: |
            type=local,src=/tmp/.buildx-cache
            type=local,src=/tmp/.uv-cache
          cache-to: |
            type=local,dest=/tmp/.buildx-cache-new,mode=max
            type=local,dest=/tmp/.uv-cache-new,mode=max

      - name: Replace Existing Cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache
          rm -rf /tmp/.uv-cache
          mv /tmp/.uv-cache-new /tmp/.uv-cache
