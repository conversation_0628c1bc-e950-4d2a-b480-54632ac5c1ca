name: Protocol Buffers
on:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/compile-proto.yml'
      - '**/*.proto'
      - 'buf.gen.yaml'
      - 'buf.yaml'
      - 'gen/python/pyproject.toml'
  pull_request:
    paths:
      - '.github/workflows/compile-proto.yml'
      - '**/*.proto'
      - 'buf.gen.yaml'
      - 'buf.yaml'
      - 'gen/python/pyproject.toml'
jobs:
  proto_checks:
    runs-on: ubuntu-24.04
    permissions:
      contents: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        ref: ${{ github.head_ref }}
        fetch-depth: 0

    - name: Setup buf
      uses: bufbuild/buf-setup-action@v1
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}

    - name: Lint proto files
      run: buf lint

    # Only generate and commit code on push to main
    - name: Generate code from proto files
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      run: |
        buf generate
        # Create __init__.py files in all package directories
        find gen/python/* -type d -exec touch {}/__init__.py \;

    - name: Commit generated code
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      run: |
        git config --local user.name "GitHub Actions"
        git config --local user.email "<EMAIL>"
        git add gen/
        git diff --staged --quiet || (git commit -m "chore(protos): generate code from proto definitions" && git push origin HEAD:${{ github.ref_name }})
