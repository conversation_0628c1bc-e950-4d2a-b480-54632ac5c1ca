name: Manual Python Package Release

on:
  workflow_dispatch:
    inputs:
      reason:
        description: 'Reason for manual release'
        required: true
        default: 'Manual release triggered without code changes'

jobs:
  manual-release:
    uses: gametimesf/github-actions-python/.github/workflows/semantic-release.yml@v1.0.0
    with:
      package_mappings: |
        {
          "gen/python": "gametime_protos"
        }
      aws_role_arn: arn:aws:iam::728489771660:role/internal/github/protos.codeartifact
      aws_region: us-west-2
      aws_domain: ml-artifacts
      aws_domain_owner: 728489771660
      aws_repository: python-packages
      publish_to_codeartifact: true
      python_version: 3.10
    secrets:
      token: ${{ secrets.GITHUB_TOKEN }}
