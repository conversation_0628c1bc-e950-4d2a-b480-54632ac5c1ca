name: 🧪 MLUtils CI Pipeline

on:
  pull_request:
    paths:
      - 'mlutils/**'
      - '.github/workflows/mlutils.yml'
  push:
    branches:
      - main
    paths:
      - 'mlutils/**'
      - '.github/workflows/mlutils.yml'

jobs:
  test-mlutils:
    name: 🧪 Test MLUtils Package
    # Reference the workflow directly from your feature branch
    uses: gametimesf/github-actions-python/.github/workflows/pyproject-uv-tests.yml@main
    with:
      project_path: 'mlutils'
      python_version: '3.10'
      # The test command based on the dev dependencies in pyproject.toml
      test_command: 'pytest tests/ -v'
      optional_dependencies: 'aiohttp,dev,grpc,pandas,sklearn'
