---
name: pre-commit checks

on:
  pull_request:

permissions:
  id-token: write
  contents: read
  pull-requests: read

env:
  tf_version: 1.9.2
  tg_version: 0.59.7
  working_dir: "services/aws"

jobs:
  pre-commit:
    runs-on: ubuntu-latest-m
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'

      # Cache pre-commit environments
      - uses: actions/cache@v4
        id: pre-commit-cache
        with:
          path: ~/.cache/pre-commit
          key: pre-commit-${{ runner.os }}-py-${{ env.pythonLocation }}-${{ hashFiles('.pre-commit-config.yaml') }}
          restore-keys: |
            pre-commit-${{ runner.os }}-py-${{ env.pythonLocation }}-

      # cache tflint setup
      - uses: actions/cache@v4
        with:
          path: ~/.tflint.d/plugins
          key: tflint-${{ hashFiles('.tflint.hcl') }}

      # tflint needed for the pre-commit checks
      - uses: terraform-linters/setup-tflint@v4
        with:
          tflint_version: v0.46.1

      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.tf_version }}

      # Install Terragrunt
      # Do our own install to allow for GitHub private repo access override
      - uses: supplypike/setup-bin@v4
        with:
          uri: "https://github.com/gruntwork-io/terragrunt/releases/download/v0.62.0/terragrunt_linux_amd64"
          name: "terragrunt"
          version: "0.62.0"

      # Cache terraform-docs
      - uses: actions/cache@v4
        id: terraform-docs-cache
        with:
          path: /usr/local/bin/terraform-docs
          key: terraform-docs-v0.19.0

      # Install terraform-docs only if not cached
      - name: Install terraform-docs
        if: steps.terraform-docs-cache.outputs.cache-hit != 'true'
        run: |
          curl -Lo ./terraform-docs.tar.gz https://github.com/terraform-docs/terraform-docs/releases/download/v0.19.0/terraform-docs-v0.19.0-linux-amd64.tar.gz
          tar -xzf terraform-docs.tar.gz
          chmod +x terraform-docs
          sudo mv terraform-docs /usr/local/bin/

      # apply pre-commit checks
      - uses: pre-commit/action@v3.0.1
        with:
          extra_args: --color=always --from-ref ${{ github.event.pull_request.base.sha }} --to-ref ${{ github.event.pull_request.head.sha }}

      # notify user to enable pre-commit locally if changes are detected
      - name: Advise user to install pre-commit hooks
        if: failure()
        run: |
          echo "Avoid these CI failures next time by installing pre-commit in your local repo:"
          echo "  pre-commit install"

  commitlint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      # cache commitlint setup
      - uses: actions/cache@v4
        with:
          path: ~/.npm
          key: npm-${{ hashFiles('package-lock.json') }}

      # run commitlint
      - uses: wagoid/commitlint-github-action@v5
