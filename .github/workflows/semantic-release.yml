---
name: Semantic Release

on:
  push:
    branches: [main]

jobs:
  semantic-release:
    uses: gametimesf/github-actions-python/.github/workflows/semantic-release.yml@v1.0.0
    with:
      package_mappings: '{
        "baseline/images": "baseline-images",
        "baseline/sdk": "baseline-sdk",
        "baseline/service": "baseline-service",
        "mlctl": "mlctl",
        "mlutils": "mlutils"
      }'
      aws_role_arn: 'arn:aws:iam::728489771660:role/github-actions-codeartifact'
      aws_region: 'us-west-2'
      aws_domain: 'ml-artifacts'
      aws_domain_owner: '728489771660'
      aws_repository: 'python-packages'
      publish_to_codeartifact: true
      python_version: '3.10'
    secrets:
      token: ${{ secrets.GITHUB_TOKEN }}
