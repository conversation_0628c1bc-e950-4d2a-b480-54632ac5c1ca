repos:

  - repo: https://github.com/bufbuild/buf
    rev: v1.45.0
    hooks:
      - id: buf-lint

  # linting yaml files
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.35.1
    hooks:
      - id: yamllint
        args:
          - "-d relaxed"

  # Some out-of-the-box hooks for pre-commit
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      # Makes sure files end in a newline and only a newline.
      - id: end-of-file-fixer
      # Trims trailing whitespace.
      - id: trailing-whitespace
      # checks json files for parseable syntax.
      - id: check-json
      # Sets a standard for formatting json files.
      - id: pretty-format-json
        args:
          - --autofix
          - --indent=4

  - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
    rev: v9.16.0
    hooks:
        - id: commitlint
          stages: [commit-msg]

  # A pre-commit hook for the uv tool
  - repo: https://github.com/astral-sh/uv-pre-commit
    # uv version.
    rev: 0.6.4
    hooks:
      # uv-lock: Updates the uv.lock files whenever dependencies change
      # This ensures your lockfiles are always in sync with your pyproject.toml files
      # Will run automatically on each commit that modifies Python dependencies
      - id: uv-lock
      # uv-sync: Synchronizes dependencies across your workspace
      # The "--locked" flag ensures exact versions from lockfiles are used
      - id: uv-sync
        args: [ "--locked" ]
