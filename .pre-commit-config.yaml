repos:
  # A tool (and pre-commit hook) to automatically upgrade syntax for newer versions of the language.
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.1
    hooks:
      - id: pyupgrade
        args: [--py310-plus]

  # linting yaml files
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.35.1
    hooks:
      - id: yamllint
        args:
          - "-d relaxed"

  # Some out-of-the-box hooks for pre-commit
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      # Makes sure files end in a newline and only a newline.
      - id: end-of-file-fixer
      # Trims trailing whitespace.
      - id: trailing-whitespace
      # checks json files for parseable syntax.
      - id: check-json
      - id: check-toml
      # Sets a standard for formatting json files.
      - id: pretty-format-json
        args:
          - --autofix
          - --indent=4

  - repo: https://github.com/pappasam/toml-sort
    rev: v0.24.2
    hooks:
      - id: toml-sort
      - id: toml-sort-fix
        args:
          - --all
          - --in-place

  #  An extremely fast Python linter and code formatter, written in Rust.
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.9.3
    hooks:
      # Run the linter.
      - id: ruff
        args: [--fix, --target-version, py310]
      # Run the formatter.
      - id: ruff-format
        args: [--target-version, py310]

  # Bandit is a tool designed to find common security issues in Python code.
  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.2
    hooks:
      - id: bandit
        args:
          - "--skip=B101" # Skip B101 (assert_used) check
          - "--exclude=./tests,./*/tests" # Exclude test directories
        files: .py$

  - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
    rev: v9.16.0
    hooks:
      - id: commitlint
        stages: [commit-msg]

  # pre-commit git hooks to take care of Terraform configurations
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.97.3
    hooks:
      # - id: terraform_docs
      #   args:
      #     - --hook-config=--add-to-existing-file=true     # Boolean. true or false
      #     - --hook-config=--create-file-if-not-exist=true # Boolean. true or false
      - id: terraform_fmt

  # A pre-commit hook for the uv tool
  - repo: https://github.com/astral-sh/uv-pre-commit
    rev: 0.7.2
    hooks:
      - id: uv-lock
        args:
          - --project
          - baseline/service
          - --project
          - baseline/images
          - --project
          - baseline/sdk
          - --project
          - prism/service
          - --project
          - mlutils
          - --project
          - mlctl
