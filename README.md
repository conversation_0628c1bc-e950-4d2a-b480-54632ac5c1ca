[![python](https://img.shields.io/badge/Python-3.10-3776AB.svg?style=flat&logo=python&logoColor=white)](https://www.python.org)
![Pre-commit Badge](https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=yellow)

# mlplatform

### Pre-Commit Setup

This repository uses pre-commit hooks to ensure code quality and consistency.
To set up pre-commit, follow the instructions in [pre-commit-setup.md](https://github.com/gametimesf/gametime-data/blob/master/docs/pre-commit-setup.md).

## Infrastructure

![ML Platform Infrastructure](diagrams/diagram.png)

This diagram shows the complete ML Platform infrastructure including:
- MWAA Environment and Job Execution
- Baseline Service and SageMaker Integration
- Pricing Service and The Algo Integration
- Cross-Region VPC Peering
- GitHub Integration for Proto Generation and DAG Deployment
- Details on how to update the diagram can be found in the [README](diagrams/README.md).
