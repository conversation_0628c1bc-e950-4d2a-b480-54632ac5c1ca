ARG BASE_IMAGE="public.ecr.aws/sagemaker/sagemaker-distribution:2.3.1-cpu"
FROM ${BASE_IMAGE}

ENV HOME=/home/<USER>

# Configure UV
ARG UV_INDEX_CODEARTIFACT_USERNAME
ARG UV_INDEX_CODEARTIFACT_PASSWORD
ENV UV_INDEX_CODEARTIFACT_USERNAME=${UV_INDEX_CODEARTIFACT_USERNAME}
ENV UV_INDEX_CODEARTIFACT_PASSWORD=${UV_INDEX_CODEARTIFACT_PASSWORD}
ENV UV_SYSTEM_PYTHON=1
ENV UV_LINK_MODE=copy
ENV UV_COMPILE_BYTECODE=1

# Use the forwarded SSH socket for git authentication
RUN mkdir -p "$HOME/.ssh" && \
    ssh-keyscan github.com >> "$HOME/.ssh/known_hosts"

COPY pyproject.toml ./

# Copy uv from the official distroless image (this is what the service image does)
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Install dependencies using uv exactly like the service image
RUN --mount=type=cache,target=/root/.cache/uv \
    mkdir -p -m 0700 ~/.ssh && \
    ssh-keyscan github.com >> ~/.ssh/known_hosts && \
    micromamba run -n base uv pip install --no-cache-dir -e .

ENTRYPOINT [ "entrypoint-jupyter-server" ]
CMD []
