# Baseline Images

This repository contains scripts for building and releasing a unified baseline Docker image used in our machine learning infrastructure. This image serves as the foundation for our ML workloads and is designed to be portable across different hardware platforms—whether running on CPU-only systems or with GPU support where available.

## Overview

Our image building process follows a staging-to-production promotion workflow, ensuring consistency and reliability in our deployments. Images are first built and tested in a staging environment, then promoted to production using Amazon ECR's manifest copying feature. This approach guarantees that the exact same image that passed testing in staging is deployed to production.

## Build and Promotion Workflow

The process consists of two main steps:

1. **Build and Push to Staging**: Build the image in the staging environment. This step compiles the image from source and pushes it to our staging ECR repository. The image can then be thoroughly tested in staging.

2. **Promote to Production**: Once testing is complete, promote the staging image to production using the same script with the production environment parameter. This process copies the image manifest from staging to production ECR, ensuring byte-for-byte identical images between environments.

## Usage

Before first use, make the build script executable:

```bash
chmod +x build.sh
```

The script uses the following syntax:

```bash
./build.sh <environment> <version> [tag-suffix]
```

### Parameters

The script accepts these parameters in order:

`environment`: Specifies the target environment:
- `staging`: Builds new images from source and pushes to staging ECR
- `production`: Copies existing images from staging to production ECR

`version`: Defines the semantic version number, requiring at least three numbers separated by periods (e.g., 1.2.3).

`tag-suffix` (optional): Adds an additional identifier to the image tags, useful for release candidates (rc1), beta releases (beta1), or other special designations.

### Example Workflow

A typical release process might look like these examples:

1. Build and push the staging image for testing:
```bash
./build.sh staging 1.2.3 rc1
```

2. Promote the tested staging image to production:
```bash
./build.sh production 1.2.3 cpu rc1
```

### Generated Images

The script generates images following this pattern:

```
728489771660.dkr.ecr.us-west-1.amazonaws.com/baseline-{environment}:{version}[-suffix]
```

For example, running `./build.sh staging 1.2.3 rc1` produces:
```
728489771660.dkr.ecr.us-west-1.amazonaws.com/baseline-staging:1.2.3-rc1
```

## Technical Details

### Image Building (Staging)

When building for staging, the script:
1. Builds images using Docker's BuildKit engine
2. Mounts SSH keys securely for private repository access
3. Uses multi-stage builds to optimize image size
4. Configures PyTorch with appropriate CUDA support for GPU images
5. Pushes images using Docker V2 Schema 2 manifest format for ECR compatibility

### Production Promotion

When promoting to production, the script:
1. Retrieves image manifests from staging ECR
2. Validates manifest availability before proceeding
3. Uses AWS ECR's manifest copying feature for efficient promotion
4. Maintains exact binary equivalence between environments

## Troubleshooting

If you encounter issues:

1. **Failed Staging Push**: Ensure you have proper ECR permissions and are logged into the registry. The script automatically handles Docker login, but your AWS credentials must be properly configured.

2. **Production Promotion Failure**: Verify that the source images exist in staging ECR. The script will fail gracefully with an error message if source images are not found.

3. **Build Failures**: Check that required build dependencies (Docker, AWS CLI, jq) are installed and properly configured. Also ensure your SSH key has access to required private repositories.
