#!/bin/bash
# Enable strict error handling:
# -u: Error on undefined variables
# -o pipefail: Return error if any command in a pipe fails
set -uo pipefail

# Logging function that prepends timestamp to all log messages
# Usage: log "Your message here"
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Print usage information
usage() {
    echo "Usage: $0 <environment> <version> [variant] [tag-suffix]"
    echo "  environment: staging or production"
    echo "  version: version number (e.g., 1.2.3)"
    echo "  variant: cpu or gpu (optional, builds both if omitted)"
    echo "  tag-suffix: additional tag suffix (e.g., rc1, beta) (optional)"
    echo ""
    echo "Examples:"
    echo "  $0 staging 1.2.3              # Builds both CPU and GPU"
    echo "  $0 staging 1.2.3 cpu          # Builds only CPU"
    echo "  $0 staging 1.2.3 gpu rc1      # Builds only GPU with rc1 suffix"
    echo "  $0 staging 1.2.3 '' beta      # Builds both with beta suffix"
}

# Validate required command line arguments
if [ -z "${1:-}" ] || [ -z "${2:-}" ]; then
    log "Error: Missing required arguments"
    usage
    exit 1
fi

# Store command line arguments in named variables for clarity
ENVIRONMENT=$1
VERSION=$2
VARIANT=${3:-}        # Optional: cpu, gpu, or empty for both
TAG_SUFFIX=${4:-}     # Optional: additional tag suffix

# Configure AWS ECR registry URL and SSH key (from 1Password)
REGISTRY="728489771660.dkr.ecr.us-west-1.amazonaws.com"
SSH_KEY="op://ML Platform Team/gt-mlp-machine github/ssh key"

# Log build configuration for debugging purposes
log "Starting build process"
log "Environment: $ENVIRONMENT"
log "Version: $VERSION"
log "Variant: ${VARIANT:-both}"
log "Tag Suffix: ${TAG_SUFFIX:-none}"

# Validate environment argument
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
    log "Error: Environment must be 'production' or 'staging'"
    exit 1
fi

# Validate version format (at least three dot‐separated numbers)
if ! [[ $VERSION =~ ^([0-9]+\.){2,}[0-9]+.*$ ]]; then
    log "Error: Version must have at least 3 period-separated numbers (e.g., 1.2.3)"
    exit 1
fi

# Validate variant if provided
if [ -n "$VARIANT" ] && [[ "$VARIANT" != "cpu" && "$VARIANT" != "gpu" ]]; then
    log "Error: Variant must be 'cpu', 'gpu', or empty"
    exit 1
fi

PROD_BASE_TAG="$REGISTRY/baseline-production:$VERSION"

# Function to build and push an image
build_and_push() {
    local variant=$1
    local tag_suffix=$2
    local full_tag="${PROD_BASE_TAG}-${variant}${tag_suffix:+-$tag_suffix}"

    log "Building staging ${variant} image..."
    if [ "$variant" = "cpu" ]; then
        DOCKER_BUILDKIT=1 op read "$SSH_KEY" | docker build \
            --platform=linux/amd64 \
            -t "$full_tag" \
            --ssh default=/dev/stdin \
            --provenance=false \
            --build-arg BASE_IMAGE=public.ecr.aws/sagemaker/sagemaker-distribution:2.3.1-cpu \
            .
    else
        DOCKER_BUILDKIT=1 op read "$SSH_KEY" | docker build \
            --platform=linux/amd64 \
            -t "$full_tag" \
            --ssh default=/dev/stdin \
            --provenance=false \
            --build-arg BASE_IMAGE=public.ecr.aws/sagemaker/sagemaker-distribution:2.3.1-gpu \
            .
    fi

    log "Pushing staging ${variant} image..."
    # If manifest inspect fails (i.e. image not found), push the image
    docker manifest inspect "$full_tag" > /dev/null 2>&1 || docker push "$full_tag" || log "Warning: Staging ${variant} image push failed"
}

# Function to promote an image to production
promote_to_production() {
    local variant=$1
    local tag_suffix=$2
    local variant_tag="${VERSION}-${variant}${tag_suffix:+-$tag_suffix}"

    log "Copying staging ${variant} image to production..."
    local staging_image="$REGISTRY/baseline-staging:$variant_tag"
    local prod_image="$REGISTRY/baseline-production:$variant_tag"

    docker pull "$staging_image"
    docker tag "$staging_image" "$prod_image"
    docker push "$prod_image"
}

# Authenticate with AWS ECR
log "Logging into ECR..."
aws ecr get-login-password --region us-west-1 | docker login --username AWS --password-stdin "$REGISTRY"

if [ "$ENVIRONMENT" = "staging" ]; then
    # STAGING ENVIRONMENT BUILD PROCESS
    if [ -z "$VARIANT" ] || [ "$VARIANT" = "cpu" ]; then
        build_and_push "cpu" "$TAG_SUFFIX"
    fi
    if [ -z "$VARIANT" ] || [ "$VARIANT" = "gpu" ]; then
        build_and_push "gpu" "$TAG_SUFFIX"
    fi
elif [ "$ENVIRONMENT" = "production" ]; then
    # PRODUCTION ENVIRONMENT PROMOTION PROCESS
    if [ -z "$VARIANT" ] || [ "$VARIANT" = "cpu" ]; then
        promote_to_production "cpu" "$TAG_SUFFIX"
    fi
    if [ -z "$VARIANT" ] || [ "$VARIANT" = "gpu" ]; then
        promote_to_production "gpu" "$TAG_SUFFIX"
    fi
fi

log "Build process completed"
