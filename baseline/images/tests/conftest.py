import pytest
import os
import docker
import time
import requests
from docker.client import DockerClient


@pytest.fixture(scope="session")
def docker_client() -> DockerClient:
    """
    Provide a Docker client instance for interacting with Docker.
    """
    return docker.from_env()


@pytest.fixture(scope="session")
def docker_image_name():
    """Return the Docker image name used for testing."""
    return os.getenv("TEST_IMAGE", "baseline:latest")


@pytest.fixture(scope="session")
def docker_file_path():
    """Return the path to the Dockerfile."""
    return os.path.join(os.path.dirname(os.path.dirname(__file__)), "Dockerfile")


@pytest.fixture(scope="session")
def run_container(docker_client, docker_image_name):
    """
    Build and run the test container, ensuring any previously running container
    with the same name is stopped and removed.
    """
    container_name = "jupyter_test"
    max_retries = 15  # 30 seconds total with 2 second interval
    retry_interval = 2

    # Stop and remove existing container if present
    try:
        existing = docker_client.containers.get(container_name)
        existing.stop()
        existing.remove()
    except docker.errors.NotFound:
        pass

    # Start a new container
    container = docker_client.containers.run(
        docker_image_name,
        name=container_name,
        ports={"8888/tcp": 8888},
        detach=True,
    )

    def is_container_ready():
        container.reload()
        if container.status != "running":
            return False

        logs = container.logs().decode("utf-8")
        return "Jupyter Server" in logs and "is running at:" in logs

    # Wait for container to be ready
    for attempt in range(max_retries):
        try:
            if is_container_ready():
                break
        except Exception as e:
            print(f"Attempt {attempt + 1}/{max_retries} failed: {e}")

        if attempt == max_retries - 1:
            raise RuntimeError(
                f"Container failed to be ready after {max_retries * retry_interval} seconds"
            )

        time.sleep(retry_interval)

    yield container

    # Cleanup
    try:
        container.stop()
        container.remove()
    except Exception as e:
        print(f"Error during cleanup: {e}")


@pytest.fixture(scope="session")
def jupyter_session(run_container):
    """
    Return a requests.Session that is connected to the running JupyterLab instance
    and has the XSRF token set (if needed).
    """
    base_url = "http://localhost:8888"
    api_url = f"{base_url}/jupyterlab/default/api/status"
    session = requests.Session()

    # Retry logic to wait for JupyterLab to become available
    max_retries = 10
    retry_interval = 2

    for attempt in range(max_retries):
        try:
            response = session.get(api_url, timeout=5)
            if response.status_code == 200:
                # Grab the XSRF cookie if available
                for cookie in session.cookies:
                    if cookie.name.endswith("_xsrf"):
                        session.headers["X-XSRFToken"] = cookie.value
                break
            else:
                print(
                    f"Attempt {attempt + 1}/{max_retries}: Status code {response.status_code}"
                )
        except requests.exceptions.RequestException as e:
            print(f"Attempt {attempt + 1}/{max_retries} failed: {e}")

        if attempt == max_retries - 1:
            raise TimeoutError("JupyterLab failed to start within 20 seconds.")

        time.sleep(retry_interval)

    session.headers.update({"Content-Type": "application/json"})
    return session
