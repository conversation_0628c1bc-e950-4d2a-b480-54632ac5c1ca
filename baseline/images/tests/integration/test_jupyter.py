import pytest
import docker
import requests
import time
import re
import ast
import json
import websocket
from typing import Any
from collections.abc import Generator

# Import specific types from Docker SDK
from docker.client import DockerClient
from docker.models.containers import Container

BASE_URL: str = "http://localhost:8888"


@pytest.fixture
def required_packages() -> list[tuple[str, str]]:
    """
    Fixture defining the essential packages that should be installed and importable.
    Each entry is a tuple of (package_name, import_statement, optional_environment_setup).
    """
    return [
        ("numpy", "import numpy"),
        (
            "torch",
            """
import torch
print(f"torch version: {torch.__version__}")
""",
        ),
        (
            "mlutils",
            """
import mlutils
from mlutils.utils import pricing_markups
""",
        ),
        (
            "baseline",
            """
import os
os.environ['BASELINE_S3_BUCKET'] = 'test-bucket'
os.environ['BASELINE_ENVIRONMENT'] = 'test'
import baseline
""",
        ),
        ("pandas", "import pandas"),
        ("scikit-learn", "import sklearn"),
        ("xgboost", "import xgboost"),
        # ("catboost", "import catboost"), known issues: https://github.com/catboost/catboost/issues/2671
        ("seaborn", "import seaborn"),
        ("boto3", "import boto3"),
    ]


def wait_for_jupyter(timeout: int = 10) -> None:
    """
    Poll http://localhost:8888/api/status until we get a 200
    or until 'timeout' seconds have elapsed.
    Raises a TimeoutError if JupyterLab doesn't start in time.
    """
    start: float = time.time()
    while time.time() - start < timeout:
        try:
            # A simple status endpoint you can probe for readiness
            response: requests.Response = requests.get(
                f"{BASE_URL}/api/status", timeout=3
            )
            if response.status_code == 200:
                return  # Jupyter is ready
        except requests.exceptions.ConnectionError:
            pass
        time.sleep(1)
    raise TimeoutError(f"JupyterLab failed to start within {timeout} seconds.")


@pytest.fixture(scope="session")
def run_container(docker_client: DockerClient) -> Generator[Container, None, None]:
    """
    Build/Run the test container (named 'jupyter_test') if not already running.
    Then actively wait for JupyterLab to become ready before yielding.
    """
    container_name: str = "jupyter_test"

    # Stop/remove any existing container with the same name
    try:
        existing: Container = docker_client.containers.get(container_name)
        existing.stop()
        existing.remove()
    except docker.errors.NotFound:
        pass

    # Start the container
    container: Container = docker_client.containers.run(
        "baseline:latest",  # Or whatever your image tag is
        name=container_name,
        ports={"8888/tcp": 8888},
        detach=True,
    )

    # Actively wait for Jupyter to be ready instead of a blind sleep
    wait_for_jupyter(timeout=60)

    # Yield control to the tests
    yield container

    # Cleanup after tests
    try:
        container.stop()
        container.remove()
    except Exception as e:
        print(f"Error during container cleanup: {e}")


@pytest.fixture(scope="session")
def jupyter_session(run_container: Container) -> requests.Session:
    """
    Once the container is confirmed running, create a requests.Session
    that picks up any XSRF tokens from /lab.
    """
    s: requests.Session = requests.Session()
    # Visit the /lab page to acquire any XSRF cookies
    _ = s.get(f"{BASE_URL}/lab")
    for cookie in s.cookies:
        if cookie.name.endswith("_xsrf"):
            s.headers["X-XSRFToken"] = cookie.value

    # Set Content-Type for JSON API requests
    s.headers.update({"Content-Type": "application/json"})
    return s


def create_kernel(session: requests.Session, kernel_name: str = "python3") -> str:
    """
    Create a new Jupyter kernel via REST API and return its kernel_id.
    """
    url: str = f"{BASE_URL}/api/kernels"
    response: requests.Response = session.post(url, json={"name": kernel_name})
    response.raise_for_status()
    return response.json()["id"]


def create_ws_connection(kernel_id: str) -> websocket.WebSocket:
    """
    Open a WebSocket connection to the Jupyter kernel.
    """
    ws_url: str = f"ws://localhost:8888/api/kernels/{kernel_id}/channels"
    return websocket.create_connection(ws_url)


def execute_code_ws(
    ws: websocket.WebSocket, code: str, msg_id: str = "test"
) -> list[dict[str, Any]]:
    """
    Execute Python code in the kernel via WebSocket and capture the output.
    Returns a list of all 'stream', 'display_data', or 'execute_result' messages.
    Raises an error if the code execution fails.
    """
    message: dict[str, Any] = {
        "header": {"msg_id": msg_id, "msg_type": "execute_request"},
        "parent_header": {},
        "metadata": {},
        "content": {
            "code": code,
            "silent": False,
            "store_history": True,
            "user_expressions": {},
            "allow_stdin": False,
            "stop_on_error": True,
        },
        "channel": "shell",
    }
    ws.send(json.dumps(message))

    outputs: list[dict[str, Any]] = []
    while True:
        raw_resp: str = ws.recv()
        resp: dict[str, Any] = json.loads(raw_resp)
        if resp.get("parent_header", {}).get("msg_id") == msg_id:
            msg_type: str = resp.get("msg_type", "")
            if msg_type in ["stream", "display_data", "execute_result"]:
                outputs.append(resp["content"])
            elif msg_type == "execute_reply":
                # If status != "ok", it indicates a runtime error
                if resp["content"].get("status") != "ok":
                    raise RuntimeError(f"Error executing code: {resp['content']}")
                break
    return outputs


def test_container_running(run_container: Container) -> None:
    """
    Check if the Docker container is actually running.
    """
    run_container.reload()
    assert run_container.status == "running", "Container is not running."


def test_jupyter_running(jupyter_session: requests.Session) -> None:
    """
    Verify JupyterLab responds on /lab.
    """
    response: requests.Response = jupyter_session.get(f"{BASE_URL}/lab")
    assert response.status_code == 200, "JupyterLab /lab endpoint not reachable."
    assert "JupyterLab" in response.text, "Unexpected content in JupyterLab page."


def test_api_endpoints(jupyter_session: requests.Session) -> None:
    """
    Quick check that these core Jupyter API endpoints return 200.
    """
    endpoints: list[str] = [
        "/api/contents",
        "/api/kernels",
        "/api/kernelspecs",
        "/api/terminals",
    ]
    for endpoint in endpoints:
        response: requests.Response = jupyter_session.get(f"{BASE_URL}{endpoint}")
        assert response.status_code == 200, f"Endpoint '{endpoint}' did not return 200."


def test_kernel_execution(jupyter_session: requests.Session) -> None:
    """
    Ensure we can create a kernel and execute a simple math expression.
    """
    kernel_id: str = create_kernel(jupyter_session)
    ws: websocket.WebSocket = create_ws_connection(kernel_id)
    try:
        execute_code_ws(ws, "2 + 2", msg_id="simple_math")
    finally:
        ws.close()


def test_installed_packages(
    jupyter_session: requests.Session, required_packages: list[tuple[str, str]]
) -> None:
    """
    Verify that all required packages are installed and can be imported.
    Uses pkg_resources to check installation and then attempts to import each package.
    """
    kernel_id: str = create_kernel(jupyter_session)
    ws: websocket.WebSocket = create_ws_connection(kernel_id)

    try:
        # First check installed packages
        check_installed_code: str = """
import pkg_resources
packages = [d.project_name for d in pkg_resources.working_set]
print("PACKAGES:", packages)
"""
        outputs: list[dict[str, Any]] = execute_code_ws(
            ws, check_installed_code, "packages_test"
        )
        all_text: str = "\n".join(str(o) for o in outputs)
        match = re.search(r"PACKAGES:\s*(\[.*\])", all_text)
        assert match, "Failed to capture installed packages."

        installed_packages: list[str] = ast.literal_eval(match.group(1))

        # Then test importing each package
        for package_name, import_code in required_packages:
            # First verify package is installed
            assert any(
                pkg.lower() == package_name.lower()
                or pkg.lower().startswith(f"{package_name.lower()}-")
                for pkg in installed_packages
            ), f"Package '{package_name}' should be installed."

            # Properly indent the import code
            indented_code: str = "\n    ".join(import_code.strip().split("\n"))

            # Then try importing it
            test_code: str = (
                "try:\n"
                f"    {indented_code}\n"
                f'    print(f"SUCCESS:{package_name}")\n'
                "except Exception as e:\n"
                f'    print(f"ERROR:{package_name}:{{str(e)}}")\n'
            )

            outputs = execute_code_ws(ws, test_code, f"import_{package_name}_test")
            combined: str = "\n".join(str(o) for o in outputs)

            assert f"SUCCESS:{package_name}" in combined, (
                f"Failed to import {package_name}. Output: {combined}"
            )

    finally:
        ws.close()


def test_user_permissions(jupyter_session: requests.Session) -> None:
    """
    Check that the user has valid UID, GID, and HOME directory inside the container.
    """
    kernel_id: str = create_kernel(jupyter_session)
    ws: websocket.WebSocket = create_ws_connection(kernel_id)
    code: str = """
import os
print(f"User ID: {os.getuid()}")
print(f"Group ID: {os.getgid()}")
print(f"Home directory: {os.environ.get('HOME')}")
"""
    try:
        outputs: list[dict[str, Any]] = execute_code_ws(ws, code, "permissions_test")
    finally:
        ws.close()

    # Extract data from the output
    output_str: str = "\n".join(str(o) for o in outputs)
    uid_match = re.search(r"User ID:\s*(\d+)", output_str)
    gid_match = re.search(r"Group ID:\s*(\d+)", output_str)
    home_match = re.search(r"Home directory:\s*([^\s]+)", output_str)

    uid: int = int(uid_match.group(1)) if uid_match else None  # type: ignore
    gid: int = int(gid_match.group(1)) if gid_match else None  # type: ignore
    home: str = home_match.group(1) if home_match else None  # type: ignore

    assert uid is not None and uid > 0, f"Invalid UID: {uid}"
    assert gid is not None and gid > 0, f"Invalid GID: {gid}"
    assert home and home.startswith("/home/"), f"Unexpected HOME: {home}"


def test_pip_install_and_import(jupyter_session: requests.Session) -> None:
    """
    Test that we can:
    1. Confirm a package is not initially available
    2. Install it using pip
    3. Successfully import the package after installation
    """
    kernel_id: str = create_kernel(jupyter_session)
    ws: websocket.WebSocket = create_ws_connection(kernel_id)

    try:
        # First verify the module is not available
        code_check_initial: str = """
try:
    from cairosvg import svg2png
    print("UNEXPECTED:Module already exists")
except ImportError as e:
    print("EXPECTED:ImportError occurred")
"""
        outputs: list[dict[str, Any]] = execute_code_ws(
            ws, code_check_initial, "check_initial"
        )
        combined: str = "\n".join(str(o) for o in outputs)
        assert "EXPECTED:ImportError occurred" in combined, (
            "cairosvg should not be available initially"
        )

        # Install the package
        code_install: str = """
import sys
import subprocess
subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'cairosvg'])
print("SUCCESS:Package installed")
"""
        outputs = execute_code_ws(ws, code_install, "install_package")
        combined = "\n".join(str(o) for o in outputs)
        assert "SUCCESS:Package installed" in combined, "Package installation failed"

        # Verify we can now import and use it
        code_verify: str = """
try:
    from cairosvg import svg2png
    print("SUCCESS:Module imported successfully")
except ImportError as e:
    print(f"ERROR:Import failed after installation: {str(e)}")
"""
        outputs = execute_code_ws(ws, code_verify, "verify_import")
        combined = "\n".join(str(o) for o in outputs)
        assert "SUCCESS:Module imported successfully" in combined, (
            "Module import failed after installation"
        )

    finally:
        ws.close()


def test_package_uninstall_permissions(jupyter_session: requests.Session) -> None:
    """
    Test that package uninstallation works correctly.
    This test verifies that we can successfully uninstall the gametime_protos package.
    """
    kernel_id: str = create_kernel(jupyter_session)
    ws: websocket.WebSocket = create_ws_connection(kernel_id)

    try:
        # Attempt to uninstall the package
        code: str = """
import sys
import subprocess
try:
    result = subprocess.run(
        [sys.executable, '-m', 'pip', 'uninstall', '-y', 'gametime_protos'],
        capture_output=True,
        text=True,
        check=True
    )
    print("SUCCESS:Uninstall completed")
except subprocess.CalledProcessError as e:
    print(f"ERROR:Uninstall failed with return code {e.returncode}")
    print(f"STDERR:{e.stderr}")
"""
        outputs: list[dict[str, Any]] = execute_code_ws(ws, code, "uninstall_test")

        # Combine all outputs into a single string for analysis
        combined_output: str = "\n".join(str(o) for o in outputs)

        # Check if uninstall was successful
        assert "SUCCESS:Uninstall completed" in combined_output, (
            f"Package uninstall failed: {combined_output}"
        )

        # Verify the package is no longer present
        verify_code: str = """
import pkg_resources
try:
    pkg_resources.get_distribution('gametime_protos')
    print("ERROR:Package still installed")
except pkg_resources.DistributionNotFound:
    print("SUCCESS:Package successfully uninstalled")
"""
        verify_outputs: list[dict[str, Any]] = execute_code_ws(
            ws, verify_code, "verify_uninstall"
        )
        verify_combined: str = "\n".join(str(o) for o in verify_outputs)
        assert "SUCCESS:Package successfully uninstalled" in verify_combined, (
            "Package should not be installed after successful uninstall"
        )

    finally:
        ws.close()
