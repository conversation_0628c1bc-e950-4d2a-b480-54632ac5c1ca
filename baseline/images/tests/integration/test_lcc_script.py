import os
import time
import requests
import docker
import pytest


@pytest.fixture(scope="function")
def jupyter_container(docker_client):
    container_name = "lcc_test"
    try:
        existing = docker_client.containers.get(container_name)
        existing.stop()
        existing.remove()
    except docker.errors.NotFound:
        pass

    current_dir = os.getcwd()
    lcc_path = os.path.join(current_dir, "startup", "lcc.sh")

    # Gather AWS credentials and other environment variables from the host
    env_vars = {
        "AWS_ACCESS_KEY_ID": os.environ.get("AWS_ACCESS_KEY_ID", ""),
        "AWS_SECRET_ACCESS_KEY": os.environ.get("AWS_SECRET_ACCESS_KEY", ""),
        "AWS_SESSION_TOKEN": os.environ.get("AWS_SESSION_TOKEN", ""),
        "AWS_DEFAULT_REGION": os.environ.get("AWS_DEFAULT_REGION", "us-west-1"),
        "aws_region": os.environ.get("AWS_DEFAULT_REGION", "us-west-1"),
        "environment": os.environ.get("ENVIRONMENT", "staging"),
        "GITHUB_USERNAME": os.environ.get("GITHUB_USERNAME", "gt-mlp-machine"),
    }

    # Let Docker assign a random host port for container's port 8888.
    container = docker_client.containers.run(
        "baseline:latest",
        name=container_name,
        detach=True,
        ports={"8888/tcp": None},
        volumes={lcc_path: {"bind": "/lcc.sh", "mode": "ro"}},
        environment=env_vars,
    )
    # Wait for the container to start up
    time.sleep(5)
    container.reload()
    yield container
    container.stop()
    container.remove()


def test_lcc_script_integration(jupyter_container):
    container = jupyter_container

    # Execute the lcc.sh script inside the container.
    exit_code, output = container.exec_run("bash /lcc.sh")
    assert exit_code == 0, f"lcc.sh script failed with output: {output.decode()}"

    # Verify that the notebooks repository was cloned.
    exit_code, _ = container.exec_run("test -d /home/<USER>/notebooks")
    assert exit_code == 0, "Notebooks repository was not cloned by lcc.sh"

    # Verify that Git global config was set correctly.
    exit_code, output = container.exec_run("git config --global user.name")
    git_user = output.decode().strip()
    assert git_user == "gt-mlp-machine", f"Unexpected git user: {git_user}"

    # Retrieve the host port Docker assigned to container's port 8888
    container.reload()
    ports = container.attrs["NetworkSettings"]["Ports"]
    host_port = ports["8888/tcp"][0]["HostPort"]

    # Verify that the Jupyter Server is still running.
    try:
        response = requests.get(f"http://localhost:{host_port}/lab", timeout=5)
        assert response.status_code == 200, "Jupyter Server did not respond as expected"
    except Exception as e:
        pytest.fail(f"Jupyter Server endpoint not reachable: {e}")
