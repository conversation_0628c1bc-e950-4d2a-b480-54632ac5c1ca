import pytest
import torch


@pytest.mark.skipif(
    not torch.cuda.is_available(), reason="CUDA is not available on this system"
)
def test_torch_training_with_cuda():
    device = torch.device("cuda")
    # Define a simple linear model and move it to GPU
    model = torch.nn.Linear(10, 1).to(device)
    criterion = torch.nn.MSELoss()
    optimizer = torch.optim.SGD(model.parameters(), lr=0.01)

    # Generate dummy data directly on the GPU
    inputs = torch.randn(100, 10, device=device)
    targets = torch.randn(100, 1, device=device)

    model.train()
    # Run a short training loop and capture the loss
    initial_loss = None
    for _ in range(10):
        optimizer.zero_grad()
        outputs = model(inputs)
        loss = criterion(outputs, targets)
        loss.backward()
        optimizer.step()
        if initial_loss is None:
            initial_loss = loss.item()

    final_loss = loss.item()
    # Ensure that the training loop reduces the loss
    assert final_loss < initial_loss, (
        f"Final loss ({final_loss}) should be less than initial loss ({initial_loss})"
    )
