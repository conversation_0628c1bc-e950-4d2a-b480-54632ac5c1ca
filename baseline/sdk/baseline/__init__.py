import json
import tarfile
from collections.abc import Mapping
from pathlib import Path
from tempfile import NamedTemporaryFile
from tempfile import TemporaryDirectory

import boto3

from baseline.config import config
from baseline.serializers import deserialize
from baseline.serializers import serialize
import os


_SECRET_PREFIX = "baseline"  # nosec


class _S3:
    @staticmethod
    def _make_bucket_resource():
        resource = boto3.resource("s3", **config.s3.client_settings)
        return resource.Bucket(config.s3.bucket)

    @staticmethod
    def _ref_to_path(ref):
        family, dt, token = ref.rsplit(sep="-", maxsplit=2)
        partial_ref = f"{dt}-{token}"
        return "/".join(["jobs", family, partial_ref, "assets.tar.gz"])

    @staticmethod
    def pull(ref, path):
        bucket = _S3._make_bucket_resource()
        source = _S3._ref_to_path(ref)
        bucket.download_file(source, path)

    @staticmethod
    def push(path, ref):
        bucket = _S3._make_bucket_resource()
        destination = _S3._ref_to_path(ref)
        bucket.upload_file(path, destination)


class Assets(Mapping):
    """
    Mapping class for accessing the assets from a baseline job.

    Example:

    import baseline

    assets = Assets.pull("family-20220520T010745-j2kl5l7l")
    model = assets["model"]
    """

    def __init__(self, ref, raw):
        self.ref = ref
        self.raw = raw

    @classmethod
    def pull(cls, ref, only=None):
        raw = {}
        with NamedTemporaryFile() as tempfile, TemporaryDirectory() as tmpdir:
            tmpdir_path = Path(tmpdir)
            _S3.pull(ref, tempfile.name)
            with tarfile.open(tempfile.name, "r:gz") as tar:
                tar.extractall(tmpdir)  # nosec
            with (tmpdir_path / "manifest.json").open("r") as f:
                manifest = json.load(f)
            for name, info in manifest.items():
                if only is None or name in only:
                    path = str(tmpdir_path / name)
                    raw[name] = deserialize(info["kind"], path, info["meta"])

        return cls(ref, raw)

    @classmethod
    def push(cls, ref, raw):
        manifest = {}
        with TemporaryDirectory() as tempdir:
            tempdir_path = Path(tempdir)
            for name, asset in raw.items():
                filename = str(tempdir_path / name)
                kind, meta = serialize(asset, filename)
                manifest[name] = {"kind": kind, "meta": meta}
            with (tempdir_path / "manifest.json").open("w") as f:
                json.dump(manifest, f)
            with NamedTemporaryFile(suffix=".tar.gz") as tempfile:
                with tarfile.open(tempfile.name, "w:gz") as tar:
                    tar.add(tempdir_path, arcname="/")
                _S3.push(tempfile.name, ref)

        return manifest

    def __iter__(self):
        return iter(self.raw)

    def __len__(self):
        return len(self.raw)

    def __getitem__(self, key):
        return self.raw[key]


class _Secrets:
    """
    Read-only dict-like class used to access secrets from within a baseline
    notebook. Secrets are fetched over the network on first access and then
    cached for future use.

    Example:

    import baseline

    my_secret = baseline.secrets["MY_SECRET"]
    """

    def __init__(self):
        self._cache = {}
        self._client = None

    def __getitem__(self, key):
        if self._client is None:
            self._client = boto3.client(
                "secretsmanager", **config.secretsmanager.client_settings
            )

        if key not in self._cache:
            try:
                id = f"{_SECRET_PREFIX}/{config.environment}/{key}"
                response = self._client.get_secret_value(SecretId=id)
                self._cache[key] = response["SecretString"]
            except self._client.exceptions.ResourceNotFoundException:
                raise KeyError(key)

        return self._cache[key]


def get_base_dir() -> Path:
    """Get the base directory for Baseline jobs and Studio sessions.

    Determines the base directory based on BASELINE_EFS_MOUNT_PATH environment variable:
    - If set: Uses the specified EFS mount path for job family data
    - If not set: Defaults to /home/<USER>

    Returns:
        Path: Base directory (EFS mount path or /home/<USER>
    """
    base_path = Path(os.environ.get("BASELINE_EFS_MOUNT_PATH", "/home/<USER>"))
    base_path.mkdir(
        parents=True, exist_ok=True
    ) if "BASELINE_EFS_MOUNT_PATH" in os.environ else None
    return base_path


def transform_snowflake_url(
    url: str,
    *,  # Force keyword arguments to avoid positional arguments
    warehouse: str | None = None,
    database: str | None = None,
    schema: str | None = None,
    role: str | None = None,
) -> str:
    """
    Transform a Snowflake connection URL by selectively updating its components.

    This function provides a robust way to modify Snowflake connection parameters
    while preserving the original URL's structure and other parameters.

    Args:
        url (str): The original Snowflake connection URL
        warehouse (str, optional): New warehouse name to replace the existing one
        database (str, optional): New database name to replace the existing one
        schema (str, optional): New schema name to replace the existing one
        role (str, optional): New role name to replace the existing one

    Returns:
        str: A new Snowflake connection URL with specified parameters updated

    Raises:
        ValueError: If the provided URL is not a valid Snowflake connection URL

    Examples:
        >>> original_url = "snowflake://SVC_BASELINE@qeb21742?warehouse=BASELINE_S_WH&database=GAMETIME&schema=BASELINE&role=BASELINE_ROLE"
        >>> transform_snowflake_url(original_url, warehouse="BASELINE_L_WH")
        'snowflake://SVC_BASELINE@qeb21742?warehouse=BASELINE_L_WH&database=GAMETIME&schema=BASELINE&role=BASELINE_ROLE'
    """
    from urllib.parse import urlparse, urlencode, urlunparse, parse_qs

    # Parse the URL
    parsed_url = urlparse(url)

    # Validate Snowflake URL scheme
    if parsed_url.scheme != "snowflake":
        raise ValueError(
            f"Invalid URL scheme. Expected 'snowflake', got '{parsed_url.scheme}'"
        )

    # Parse existing query parameters
    query_params = parse_qs(parsed_url.query, keep_blank_values=True)

    # Flatten query parameters (parse_qs returns lists)
    query_params = {k: v[0] for k, v in query_params.items()}

    # Update parameters if provided
    if warehouse is not None:
        query_params["warehouse"] = warehouse
    if database is not None:
        query_params["database"] = database
    if schema is not None:
        query_params["schema"] = schema
    if role is not None:
        query_params["role"] = role

    # Reconstruct the URL
    updated_query = urlencode(query_params)
    updated_url = urlunparse(
        (
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            updated_query,
            parsed_url.fragment,
        )
    )

    return updated_url


BASE_DIR = get_base_dir()
secrets = _Secrets()
