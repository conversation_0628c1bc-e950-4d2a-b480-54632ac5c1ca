from dataclasses import dataclass

from mlutils.dataclasses import from_env
from mlutils.utils import remove_none_values


@dataclass(kw_only=True)
class AWSBaseConfig:
    endpoint_url: str | None = None
    access_key_id: str | None = None
    secret_access_key: str | None = None

    @property
    def client_settings(self):
        return remove_none_values(
            {
                "endpoint_url": self.endpoint_url,
                "aws_access_key_id": self.access_key_id,
                "aws_secret_access_key": self.secret_access_key,
            }
        )


@dataclass(kw_only=True)
class S3Config(AWSBaseConfig):
    bucket: str


@dataclass(kw_only=True)
class SecretsManagerConfig(AWSBaseConfig):
    pass


@dataclass
class GitHubConfig:
    base_url: str | None = None
    access_token_arn: str | None = None

    @property
    def client_settings(self):
        return remove_none_values({"base_url": self.base_url})


@dataclass
class Config:
    s3: S3Config
    secretsmanager: SecretsManagerConfig
    github: GitHubConfig
    environment: str
    url: str | None = None
    credentials_arn: str | None = None
    job_ref: str | None = None


config = from_env(Config, prefixes=["baseline"])
