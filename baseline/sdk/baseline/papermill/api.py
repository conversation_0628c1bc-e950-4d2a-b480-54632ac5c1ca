from contextlib import contextmanager

import boto3
import grpc
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import JobsServiceStub

from baseline.config import config
from mlutils.grpc.sync import HeaderAdderClientInterceptor


@contextmanager
def make_stub():
    secretsmanager_client = boto3.client(
        "secretsmanager", **config.secretsmanager.client_settings
    )
    response = secretsmanager_client.get_secret_value(SecretId=config.credentials_arn)
    credentials = response["SecretString"]
    interceptor = HeaderAdderClientInterceptor({"Authorization": credentials})
    credentials = grpc.ssl_channel_credentials()
    with grpc.secure_channel(config.url, credentials) as channel:
        intercept_channel = grpc.intercept_channel(channel, interceptor)
        yield JobsServiceStub(intercept_channel)
