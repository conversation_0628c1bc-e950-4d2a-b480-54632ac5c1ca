import ast
import logging
import textwrap

import nbformat
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFinalizeRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceInitializeRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobStatus
from papermill.engines import NBClientEngine

from baseline.config import config
from baseline.papermill.api import make_stub


_ASSETS_TAG = "assets"
_EVALUATION_TAG = "evaluation"
_PUSH_ASSETS_TAG = "baseline_push_assets"
_PUSH_ASSETS_SOURCE = f"""
    # THIS CELL WAS INJECTED AUTOMATICALLY BY BASELINE
    from baseline import Assets
    Assets.push("{config.job_ref}", Out[sorted(Out.keys())[-1]])
"""


logger = logging.Logger(__name__)


def _cell_output_to_dict(cell):
    d = {}

    for output in cell.outputs:
        if output.output_type == "execute_result":
            for _, v in output.data.items():
                d.update(ast.literal_eval(v))

    # if output is printed to stdout
    if not d:
        for output in cell.outputs:
            if output.output_type == "stream" and output.name == "stdout":
                d.update(ast.literal_eval(output.text))

    return d


def _check_failures(cells):
    for cell in cells:
        if cell.metadata.papermill.status == "failed":
            for output in cell.outputs:
                if output.output_type == "error":
                    return True, output.evalue
            return True, None
    return False, None


def _inject_push_assets_cell(cells):
    new_cells = []
    injected = False
    for cell in cells:
        new_cells.append(cell)
        tags = cell.metadata.get("tags", [])
        if (not injected) and (_ASSETS_TAG in tags):
            push_assets_source = textwrap.dedent(_PUSH_ASSETS_SOURCE)
            push_assets_cell = nbformat.v4.new_code_cell(source=push_assets_source)
            push_assets_cell.metadata["tags"] = [_PUSH_ASSETS_TAG]
            push_assets_cell.metadata["papermill"] = {
                "exception": None,
                "start_time": None,
                "end_time": None,
                "duration": None,
                "status": "pending",
            }
            new_cells.append(push_assets_cell)
            injected = True
    return new_cells


class BaselineEngine(NBClientEngine):
    @classmethod
    def execute_managed_notebook(cls, nb_man, kernel_name, **kwargs):
        with make_stub() as stub:
            initialize_request = JobsServiceInitializeRequest(ref=config.job_ref)
            stub.Initialize(initialize_request)
            try:
                nb_man.nb.cells = _inject_push_assets_cell(nb_man.nb.cells)
                super().execute_managed_notebook(nb_man, kernel_name, **kwargs)

                evaluation, manifest = None, None
                for cell in reversed(nb_man.nb.cells):
                    tags = cell.metadata.get("tags", [])
                    if not manifest and _PUSH_ASSETS_TAG in tags:
                        manifest = _cell_output_to_dict(cell)
                    if not evaluation and _EVALUATION_TAG in tags:
                        evaluation = _cell_output_to_dict(cell)
                    if evaluation and manifest:
                        break

                failed, reason = _check_failures(nb_man.nb.cells)
                status = (
                    JobStatus.JOB_STATUS_FAILED
                    if failed
                    else JobStatus.JOB_STATUS_SUCCEEDED
                )
                finalize_request = JobsServiceFinalizeRequest(
                    ref=config.job_ref, status=status, status_reason=reason
                )
                if manifest:
                    finalize_request.asset_manifest.update(manifest)
                if evaluation:
                    finalize_request.evaluation_metrics.update(evaluation)

                stub.Finalize(finalize_request)
            except Exception as e:
                logger.exception("failed to execute notebook")
                finalize_request = JobsServiceFinalizeRequest(
                    ref=config.job_ref,
                    status=JobStatus.JOB_STATUS_FAILED,
                    status_reason=str(e),
                )
                stub.Finalize(finalize_request)
                raise
