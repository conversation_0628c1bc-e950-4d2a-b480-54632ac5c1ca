import boto3
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFinalizeRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobStatus
from github import Auth
from github import Github
from papermill.exceptions import PapermillException

from baseline.config import config
from baseline.papermill.api import make_stub


def _make_github_client():
    secretsmanager_client = boto3.client(
        "secretsmanager", **config.secretsmanager.client_settings
    )
    response = secretsmanager_client.get_secret_value(
        SecretId=config.github.access_token_arn
    )
    token = response["SecretString"]
    auth = Auth.Token(token)
    return Github(auth=auth, **config.github.client_settings)


class BaselineGithubHandler:
    @classmethod
    def read(cls, path):
        try:
            splits = path.split("/")
            org_id = splits[3]
            repo_id = splits[4]
            ref_id = splits[6]
            sub_path = "/".join(splits[7:])
            github_client = _make_github_client()
            repo = github_client.get_repo(org_id + "/" + repo_id)
            content = repo.get_contents(sub_path, ref=ref_id)
            return content.decoded_content
        except Exception as e:
            request = JobsServiceFinalizeRequest(
                ref=config.job_ref,
                status=JobStatus.JOB_STATUS_FAILED,
                status_reason=str(e),
            )
            with make_stub() as stub:
                stub.Finalize(request)
            raise

    @classmethod
    def listdir(cls, path):
        raise PapermillException("listdir is not supported by BaselineGithubHandler")

    @classmethod
    def write(cls, buf, path):
        raise PapermillException("write is not supported by BaselineGithubHandler")

    @classmethod
    def pretty_path(cls, path):
        return path
