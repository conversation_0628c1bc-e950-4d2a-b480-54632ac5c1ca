import baseline.serializers.numpy  # noqa: F401
import baseline.serializers.pandas  # noqa: F401
import baseline.serializers.python  # noqa: F401
import baseline.serializers.sklearn  # noqa: F401
import baseline.serializers.torch  # noqa: F401
from baseline.exceptions import SerializerNotFoundError
from baseline.serializers.base import Serializer


def serialize(asset, path):
    for kind, serializer in Serializer.registry.items():
        if serializer.detect(asset):
            return kind, serializer.serialize(asset, path)
    raise SerializerNotFoundError("Matching serializer not found")


def deserialize(kind, path, meta):
    if kind not in Serializer.registry:
        raise SerializerNotFoundError(f"Matching serializer not found for {kind}")
    return Serializer.registry[kind].deserialize(path, meta)


__all__ = [
    "SerializerNotFoundError",
    "Serializer",
    "serialize",
    "deserialize",
]
