from abc import ABC
from abc import abstractmethod


class Serializer(ABC):
    registry = {}

    @classmethod
    def register(cls, kind, serializer):
        cls.registry[kind] = serializer

    @classmethod
    def unregister(cls, kind):
        cls.registry.pop(kind, None)

    @staticmethod
    @abstractmethod
    def detect(asset):
        pass

    @staticmethod
    @abstractmethod
    def serialize(asset, path):
        pass

    @staticmethod
    @abstractmethod
    def deserialize(path, meta):
        pass
