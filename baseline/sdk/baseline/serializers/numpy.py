from baseline.serializers.base import Serializer


class ArraySerializer(Serializer):
    @staticmethod
    def detect(asset):
        try:
            import numpy as np
        except ImportError:
            return False
        return isinstance(asset, np.ndarray)

    @staticmethod
    def serialize(asset, path):
        import numpy as np

        with open(path, "wb") as f:
            return np.save(f, asset)

    @staticmethod
    def deserialize(path, meta):
        import numpy as np

        with open(path, "rb") as f:
            return np.load(f, allow_pickle=True)


Serializer.register("numpy/array", ArraySerializer)
