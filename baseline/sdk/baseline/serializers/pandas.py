from baseline.serializers.base import Serializer


COMPRESSION = "gzip"


class NDFrameSerializer(Serializer):
    @staticmethod
    def serialize(asset, path):
        asset.to_pickle(path, compression=COMPRESSION)
        return {"compression": COMPRESSION}

    @staticmethod
    def deserialize(path, meta):
        import pandas as pd

        return pd.read_pickle(path, **meta)  # nosec

    @staticmethod
    def detect(asset):
        try:
            import pandas as pd
        except ImportError:
            return False
        return isinstance(asset, pd.core.generic.NDFrame)


Serializer.register("pandas/ndframe", NDFrameSerializer)
