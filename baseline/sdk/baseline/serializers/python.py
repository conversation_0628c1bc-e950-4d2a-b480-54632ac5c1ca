import pickle  # nosec

from baseline.serializers.base import Serializer


BUILTIN_TYPES = [list, tuple, set, dict, str, float, int, bool]


class BuiltinSerializer(Serializer):
    @staticmethod
    def detect(asset):
        return type(asset) in BUILTIN_TYPES

    @staticmethod
    def serialize(asset, path):
        with open(path, "wb") as f:
            pickle.dump(asset, f)

    @staticmethod
    def deserialize(path, meta):
        with open(path, "rb") as f:
            return pickle.load(f)  # nosec


Serializer.register("python/builtin", BuiltinSerializer)
