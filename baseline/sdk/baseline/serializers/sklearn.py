import joblib

from baseline.serializers.base import Serializer


class EstimatorSerializer(Serializer):
    @staticmethod
    def detect(asset):
        try:
            import sklearn
        except ImportError:
            return False
        return isinstance(asset, sklearn.base.BaseEstimator)

    @staticmethod
    def serialize(asset, path):
        joblib.dump(asset, path)

    @staticmethod
    def deserialize(path, meta):
        return joblib.load(path)


Serializer.register("sklearn/estimator", EstimatorSerializer)
