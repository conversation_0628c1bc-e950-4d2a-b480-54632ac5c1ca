from baseline.serializers.base import Serializer


class TensorSerializer(Serializer):
    @staticmethod
    def detect(asset):
        try:
            import torch
        except ImportError:
            return False
        return isinstance(asset, torch.Tensor)

    @staticmethod
    def serialize(asset, path):
        import torch

        asset.to("cpu")
        torch.save(asset, path, pickle_protocol=2)  # nosec B614

    @staticmethod
    def deserialize(path, meta):
        import torch
        import os

        if not os.path.isfile(path):
            raise ValueError(f"Path {path} is not a valid file")

        return torch.load(path, weights_only=True, map_location="cpu")  # nosec B614


class TorchScriptSerializer(Serializer):
    @staticmethod
    def detect(asset):
        try:
            import torch
        except ImportError:
            return False
        return isinstance(asset, torch.nn.Module)

    @staticmethod
    def serialize(asset, path):
        import torch

        asset.to("cpu")
        model_scripted = torch.jit.script(asset)
        model_scripted.save(path)

    @staticmethod
    def deserialize(path, meta):
        import torch
        import os

        if not os.path.isfile(path):
            raise ValueError(f"Path {path} is not a valid file")

        return torch.jit.load(path, map_location="cpu")  # nosec B614


Serializer.register("torch/tensor", TensorSerializer)
Serializer.register("torch/tsmodule", TorchScriptSerializer)
