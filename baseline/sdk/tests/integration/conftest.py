import os
from concurrent.futures import ThreadPoolExecutor

import boto3
import grpc
import pytest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFinalizeResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceInitializeResponse
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import (
    add_JobsServiceServicer_to_server,
)
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import JobsServiceServicer
from testcontainers.minio import MinioContainer


@pytest.fixture(scope="session")
def s3():
    from baseline.config import config

    with MinioContainer() as minio:
        minio_config = minio.get_config()
        config.s3.bucket = "fake"
        config.s3.endpoint_url = f"http://{minio_config['endpoint']}"
        config.s3.access_key_id = minio_config["access_key"]
        config.s3.secret_access_key = minio_config["secret_key"]
        os.environ["BASELINE_S3_ENDPOINT_URL"] = config.s3.endpoint_url
        os.environ["BASELINE_S3_ACCESS_KEY_ID"] = config.s3.access_key_id
        os.environ["BASELINE_S3_SECRET_ACCESS_KEY"] = config.s3.secret_access_key
        client = boto3.client("s3", **config.s3.client_settings)
        client.create_bucket(Bucket=config.s3.bucket)
        yield client


@pytest.fixture
def jobs_servicer():
    from baseline.config import config

    class Servicer(JobsServiceServicer):
        def __init__(self):
            super().__init__()
            self.requests = {}

        def _push_request(self, name, request):
            if name not in self.requests:
                self.requests[name] = []
            self.requests[name].append(request)

        def Initialize(self, request, context):
            self._push_request("Initialize", request)
            return JobsServiceInitializeResponse()

        def Finalize(self, request, context):
            self._push_request("Finalize", request)
            return JobsServiceFinalizeResponse()

    server = grpc.server(ThreadPoolExecutor(max_workers=2))
    servicer = Servicer()
    add_JobsServiceServicer_to_server(servicer, server)
    port = server.add_insecure_port("[::]:0")
    server.start()
    config.url = f"localhost:{port}"

    try:
        yield servicer
    finally:
        server.stop(None)
