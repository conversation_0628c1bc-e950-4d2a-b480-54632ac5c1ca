{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["test = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = int(test) / 0\n", "results = [result]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["assets"]}, "outputs": [], "source": ["{\"results\": results}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["evaluation"]}, "outputs": [], "source": ["{\"length\": len(results)}"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}