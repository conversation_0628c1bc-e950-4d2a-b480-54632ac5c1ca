import json
import os
from base64 import b64encode
from pathlib import Path

import pytest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobStatus

from baseline import Assets
from baseline.config import config
from mlutils.grpc import proto_to_dict

import papermill as pm
from papermill.engines import papermill_engines
from papermill.iorw import papermill_io
from baseline.papermill.engines import BaselineEngine
from baseline.papermill.handlers import BaselineGithubHandler


family = "test"
partial_ref = "20240627T010203-ld42kf9o"
job_ref = f"{family}-{partial_ref}"
output_key = f"jobs/{family}/{partial_ref}/output.ipynb"
# this needs to be set before import papermill modules
config.job_ref = job_ref


engine_name = "baseline_engine"
papermill_engines.register(engine_name, BaselineEngine)
papermill_io.register("https://github.com/", BaselineGithubHandler)


def execute_notebook(httpserver, path):
    os.environ["BOTO3_ENDPOINT_URL"] = config.s3.endpoint_url
    os.environ["AWS_ACCESS_KEY_ID"] = config.s3.access_key_id
    os.environ["AWS_SECRET_ACCESS_KEY"] = config.s3.secret_access_key
    bytes = path.read_bytes()
    content = b64encode(bytes)

    access_token_arn = "fake_access_token_arn"  # nosec
    access_token = "fake_token"  # nosec
    config.github.access_token_arn = access_token_arn
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        headers={"x-amz-target": "secretsmanager.GetSecretValue"},
        json={"SecretId": access_token_arn},
    ).respond_with_json({"SecretString": access_token}, status=200)

    httpserver.expect_oneshot_request(
        "/repos/gametimesf/notebooks", method="GET"
    ).respond_with_json(
        {
            "url": f"http://localhost:{os.environ['PYTEST_HTTPSERVER_PORT']}/repos/gametimesf/notebooks",
        },
        status=200,
    )
    httpserver.expect_oneshot_request(
        "/repos/gametimesf/notebooks/contents/jobs/test.ipynb",
        query_string="ref=abc123",
        method="GET",
    ).respond_with_json(
        {"encoding": "base64", "content": content.decode("UTF-8")}, status=200
    )

    credentials_arn = "fake_credentials_arn"
    credentials = "fake_token"
    config.credentials_arn = credentials_arn
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        headers={"x-amz-target": "secretsmanager.GetSecretValue"},
        json={"SecretId": credentials_arn},
    ).respond_with_json({"SecretString": credentials}, status=200)

    input_path = "https://github.com/gametimesf/notebooks/blob/abc123/jobs/test.ipynb"
    output_path = f"s3://{config.s3.bucket}/{output_key}"
    return pm.execute_notebook(
        input_path, output_path, engine_name=engine_name, kernel_name="python3"
    )


def fetch_output(tmp_path, s3):
    destination = tmp_path / "output.ipynb"
    s3.download_file(config.s3.bucket, output_key, str(destination))
    return json.loads(destination.read_text())


def test_notebook_success(tmp_path, httpserver, s3, jobs_servicer):
    path = Path(__file__).parent / "fixtures/success.ipynb"
    execute_notebook(httpserver, path)
    assets = Assets.pull(job_ref)
    assert assets["results"] == [2]

    assert len(jobs_servicer.requests["Initialize"]) == 1
    assert len(jobs_servicer.requests["Finalize"]) == 1

    initialize_request = jobs_servicer.requests["Initialize"][0]
    assert initialize_request.ref == job_ref

    finalize_request = jobs_servicer.requests["Finalize"][0]
    assert finalize_request.status == JobStatus.JOB_STATUS_SUCCEEDED
    assert finalize_request.status_reason == ""
    assert proto_to_dict(finalize_request.evaluation_metrics) == {"length": 1}
    assert proto_to_dict(finalize_request.asset_manifest) == {
        "results": {"kind": "python/builtin", "meta": None}
    }

    nb = fetch_output(tmp_path, s3)
    assert nb["cells"][-1]["metadata"]["tags"] == ["evaluation"]


def test_notebook_failure(tmp_path, httpserver, s3, jobs_servicer):
    path = Path(__file__).parent / "fixtures/failure.ipynb"
    with pytest.raises(pm.exceptions.PapermillExecutionError):
        execute_notebook(httpserver, path)

    assert len(jobs_servicer.requests["Initialize"]) == 1
    assert len(jobs_servicer.requests["Finalize"]) == 1

    initialize_request = jobs_servicer.requests["Initialize"][0]
    assert initialize_request.ref == job_ref

    finalize_request = jobs_servicer.requests["Finalize"][0]
    assert finalize_request.status == JobStatus.JOB_STATUS_FAILED
    assert finalize_request.status_reason == "division by zero"
    assert proto_to_dict(finalize_request.evaluation_metrics) == {}
    assert proto_to_dict(finalize_request.asset_manifest) == {}

    nb = fetch_output(tmp_path, s3)
    assert nb["cells"][-1]["metadata"]["tags"] == ["evaluation"]
