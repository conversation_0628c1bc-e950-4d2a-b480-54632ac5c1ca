import boto3
import pytest
from testcontainers.minio import MinioContainer

from baseline.config import config


@pytest.fixture
def s3():
    with MinioContainer() as minio:
        minio_config = minio.get_config()
        config.s3.bucket = "fake"
        config.s3.endpoint_url = f"http://{minio_config['endpoint']}"
        config.s3.access_key_id = minio_config["access_key"]
        config.s3.secret_access_key = minio_config["secret_key"]
        client = boto3.client("s3", **config.s3.client_settings)
        client.create_bucket(Bucket=config.s3.bucket)
        yield client
