from sklearn.ensemble import RandomForestClassifier

from baseline import Assets


def test_assets(s3):
    ref = "test-20240627T010203-ld42kf9o"
    raw = {"model": RandomForestClassifier(4), "parameters": [1, 2, 3]}
    manifest = Assets.push(ref, raw)
    assert len(manifest) == 2
    assert "model" in manifest
    assert "parameters" in manifest

    pulled = Assets.pull(ref)
    assert pulled.ref == ref
    assert len(pulled) == 2
    assert pulled["model"].n_estimators == raw["model"].n_estimators
    assert pulled["parameters"] == raw["parameters"]

    selectively_pulled = Assets.pull(ref, only=["model"])

    assert selectively_pulled.ref == ref
    assert len(selectively_pulled) == 1
    assert selectively_pulled["model"].n_estimators == raw["model"].n_estimators
    assert "parameters" not in selectively_pulled
