import pytest

import baseline


def test_secrets(httpserver):
    secret_name = "test_secret"  # nosec
    secret_string = "sekret"  # nosec
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        headers={
            "x-amz-target": "secretsmanager.GetSecretValue",
        },
        json={"SecretId": f"baseline/testing/{secret_name}"},
    ).respond_with_json(
        {
            "__type": "ResourceNotFoundException",
            "Message": "Secrets Manager can't find the specified secret.",
        },
        status=400,
    )
    with pytest.raises(KeyError):
        baseline.secrets[secret_name]

    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        headers={
            "x-amz-target": "secretsmanager.GetSecretValue",
        },
        json={"SecretId": f"baseline/testing/{secret_name}"},
    ).respond_with_json(
        {
            "SecretString": secret_string,
        },
        status=200,
    )
    assert baseline.secrets[secret_name] == secret_string
