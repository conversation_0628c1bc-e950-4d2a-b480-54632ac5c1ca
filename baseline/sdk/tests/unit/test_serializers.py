import numpy as np
import pandas as pd
import pytest
from sklearn.ensemble import RandomForestClassifier
from torch import equal
from torch import tensor
from torch.nn import Linear

from baseline.exceptions import SerializerNotFoundError
from baseline.serializers import deserialize
from baseline.serializers import serialize


class Unknown:
    pass


@pytest.mark.parametrize(
    "t, asset",
    [
        ("int", 1),
        ("float", 1.5),
        ("boolean", True),
        ("string", "value"),
        ("dict", {"key": "value"}),
        ("list", [1, 2]),
        ("tuple", (1, 2)),
    ],
)
def test_python(tmp_path, t, asset):
    path = tmp_path / f"python-{t}"
    kind, meta = serialize(asset, path)
    deserialized = deserialize(kind, path, meta)
    assert deserialized == asset


@pytest.mark.parametrize(
    "t, asset",
    [("int", np.array([1, 2])), ("object", np.array(["1','2"], dtype="object"))],
)
def test_numpy(tmp_path, t, asset):
    path = tmp_path / "numpy-array"
    kind, meta = serialize(asset, path)
    deserialized = deserialize(kind, path, meta)
    assert deserialized.all() == asset.all()


def test_pytorch_module(tmp_path):
    asset = Linear(4, 2)
    path = tmp_path / "pytorch-module"
    kind, meta = serialize(asset, path)
    deserialized = deserialize(kind, path, meta)
    assert deserialized.in_features == asset.in_features
    assert deserialized.out_features == asset.out_features


def test_pytorch_tensor(tmp_path):
    asset = tensor([[1.0, -1.0], [1.0, -1.0]])
    path = tmp_path / "pytorch-tensor"
    kind, meta = serialize(asset, path)
    deserialized = deserialize(kind, path, meta)
    assert equal(deserialized, asset)


def test_sklearn_estimator(tmp_path):
    asset = RandomForestClassifier(8)
    path = tmp_path / "sklearn-estimator"
    kind, meta = serialize(asset, path)
    deserialized = deserialize(kind, path, meta)
    assert deserialized.n_estimators == asset.n_estimators


def test_pandas_data_frame(tmp_path):
    asset = pd.DataFrame({"a": [1]})
    path = tmp_path / "pandas-dataframe"
    kind, meta = serialize(asset, path)
    deserialized = deserialize(kind, path, meta)
    assert deserialized["a"].iloc[0] == 1


def test_pandas_series(tmp_path):
    asset = pd.Series([1])
    path = tmp_path / "pandas-series"
    kind, meta = serialize(asset, path)
    deserialized = deserialize(kind, path, meta)
    assert deserialized.iloc[0] == 1


def test_unknown_serialize(tmp_path):
    asset = Unknown()
    path = tmp_path / "unknown"
    with pytest.raises(SerializerNotFoundError):
        serialize(asset, path)


def test_unknown_deserialize(tmp_path):
    path = tmp_path / "unknown"
    with pytest.raises(SerializerNotFoundError):
        deserialize("unknown/unknown", path, None)
