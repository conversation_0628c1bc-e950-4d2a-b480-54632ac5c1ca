import pytest
from baseline import transform_snowflake_url


def test_warehouse_update():
    """
    Verify that warehouse parameter can be successfully updated.
    """
    original_url = "snowflake://SVC_BASELINE@qeb21742?warehouse=BASELINE_S_WH&database=GAMETIME&schema=BASELINE&role=BASELINE_ROLE"

    updated_url = transform_snowflake_url(original_url, warehouse="BASELINE_L_WH")

    assert "warehouse=BASELINE_L_WH" in updated_url
    assert "database=GAMETIME" in updated_url
    assert "schema=BASELINE" in updated_url
    assert "role=BASELINE_ROLE" in updated_url


def test_multiple_parameter_update():
    """
    Ensure multiple parameters can be updated simultaneously.
    """
    original_url = "snowflake://SVC_BASELINE@qeb21742?warehouse=BASELINE_S_WH&database=GAMETIME&schema=BASELINE&role=BASELINE_ROLE"

    updated_url = transform_snowflake_url(
        original_url, warehouse="BASELINE_L_WH", schema="PRODUCTION"
    )

    assert "warehouse=BASELINE_L_WH" in updated_url
    assert "schema=PRODUCTION" in updated_url
    assert "database=GAMETIME" in updated_url
    assert "role=BASELINE_ROLE" in updated_url


def test_invalid_url_scheme():
    """
    Confirm that non-snowflake URLs raise a ValueError.
    """
    invalid_url = "https://example.com?warehouse=BASELINE_S_WH"

    with pytest.raises(ValueError, match="Invalid URL scheme"):
        transform_snowflake_url(invalid_url, warehouse="BASELINE_L_WH")
