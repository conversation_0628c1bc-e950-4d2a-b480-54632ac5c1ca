FROM python:3.11-slim-bookworm

ENV SHELL=/bin/bash \
    PORT=8888 \
    PATH="/root/.local/bin:$PATH" \
    UV_COMPILE_BYTECODE=1 \
    UV_SYSTEM_PYTHON=1 \
    UV_LINK_MODE=copy

# Copy uv from the official distroless image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

RUN --mount=type=cache,target=/var/cache/apt \
    --mount=type=cache,target=/var/lib/apt \
    apt-get update && \
    apt-get install -y ffmpeg \
    openssh-client \
    git \
    gfortran \
    libsm6 \
    libxext6 \
    zlib1g-dev \
    libblas-dev \
    liblapack-dev \
    libjpeg-dev \
    curl \
    ca-certificates && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /opt/app

COPY uv.lock ./
COPY pyproject.toml ./

# Configure UV with CodeArtifact authentication
RUN --mount=type=secret,id=CODEARTIFACT_AUTH_TOKEN \
    --mount=type=cache,target=/root/.cache/uv \
    # Set up UV environment for CodeArtifact
    export UV_INDEX_CODEARTIFACT_URL="https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" && \
    export UV_INDEX_CODEARTIFACT_USERNAME=aws && \
    export UV_INDEX_CODEARTIFACT_PASSWORD=$(cat /run/secrets/CODEARTIFACT_AUTH_TOKEN) && \
    # Create venv and install using uv.lock configuration
    uv venv .venv && \
    uv sync --frozen --no-dev

# Remove uv.lock and pyproject.toml to save space
RUN rm uv.lock pyproject.toml

COPY src /opt/app

CMD [".venv/bin/python", "-m", "main"]
