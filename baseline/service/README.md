# baseline - service

## Testing
```shell
uv venv  # first time only
source .venv/bin/activate
uv sync
pytest tests
```

## Building
```shell
AWS_CODEARTIFACT_TOKEN=$(aws codeartifact get-authorization-token \
  --domain ml-artifacts \
  --domain-owner 728489771660 \
  --region us-west-2 \
  --query authorizationToken \
  --output text)
op read "op://ML Platform Team/gt-mlp-machine github/ssh key" > /tmp/ssh_key
DOCKER_BUILDKIT=1 docker build \
  --ssh default=/tmp/ssh_key \
  --build-arg UV_INDEX_CODEARTIFACT_USERNAME=aws \
  --build-arg UV_INDEX_CODEARTIFACT_PASSWORD="$AWS_CODEARTIFACT_TOKEN" \
  -t baseline-service .
```

## Local Development
```shell
op read "op://ML Platform Team/gt-mlp-machine github/ssh key" | docker build -t <tag> --ssh default=/dev/stdin .
aws-vault exec <profile> -- docker-compose up
```
