#!/bin/bash
set -uo pipefail

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Check required arguments
if [ -z "${1:-}" ] || [ -z "${2:-}" ]; then
    log "Error: Missing required arguments"
    echo "Usage: $0 <environment> <version> [tag-suffix]"
    echo "Example: $0 staging 1.2.3 rc1"
    exit 1
fi

# Basic setup
ENVIRONMENT=$1
VERSION=$2
TAG_SUFFIX=${3:+"$3"}
REGISTRY="728489771660.dkr.ecr.us-west-1.amazonaws.com"
TAG="$REGISTRY/baseline-service-$ENVIRONMENT:$VERSION${TAG_SUFFIX:+-$TAG_SUFFIX}"

log "Starting build process"
log "Environment: $ENVIRONMENT"
log "Version: $VERSION"
log "Tag Suffix: ${TAG_SUFFIX:-none}"
log "Image Tag: $TAG"

# Validate inputs
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
    log "Error: Environment must be 'production' or 'staging'"
    exit 1
fi

if ! [[ $VERSION =~ ^([0-9]+\.){2,}[0-9]+.*$ ]]; then
    log "Error: Version must have at least 3 period-separated numbers (e.g., 1.2.3)"
    exit 1
fi

# Login to ECR
log "Logging into ECR..."
aws ecr get-login-password --region us-west-1 | docker login --username AWS --password-stdin "$REGISTRY"

# Build and push image
log "Building image..."
DOCKER_BUILDKIT=1 op read "op://ML Platform Team/gt-mlp-machine github/ssh key" | docker build \
    --platform=linux/amd64 \
    -t "$TAG" \
    --ssh default=/dev/stdin \
    --provenance=false \
    .
log "Pushing image..."
docker push "$TAG"

log "Build process completed"
