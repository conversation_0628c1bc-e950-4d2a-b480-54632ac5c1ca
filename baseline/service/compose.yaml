services:
  app:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./src:/opt/app
    env_file: ".env"
    environment:
      AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
      GRPC_ENABLE_FORK_SUPPORT: "0"
      PORT: "8080"
      ENVIRONMENT: "staging"
      AWS_DEFAULT_REGION: "us-west-1"
      GITHUB_REPO: "gametimesf/notebooks"
      DYNAMO_AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      DYNAMO_AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      DYNAMO_AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
      S3_AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      S3_AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      S3_AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
      SECRETSMANAGER_AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      SECRETSMANAGER_AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      SECRETSMANAGER_AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
      SAGEMAKER_AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      SAGEMAKER_AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      SAGEMAKER_AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
