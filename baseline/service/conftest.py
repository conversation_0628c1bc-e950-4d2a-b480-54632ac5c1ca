import os
import sys

from mlutils.utils import get_free_tcp_port


sys.path.append("src")


httpserver_port = get_free_tcp_port()
os.environ.update(
    {
        "ENVIRONMENT": "testing",
        "DYNAMO_TABLE": "test",
        "S3_BUCKET": "test",
        "SAGEMAKER_DOMAIN_ID": "test",
        "PYTEST_HTTPSERVER_PORT": f"{httpserver_port}",
        "SECRETSMANAGER_ENDPOINT_URL": f"http://localhost:{httpserver_port}",
        "SAGEMAKER_ENDPOINT_URL": f"http://localhost:{httpserver_port}",
        "SAGEMAKER_LIFECYCLE_CONFIG_ARN": "test",
        "SAGEMAKER_CPU_IMAGE_ARN": "test",
        "SAGEMAKER_GPU_IMAGE_ARN": "test",
        "SAGEMAKER_DEFAULT_CPU_IMAGE_URL": "testcpu",
        "SAGEMAKER_DEFAULT_GPU_IMAGE_URL": "testgpu",
        "SAGEMAKER_DEFAULT_CPU_IMAGE_VERSION_ARN": "testcpu",
        "SAGEMAKER_DEFAULT_GPU_IMAGE_VERSION_ARN": "testgpu",
        "SAGEMAKER_VPC_SECURITY_GROUP_ID": "test",
        "SAGEMAKER_VPC_SUBNETS": "test1,test2",
        "SAGEMAKER_JOB_ROLE_ARN": "testtesttesttesttesttest",
        "GITHUB_REPO": "test",
        "GITHUB_BASE_URL": f"http://localhost:{httpserver_port}",
        "GITHUB_ACCESS_TOKEN": "test",
        "GITHUB_ACCESS_TOKEN_ARN": "testtesttesttesttesttest",
        "API_URL": "test",
        "API_CREDENTIALS": "test",
        "API_CREDENTIALS_ARN": "testtesttesttesttesttest",
        "AWS_DEFAULT_REGION": "us-west-1",
    }
)
