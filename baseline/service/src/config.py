from dataclasses import dataclass
from pendulum import duration

from mlutils.dataclasses import from_env
from mlutils.utils import remove_none_values


@dataclass(kw_only=True)
class AWSBaseConfig:
    endpoint_url: str | None = None
    aws_access_key_id: str | None = None
    aws_secret_access_key: str | None = None
    aws_session_token: str | None = None

    @property
    def client_settings(self):
        return remove_none_values(
            {
                "endpoint_url": self.endpoint_url,
                "aws_access_key_id": self.aws_access_key_id,
                "aws_secret_access_key": self.aws_secret_access_key,
                "aws_session_token": self.aws_session_token,
            }
        )


@dataclass(kw_only=True)
class DynamoConfig(AWSBaseConfig):
    table: str


@dataclass(kw_only=True)
class S3Config(AWSBaseConfig):
    bucket: str


@dataclass(kw_only=True)
class SagemakerConfig(AWSBaseConfig):
    domain_id: str
    lifecycle_config_arn: str
    cpu_image_arn: str
    gpu_image_arn: str
    default_cpu_image_url: str
    default_cpu_image_version_arn: str
    default_gpu_image_url: str
    default_gpu_image_version_arn: str
    vpc_security_group_id: str
    vpc_subnets: str
    job_role_arn: str
    job_volume_size_gb: int = 200
    job_max_runtime_seconds: int = int(duration(days=3).total_seconds())
    region: str = "us-west-1"


@dataclass(kw_only=True)
class SecretsManagerConfig(AWSBaseConfig):
    pass


@dataclass
class GitHubConfig:
    repo: str
    access_token: str
    access_token_arn: str
    base_url: str = "https://api.github.com"


@dataclass
class APIConfig:
    url: str
    credentials_arn: str


@dataclass(kw_only=True)
class EFSConfig(AWSBaseConfig):
    """Configuration for EFS file system used in training jobs.

    The base_path should match the EFS access point root directory (/jobs).
    SageMaker will automatically mount this at /opt/ml/input/data/workspace
    in the training container.

    Path Structure:
        EFS: /jobs/{family}
        Container: /opt/ml/input/data/workspace/{family}
    """

    file_system_id: str
    base_path: str = "/jobs"  # EFS access point root directory
    access_mode: str = "rw"


@dataclass(kw_only=True)
class CloudWatchLogsConfig(AWSBaseConfig):
    """Configuration for CloudWatch Logs service.

    Used for fetching logs from SageMaker training jobs.
    """

    log_group: str = "/aws/sagemaker/TrainingJobs"
    region: str = SagemakerConfig.region


@dataclass
class Config:
    environment: str
    api: APIConfig
    dynamo: DynamoConfig
    s3: S3Config
    sagemaker: SagemakerConfig
    secretsmanager: SecretsManagerConfig
    github: GitHubConfig
    efs: EFSConfig
    cloudwatch_logs: CloudWatchLogsConfig


config = from_env(Config)
