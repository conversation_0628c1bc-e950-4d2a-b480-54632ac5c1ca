import logging
import os
import sys

import aioboto3
from servicers.jobs import register_servicer as register_jobs_servicer
from servicers.jobs import Servicer as JobsServicer
from servicers.jupyter import register_servicer as register_jupyter_servicer
from servicers.jupyter import Servicer as JupyterServicer
from servicers.secrets import register_servicer as register_secrets_servicer
from servicers.secrets import Servicer as SecretsServicer

from mlutils.grpc import Service
from mlutils.grpc.auth import BasicAuthServerInterceptor


session = aioboto3.Session()
jobs_servicer = JobsServicer(session)
jupyter_servicer = JupyterServicer(session)
secrets_servicer = SecretsServicer(session)
servicers = [
    (jobs_servicer, register_jobs_servicer),
    (jupyter_servicer, register_jupyter_servicer),
    (secrets_servicer, register_secrets_servicer),
]
interceptors = [BasicAuthServerInterceptor(os.environ["USERS"])]
service = Service(
    servicers,
    interceptors,
    on_startup=[jobs_servicer.setup, jupyter_servicer.setup, secrets_servicer.setup],
    on_shutdown=[
        jobs_servicer.teardown,
        jupyter_servicer.teardown,
        secrets_servicer.teardown,
    ],
)


if __name__ == "__main__":
    logging.basicConfig(stream=sys.stdout, level=logging.INFO)
    service.serve(port=os.environ["PORT"])
