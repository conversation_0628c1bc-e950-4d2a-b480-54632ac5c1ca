import json
import logging
from datetime import datetime
from secrets import token_hex
import nbformat
from collections import deque

from boto3.dynamodb.conditions import Attr
from boto3.dynamodb.conditions import Key
from botocore.client import Config
from botocore.exceptions import ClientError
from config import config
from gametime_protos.mlp.baseline.v1.service_pb2 import InstanceType
from gametime_protos.mlp.baseline.v1.service_pb2 import JobListItem
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceCancelResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceDescribeResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import (
    JobsServiceFetchOutputUrlResponse,
)
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFinalizeResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceInitializeResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceListResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceRunResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JobStatus
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import (
    add_JobsServiceServicer_to_server,
)
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import JobsServiceServicer
from google.protobuf.json_format import MessageToJson
from grpc import StatusCode
from utils import instance_type_enum_to_str
from utils import instance_type_str_to_enum
from utils import is_gpu
from utils import utcnow
from utils import EFSMount

from mlutils.aiohttp import Client as HTTPClient
from mlutils.boto3 import fetch_all
from mlutils.grpc.auth import current_user
from mlutils.grpc.auth import require_permission

from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFetchLogsResponse


JOB_NAME_CHAR_LIMIT = 63
REF_DATE_FORMAT = "%Y%m%dT%H%M%S"
OUTPUT_URL_EXPIRATION_SECONDS = 60


register_servicer = add_JobsServiceServicer_to_server


class Ref:
    @staticmethod
    def generate_partial(dt):
        token = token_hex(4)
        dt = dt.strftime(REF_DATE_FORMAT)
        return f"{dt}-{token}"

    @staticmethod
    def pack(family, partial):
        return f"{family}-{partial}"

    @staticmethod
    def unpack(ref):
        family, dt, token = ref.rsplit(sep="-", maxsplit=2)
        partial = f"{dt}-{token}"
        return family, partial


class Servicer(JobsServiceServicer):
    def __init__(self, session):
        self.session = session
        self.sagemaker_client = None
        self.s3_client = None
        self.dynamo_resource = None
        self.dynamo_table = None
        self.github_client = None

    async def _fetch_git_sha(self, git_ref):
        ref = "master" if not (git_ref) else git_ref
        response = await self.github_client.request(
            "GET", f"/repos/{config.github.repo}/commits/{ref}"
        )
        return response["sha"]

    def _image_url_for(self, instance_type):
        if is_gpu(instance_type):
            return config.sagemaker.default_gpu_image_url
        else:
            return config.sagemaker.default_cpu_image_url

    def _pk_for(self, family):
        return f"job#{family}"

    def _key_prefix_for(self, ref):
        family, partial_ref = Ref.unpack(ref)
        return f"jobs/{family}/{partial_ref}"

    def _output_key_for(self, ref):
        prefix = self._key_prefix_for(ref)
        return f"{prefix}/output.ipynb"

    def _ignore_key_for(self, ref):
        prefix = self._key_prefix_for(ref)
        return f"{prefix}/ignore"

    def _job_name_for(self, ref):
        return f"baseline-{ref}"

    def _dynamo_key_from_ref(self, ref):
        family, partial_ref = Ref.unpack(ref)
        return {"pk": self._pk_for(family), "sk": partial_ref}

    async def _update_job(self, ref, attributes, allowed_statuses=None):
        key = self._dynamo_key_from_ref(ref)
        condition_expression = Attr("pk").exists() & Attr("sk").exists()
        if allowed_statuses is not None:
            condition_expression = condition_expression & Attr("status").is_in(
                allowed_statuses
            )
        update_expressions = []
        expression_attribute_names = {}
        expression_attribute_values = {}
        for i, (k, v) in enumerate(attributes.items()):
            expression_attribute_name = f"#{i}"
            expression_attribute_value = f":{i}"
            update_expressions.append(
                f"{expression_attribute_name}={expression_attribute_value}"
            )
            expression_attribute_names[expression_attribute_name] = k
            expression_attribute_values[expression_attribute_value] = v
        update_expression = f"SET {','.join(update_expressions)}"
        await self.dynamo_table.update_item(
            Key=key,
            UpdateExpression=update_expression,
            ExpressionAttributeValues=expression_attribute_values,
            ExpressionAttributeNames=expression_attribute_names,
            ConditionExpression=condition_expression,
        )

    async def _output_exists(self, params):
        try:
            await self.s3_client.head_object(**params)
        except ClientError as e:
            if e.response["ResponseMetadata"]["HTTPStatusCode"] not in (404, 403):
                raise
            return False
        else:
            return True

    @require_permission("user")
    async def Run(self, request, context):
        if request.instance_type == InstanceType.INSTANCE_TYPE_UNSPECIFIED:
            await context.abort(
                code=StatusCode.INVALID_ARGUMENT, details="invalid `instance_type`"
            )

        now = utcnow()
        user = current_user.get()
        partial_ref = Ref.generate_partial(now)
        ref = Ref.pack(request.family, partial_ref)
        job_name = self._job_name_for(ref)

        if len(job_name) > JOB_NAME_CHAR_LIMIT:
            max_characters = JOB_NAME_CHAR_LIMIT - (len(job_name) - len(request.family))
            await context.abort(
                code=StatusCode.INVALID_ARGUMENT,
                details=f"`family` can be at most {max_characters} characters",
            )

        git_sha = await self._fetch_git_sha(request.git_ref)
        parameters = dict(request.parameters)
        instance_type = instance_type_enum_to_str(request.instance_type)
        image_url = self._image_url_for(instance_type)
        pk = self._pk_for(request.family)

        efs_mount = EFSMount(
            family=request.family,
            file_system_id=config.efs.file_system_id,
            base_path=config.efs.base_path,
            access_mode=config.efs.access_mode,
        )

        tags = [
            {"Key": "BaselineJobRunBy", "Value": user.email},
            {"Key": "BaselineJobFamily", "Value": request.family},
            {"Key": "BaselineJobRef", "Value": ref},
        ]

        await self.dynamo_table.put_item(
            Item={
                "pk": pk,
                "sk": partial_ref,
                "ref": ref,
                "family": request.family,
                "status": JobStatus.Name(JobStatus.JOB_STATUS_PENDING),
                "parameters": json.dumps(parameters),
                "image_url": image_url,
                "instance_type": instance_type,
                "run_by": user.email,
                "requested_at": now.isoformat(),
                "git_sha": git_sha,
                "tags": json.dumps(tags),
            },
            ConditionExpression="pk <> :p AND sk <> :s",
            ExpressionAttributeValues={":s": {"S": partial_ref}, ":p": {"S": pk}},
        )

        output_key = self._output_key_for(ref)
        ignore_key = self._ignore_key_for(ref)

        entrypoint = [
            "papermill",
            f"https://github.com/gametimesf/notebooks/blob/{git_sha}/jobs/{request.family}.ipynb",
            f"s3://{config.s3.bucket}/{output_key}",
            "--engine",
            "baseline_engine",
            "-k",
            "python3",
        ]
        for k, v in parameters.items():
            entrypoint.extend(["-p", k, v])

        try:
            await self.sagemaker_client.create_training_job(
                TrainingJobName=job_name,
                AlgorithmSpecification={
                    "TrainingImage": image_url,
                    "TrainingInputMode": "File",
                    "EnableSageMakerMetricsTimeSeries": True,
                    "ContainerEntrypoint": entrypoint,
                },
                ResourceConfig={
                    "InstanceType": instance_type,
                    "InstanceCount": 1,
                    "VolumeSizeInGB": config.sagemaker.job_volume_size_gb,
                },
                InputDataConfig=[efs_mount.input_config],
                OutputDataConfig={
                    "S3OutputPath": f"s3://{config.s3.bucket}/{ignore_key}"
                },
                VpcConfig={
                    "SecurityGroupIds": [config.sagemaker.vpc_security_group_id],
                    "Subnets": config.sagemaker.vpc_subnets.split(","),
                },
                RoleArn=config.sagemaker.job_role_arn,
                StoppingCondition={
                    "MaxRuntimeInSeconds": config.sagemaker.job_max_runtime_seconds,
                },
                Environment={
                    "BASELINE_ENVIRONMENT": config.environment,
                    "BASELINE_S3_BUCKET": config.s3.bucket,
                    "BASELINE_JOB_REF": ref,
                    "BASELINE_URL": config.api.url,
                    "BASELINE_CREDENTIALS_ARN": config.api.credentials_arn,
                    "BASELINE_GITHUB_ACCESS_TOKEN_ARN": config.github.access_token_arn,
                    "AWS_DEFAULT_REGION": config.sagemaker.region,
                    "BASELINE_EFS_MOUNT_PATH": efs_mount.family_mount_path,
                },
                Tags=tags,
            )

            logging.info(f"Successfully created SageMaker training job: {job_name}")

        except Exception as e:
            error_msg = f"Failed to create SageMaker training job {job_name}: {type(e).__name__} - {str(e)}"
            logging.error(error_msg, exc_info=True)
            await self._update_job(
                ref,
                {
                    "status": JobStatus.Name(JobStatus.JOB_STATUS_FAILED),
                    "status_reason": error_msg,
                },
            )
        return JobsServiceRunResponse(ref=ref)

    @require_permission("user")
    async def Cancel(self, request, context):
        try:
            await self._update_job(
                request.ref,
                {
                    "status": JobStatus.Name(JobStatus.JOB_STATUS_CANCELED),
                    "status_reason": None,
                },
                allowed_statuses=[
                    JobStatus.Name(JobStatus.JOB_STATUS_PENDING),
                    JobStatus.Name(JobStatus.JOB_STATUS_RUNNING),
                ],
            )
            job_name = self._job_name_for(request.ref)
            await self.sagemaker_client.stop_training_job(TrainingJobName=job_name)
        except self.dynamo_table.meta.client.exceptions.ConditionalCheckFailedException:
            await context.abort(
                code=StatusCode.FAILED_PRECONDITION,
                details="job cannot be canceled with current status",
            )
        return JobsServiceCancelResponse()

    @require_permission("user")
    async def Describe(self, request, context):
        key = self._dynamo_key_from_ref(request.ref)
        response = await self.dynamo_table.get_item(Key=key)
        raw = response.get("Item", None)

        if not raw:
            await context.abort(code=StatusCode.NOT_FOUND, details="job not found")

        response = JobsServiceDescribeResponse(
            ref=raw["ref"],
            family=raw["family"],
            instance_type=instance_type_str_to_enum(raw["instance_type"]),
            git_sha=raw["git_sha"],
            status=JobStatus.Value(raw["status"]),
            run_by=raw["run_by"],
        )
        response.requested_at.FromDatetime(datetime.fromisoformat(raw["requested_at"]))
        if "parameters" in raw:
            for k, v in json.loads(raw["parameters"]).items():
                response.parameters[k] = v
        if "started_at" in raw:
            response.started_at.FromDatetime(datetime.fromisoformat(raw["started_at"]))
        if "ended_at" in raw:
            response.ended_at.FromDatetime(datetime.fromisoformat(raw["ended_at"]))
        if "asset_manifest" in raw:
            response.asset_manifest.update(json.loads(raw["asset_manifest"]))
        if "evaluation_metrics" in raw:
            response.evaluation_metrics.update(json.loads(raw["evaluation_metrics"]))
        if raw.get("status_reason") is not None:
            response.status_reason = raw["status_reason"]

        return response

    @require_permission("user")
    async def List(self, request, context):
        before = request.before.ToDatetime()
        after = request.after.ToDatetime()
        pk = self._pk_for(request.family)
        pk_condition = Key("pk").eq(pk)
        sk_condition = Key("sk").between(
            after.strftime(REF_DATE_FORMAT), before.strftime(REF_DATE_FORMAT)
        )
        key_condition = pk_condition & sk_condition

        items = await fetch_all(
            self.dynamo_table.query,
            response_key="Items",
            start_kw="ExclusiveStartKey",
            next_kw="LastEvaluatedKey",
            ScanIndexForward=request.ascending,
            KeyConditionExpression=key_condition,
        )

        jobs = []
        for item in items:
            job = JobListItem(
                ref=item["ref"],
                family=item["family"],
                status=JobStatus.Value(item["status"]),
                run_by=item["run_by"],
            )
            job.requested_at.FromDatetime(datetime.fromisoformat(item["requested_at"]))
            if "started_at" in item:
                job.started_at.FromDatetime(datetime.fromisoformat(item["started_at"]))
            if "ended_at" in item:
                job.ended_at.FromDatetime(datetime.fromisoformat(item["ended_at"]))
            jobs.append(job)

        return JobsServiceListResponse(jobs=jobs)

    @require_permission("machine")
    async def Initialize(self, request, context):
        try:
            await self._update_job(
                request.ref,
                {
                    "status": JobStatus.Name(JobStatus.JOB_STATUS_RUNNING),
                    "started_at": utcnow().isoformat(),
                },
                allowed_statuses=[JobStatus.Name(JobStatus.JOB_STATUS_PENDING)],
            )
        except self.dynamo_table.meta.client.exceptions.ConditionalCheckFailedException:
            await context.abort(
                code=StatusCode.FAILED_PRECONDITION,
                details="job cannot be initialized with current status",
            )
        return JobsServiceInitializeResponse()

    @require_permission("machine")
    async def Finalize(self, request, context):
        try:
            attributes = {
                "status": JobStatus.Name(request.status),
                "status_reason": request.status_reason,
                "ended_at": utcnow().isoformat(),
            }
            if request.asset_manifest:
                attributes["asset_manifest"] = MessageToJson(request.asset_manifest)
            if request.evaluation_metrics:
                attributes["evaluation_metrics"] = MessageToJson(
                    request.evaluation_metrics
                )
            await self._update_job(
                request.ref,
                attributes,
                allowed_statuses=[JobStatus.Name(JobStatus.JOB_STATUS_RUNNING)],
            )
        except self.dynamo_table.meta.client.exceptions.ConditionalCheckFailedException:
            await context.abort(
                code=StatusCode.FAILED_PRECONDITION,
                details="job cannot be finalized with current status",
            )
        return JobsServiceFinalizeResponse()

    @require_permission("user")
    async def FetchOutputUrl(self, request, context):
        output_key = self._output_key_for(request.ref)
        params = {"Bucket": config.s3.bucket, "Key": output_key}
        if not await self._output_exists(params):
            await context.abort(StatusCode.NOT_FOUND, "output not found")
        url = await self.s3_client.generate_presigned_url(
            "get_object", Params=params, ExpiresIn=OUTPUT_URL_EXPIRATION_SECONDS
        )
        return JobsServiceFetchOutputUrlResponse(url=url)

    @require_permission("user")
    async def FetchLogs(self, request, context):
        """
        Fetch notebook output from S3 for a training job.

        Args:
            request: JobsServiceFetchLogsRequest containing job ref and tail length
            context: gRPC context

        Returns:
            JobsServiceFetchLogsResponse containing notebook cell outputs
        """
        output_key = self._output_key_for(request.ref)
        params = {"Bucket": config.s3.bucket, "Key": output_key}

        try:
            # Download the notebook from S3
            response = await self.s3_client.get_object(**params)
            async with response["Body"] as streaming_body:
                notebook_content: bytes = await streaming_body.read()

            # Parse the notebook and extract cell outputs
            notebook: nbformat.NotebookNode = nbformat.reads(
                notebook_content.decode("utf-8"), as_version=4
            )

            # If no cells, return empty response
            if not notebook.cells:
                return JobsServiceFetchLogsResponse(content="")

            # Use a deque with maxlen to efficiently implement tail functionality:
            # - When maxlen is None (tail=0), all outputs are kept
            # - When maxlen=N (tail=N), only the last N outputs are kept
            # - New items are appended to the right, oldest items are automatically dropped
            # - This maintains chronological order while efficiently limiting output size
            outputs: deque[str] = deque(
                maxlen=request.tail if request.tail > 0 else None
            )

            for cell in notebook.cells:
                # Only consider code cells with outputs
                if cell.cell_type == "code" and cell.outputs:
                    for output in cell.outputs:
                        # Capture all types of outputs: stream, execute_result, error, and display_data
                        if output.output_type == "stream":
                            outputs.append(output.text)
                        elif (
                            output.output_type == "execute_result"
                            and "text/plain" in output.data
                        ):
                            outputs.append(output.data["text/plain"])
                        elif output.output_type == "error":
                            # Format error output with traceback
                            error_output = []
                            if "ename" in output:
                                error_output.append(
                                    f"{output.ename}: {output.get('evalue', '')}"
                                )
                            if "traceback" in output:
                                error_output.extend(output.traceback)
                            outputs.append("\n".join(error_output))
                        elif output.output_type == "display_data":
                            # For display_data, prefer text/plain if available, otherwise use text/html
                            if "text/plain" in output.data:
                                outputs.append(output.data["text/plain"])
                            elif "text/html" in output.data:
                                # Preserve HTML content with a clear separator
                                outputs.append("\n--- HTML Output ---\n")
                                outputs.append(output.data["text/html"])
                                outputs.append("\n------------------\n")

            # If no outputs found, return empty response
            if not outputs:
                return JobsServiceFetchLogsResponse(content="")

            # Join all outputs with newlines
            content = "\n".join(outputs)

            return JobsServiceFetchLogsResponse(content=content)

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "NoSuchKey":
                await context.abort(
                    code=StatusCode.NOT_FOUND,
                    details=f"No output found for job {request.ref}",
                )
            else:
                logging.error(f"S3 error fetching logs for job {request.ref}: {str(e)}")
                await context.abort(
                    code=StatusCode.INTERNAL,
                    details=f"S3 error: {error_code}",
                )
        except nbformat.ValidationError as e:
            logging.error(f"Invalid notebook format for job {request.ref}: {str(e)}")
            await context.abort(
                code=StatusCode.INTERNAL,
                details="Invalid notebook format",
            )
        except UnicodeDecodeError as e:
            logging.error(
                f"Failed to decode notebook content for job {request.ref}: {str(e)}"
            )
            await context.abort(
                code=StatusCode.INTERNAL,
                details="Failed to decode notebook content",
            )
        except Exception as e:
            logging.error(
                f"Unexpected error fetching logs for job {request.ref}: {str(e)}"
            )
            await context.abort(
                code=StatusCode.INTERNAL,
                details="Unexpected error occurred",
            )
        return None

    async def setup(self):
        self.sagemaker_client = await self.session.client(
            "sagemaker", **config.sagemaker.client_settings
        ).__aenter__()
        self.s3_client = await self.session.client(
            "s3", **config.s3.client_settings, config=Config(signature_version="s3v4")
        ).__aenter__()
        self.dynamo_resource = await self.session.resource(
            "dynamodb", **config.dynamo.client_settings
        ).__aenter__()
        self.dynamo_table = await self.dynamo_resource.Table(config.dynamo.table)
        self.github_client = HTTPClient(
            base_url=config.github.base_url,
            headers={
                "Accept": "application/vnd.github+json",
                "Authorization": f"token {config.github.access_token}",
            },
        )
        await self.github_client.connect()

    async def teardown(self):
        await self.sagemaker_client.__aexit__(None, None, None)
        await self.s3_client.__aexit__(None, None, None)
        await self.dynamo_resource.__aexit__(None, None, None)
        await self.github_client.disconnect()
