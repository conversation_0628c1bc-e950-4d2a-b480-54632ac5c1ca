from config import config
from gametime_protos.mlp.baseline.v1.service_pb2 import InstanceType
from gametime_protos.mlp.baseline.v1.service_pb2 import JupyterServiceDescribeResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JupyterServiceFetchUrlResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JupyterServiceStartResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import JupyterServiceStopResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import <PERSON><PERSON><PERSON><PERSON>tatus
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import (
    add_JupyterServiceServicer_to_server,
)
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import JupyterServiceServicer
from grpc import StatusCode
from utils import instance_type_enum_to_str
from utils import instance_type_str_to_enum
from utils import is_gpu

from mlutils.grpc.auth import current_user
from mlutils.grpc.auth import require_permission


APP_TYPE = "JupyterLab"
APP_NAME = "default"
LANDING_URI = "app:JupyterLab:lab/tree/notebooks/"
STATUS_MAP = {
    "Deleted": JupyterStatus.JUPYTER_STATUS_STOPPED,
    "Deleting": JupyterStatus.JUPYTER_STATUS_STOPPING,
    "Failed": JupyterStatus.JUPYTER_STATUS_FAILED,
    "InService": JupyterStatus.JUPYTER_STATUS_RUNNING,
    "Pending": JupyterStatus.JUPYTER_STATUS_PENDING,
}
DEFAULT_INSTANCE_TYPE = InstanceType.INSTANCE_TYPE_M5_LARGE


register_servicer = add_JupyterServiceServicer_to_server


class Servicer(JupyterServiceServicer):
    def __init__(self, session):
        self.session = session
        self.client = None

    def _profile_name_for(self, email):
        return email.split("@")[0].replace(".", "").lower()

    def _space_name_for(self, profile_name):
        return f"baseline-jupyter--{profile_name}"

    async def _describe(self):
        user = current_user.get()
        profile_name = self._profile_name_for(user.email)
        space_name = self._space_name_for(profile_name)
        try:
            response = await self.client.describe_app(
                DomainId=config.sagemaker.domain_id,
                SpaceName=space_name,
                AppType=APP_TYPE,
                AppName=APP_NAME,
            )
        except self.client.exceptions.ResourceNotFound:
            return JupyterServiceDescribeResponse(
                instance_type=DEFAULT_INSTANCE_TYPE,
                status=JupyterStatus.JUPYTER_STATUS_STOPPED,
            )
        else:
            instance_type = instance_type_str_to_enum(
                response["ResourceSpec"]["InstanceType"]
            )
            return JupyterServiceDescribeResponse(
                instance_type=instance_type, status=STATUS_MAP[response["Status"]]
            )

    async def _require_status(self, context, statuses):
        describe_response = await self._describe()
        if describe_response.status not in statuses:
            await context.abort(
                code=StatusCode.FAILED_PRECONDITION,
                details="Jupyter is not in valid state",
            )

    def _image_config_for(self, instance_type):
        if is_gpu(instance_type):
            return {
                "SageMakerImageArn": config.sagemaker.gpu_image_arn,
                "SageMakerImageVersionArn": config.sagemaker.default_gpu_image_version_arn,
            }
        else:
            return {
                "SageMakerImageArn": config.sagemaker.cpu_image_arn,
                "SageMakerImageVersionArn": config.sagemaker.default_cpu_image_version_arn,
            }

    @require_permission("user")
    async def Start(self, request, context):
        if request.instance_type == InstanceType.INSTANCE_TYPE_UNSPECIFIED:
            await context.abort(
                code=StatusCode.INVALID_ARGUMENT, details="invalid `instance_type`"
            )
        await self._require_status(
            context,
            [JupyterStatus.JUPYTER_STATUS_FAILED, JupyterStatus.JUPYTER_STATUS_STOPPED],
        )
        user = current_user.get()
        profile_name = self._profile_name_for(user.email)
        space_name = self._space_name_for(profile_name)
        instance_type = instance_type_enum_to_str(request.instance_type)
        image_config = self._image_config_for(instance_type)
        await self.client.create_app(
            DomainId=config.sagemaker.domain_id,
            SpaceName=space_name,
            AppType=APP_TYPE,
            AppName=APP_NAME,
            ResourceSpec={
                **image_config,
                "InstanceType": instance_type,
                "LifecycleConfigArn": config.sagemaker.lifecycle_config_arn,
            },
        )
        return JupyterServiceStartResponse()

    @require_permission("user")
    async def Stop(self, request, context):
        await self._require_status(context, [JupyterStatus.JUPYTER_STATUS_RUNNING])
        user = current_user.get()
        profile_name = self._profile_name_for(user.email)
        space_name = self._space_name_for(profile_name)
        await self.client.delete_app(
            DomainId=config.sagemaker.domain_id,
            SpaceName=space_name,
            AppType=APP_TYPE,
            AppName=APP_NAME,
        )
        return JupyterServiceStopResponse()

    @require_permission("user")
    async def Describe(self, request, context):
        return await self._describe()

    @require_permission("user")
    async def FetchUrl(self, request, context):
        await self._require_status(context, [JupyterStatus.JUPYTER_STATUS_RUNNING])
        user = current_user.get()
        profile_name = self._profile_name_for(user.email)
        space_name = self._space_name_for(profile_name)
        response = await self.client.create_presigned_domain_url(
            DomainId=config.sagemaker.domain_id,
            UserProfileName=profile_name,
            SpaceName=space_name,
            LandingUri=LANDING_URI,
        )
        return JupyterServiceFetchUrlResponse(url=response["AuthorizedUrl"])

    async def setup(self):
        self.client = await self.session.client(
            "sagemaker", **config.sagemaker.client_settings
        ).__aenter__()

    async def teardown(self):
        await self.client.__aexit__(None, None, None)
