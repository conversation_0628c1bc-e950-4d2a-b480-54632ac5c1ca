import re

from config import config
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretListItem
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceAddResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceListResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceRemoveResponse
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceUpdateResponse
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import (
    add_SecretsServiceServicer_to_server,
)
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import SecretsServiceServicer
from grpc import StatusCode

from mlutils.boto3 import fetch_all
from mlutils.grpc.auth import require_permission


NAME_REGEX = re.compile(r"[A-Z_]+[A-Z0-9_]*$")


register_servicer = add_SecretsServiceServicer_to_server


class Servicer(SecretsServiceServicer):
    def __init__(self, session):
        self.session = session
        self.client = None

    def _full_name_for(self, name):
        return f"baseline/{config.environment}/{name}"

    def _validate(self, request):
        if not NAME_REGEX.match(request.name):
            return "`name` must contain only uppercase letters, digits, and underscores"
        if request.value == "":
            return "must provide `value`"
        if request.description == "":
            return "must provide `description`"
        return None

    @require_permission("user")
    async def Add(self, request, context):
        full_name = self._full_name_for(request.name)
        error = self._validate(request)
        if error is not None:
            await context.abort(code=StatusCode.INVALID_ARGUMENT, details=error)
        await self.client.create_secret(
            Name=full_name, SecretString=request.value, Description=request.description
        )
        return SecretsServiceAddResponse()

    @require_permission("user")
    async def Update(self, request, context):
        full_name = self._full_name_for(request.name)
        kwargs = dict(SecretId=full_name)
        if request.value != "":
            kwargs["SecretString"] = request.value
        if request.description != "":
            kwargs["Description"] = request.description
        if len(kwargs) == 1:
            await context.abort(
                code=StatusCode.INVALID_ARGUMENT,
                details="must provide `value` and/or `description`",
            )
        try:
            await self.client.update_secret(**kwargs)
        except self.client.exceptions.ResourceNotFoundException:
            await context.abort(
                code=StatusCode.NOT_FOUND, details=f"secret `{request.name}` not found"
            )
        return SecretsServiceUpdateResponse()

    @require_permission("user")
    async def Remove(self, request, context):
        full_name = self._full_name_for(request.name)
        try:
            await self.client.delete_secret(SecretId=full_name)
        except self.client.exceptions.ResourceNotFoundException:
            await context.abort(
                code=StatusCode.NOT_FOUND, details=f"secret `{request.name}` not found"
            )
        return SecretsServiceRemoveResponse()

    @require_permission("user")
    async def List(self, request, context):
        secrets = []
        prefix = self._full_name_for("")
        raw_secrets = await fetch_all(
            self.client.list_secrets,
            response_key="SecretList",
            Filters=[{"Key": "name", "Values": [prefix]}],
        )
        for s in raw_secrets:
            item = SecretListItem(
                name=s["Name"][len(prefix) :], description=s["Description"]
            )
            item.created_at.FromDatetime(s["CreatedDate"])
            item.updated_at.FromDatetime(s["LastChangedDate"])
            secrets.append(item)
        return SecretsServiceListResponse(secrets=secrets)

    async def setup(self):
        self.client = await self.session.client(
            "secretsmanager", **config.secretsmanager.client_settings
        ).__aenter__()

    async def teardown(self):
        await self.client.__aexit__(None, None, None)
