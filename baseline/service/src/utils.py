from datetime import datetime
from datetime import timezone
from dataclasses import dataclass

from gametime_protos.mlp.baseline.v1.service_pb2 import InstanceType


def utcnow() -> datetime:
    """Get current UTC datetime.

    Returns:
        datetime: Current time in UTC
    """
    return datetime.now(timezone.utc)


def is_gpu(instance_type: str) -> bool:
    """Check if instance type is GPU-enabled.

    Args:
        instance_type: SageMaker instance type string

    Returns:
        bool: True if instance type has GPU support
    """
    return "g4dn" in instance_type


def instance_type_enum_to_str(e: InstanceType) -> str:
    """Convert InstanceType enum to SageMaker instance type string.

    Args:
        e: InstanceType enum value

    Returns:
        str: SageMaker instance type string (e.g. 'ml.g4dn.xlarge')
    """
    name = InstanceType.Name(e)
    return name.lower().replace("instance_type", "ml").replace("_", ".")


def instance_type_str_to_enum(s: str) -> InstanceType:
    """Convert SageMaker instance type string to InstanceType enum.

    Args:
        s: SageMaker instance type string (e.g. 'ml.g4dn.xlarge')

    Returns:
        InstanceType: Corresponding enum value
    """
    value = s.replace(".", "_").replace("ml", "instance_type").upper()
    return InstanceType.Value(value)


@dataclass
class EFSMount:
    """Manages EFS mount configuration for training jobs.

    This class handles the configuration and setup of EFS mounts in SageMaker training jobs.

    Path Structure:
        EFS directory structure:
            /jobs/{family}  # Where data actually exists in EFS

        SageMaker mount structure:
            /opt/ml/input/data/workspace/{family}  # Where it appears in container

    Attributes:
        family: Job family name used for directory isolation
        file_system_id: AWS EFS filesystem ID
        base_path: Base path in EFS to mount (default: "/jobs")
        access_mode: EFS access mode (default: "rw")
    """

    family: str
    file_system_id: str
    base_path: str = "/jobs"  # EFS base path that will be mounted
    access_mode: str = "rw"

    @property
    def sagemaker_mount_path(self) -> str:
        """Get SageMaker's standard mount path for input data.

        Returns:
            str: Standard SageMaker mount path
        """
        return "/opt/ml/input/data/workspace"

    @property
    def family_mount_path(self) -> str:
        """Get the path where family data is accessible in the container.

        Returns:
            str: Path to family's data directory
        """
        return f"{self.sagemaker_mount_path}/{self.family}"

    @property
    def input_config(self) -> dict:
        """Get SageMaker input configuration for EFS mount.

        Returns:
            dict: SageMaker InputDataConfig for EFS mount
        """
        return {
            "ChannelName": "workspace",
            "DataSource": {
                "FileSystemDataSource": {
                    "FileSystemId": self.file_system_id,
                    "FileSystemType": "EFS",
                    "DirectoryPath": self.base_path,
                    "FileSystemAccessMode": self.access_mode,
                }
            },
        }
