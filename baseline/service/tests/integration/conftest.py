import json
import os

import aioboto3
import grpc
import pytest
from config import config
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import JobsServiceStub
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import JupyterServiceStub
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import SecretsServiceStub
from pytest_asyncio import is_async_test
from testcontainers.core.container import DockerContainer
from testcontainers.minio import MinioContainer

from mlutils.grpc.auth import BasicAuthClientInterceptor
from mlutils.grpc.auth import decode_users
from mlutils.utils import get_free_tcp_port


os.environ.update(
    {
        "USERS": json.dumps(
            {
                "<EMAIL>": {
                    "password": "fakepw",
                    "permissions": ["user"],
                },
                "<EMAIL>": {
                    "password": "fakepw",
                    "permissions": ["machine"],
                },
            }
        )
    }
)


def pytest_collection_modifyitems(items):
    pytest_asyncio_tests = (item for item in items if is_async_test(item))
    session_scope_marker = pytest.mark.asyncio(scope="session")
    for async_test in pytest_asyncio_tests:
        async_test.add_marker(session_scope_marker, append=False)


@pytest.fixture(scope="session")
async def s3():
    with MinioContainer() as minio:
        minio_config = minio.get_config()
        config.s3.endpoint_url = f"http://{minio_config['endpoint']}"
        config.s3.aws_access_key_id = minio_config["access_key"]
        config.s3.aws_secret_access_key = minio_config["secret_key"]
        session = aioboto3.Session()
        async with session.client("s3", **config.s3.client_settings) as s3_client:
            await s3_client.create_bucket(Bucket=config.s3.bucket)
            yield s3_client


@pytest.fixture(scope="session")
async def dynamo_table():
    with (
        DockerContainer("amazon/dynamodb-local")
        .with_bind_ports(8000, 8000)
        .with_command("-jar DynamoDBLocal.jar")
        .with_exposed_ports(8000) as dynamo
    ):
        port = dynamo.get_exposed_port(8000)
        session = aioboto3.Session()
        config.dynamo.endpoint_url = f"http://localhost:{port}"
        async with session.client(
            "dynamodb", endpoint_url=config.dynamo.endpoint_url
        ) as client:
            await client.create_table(
                TableName=config.dynamo.table,
                KeySchema=[
                    {"AttributeName": "pk", "KeyType": "HASH"},
                    {"AttributeName": "sk", "KeyType": "RANGE"},
                ],
                AttributeDefinitions=[
                    {"AttributeName": "pk", "AttributeType": "S"},
                    {"AttributeName": "sk", "AttributeType": "S"},
                ],
                ProvisionedThroughput={
                    "ReadCapacityUnits": 10,
                    "WriteCapacityUnits": 10,
                },
            )
        async with session.resource(
            "dynamodb", **config.dynamo.client_settings
        ) as dynamo:
            yield await dynamo.Table(config.dynamo.table)


@pytest.fixture(scope="session")
def decoded_users():
    return decode_users(os.environ["USERS"])


@pytest.fixture(scope="session")
def machine_user(decoded_users):
    return decoded_users["<EMAIL>"]


@pytest.fixture(scope="session")
def regular_user(decoded_users):
    return decoded_users["<EMAIL>"]


@pytest.fixture(scope="session")
async def service(dynamo_table, s3):
    from main import service as svc

    host = "localhost"
    port = get_free_tcp_port()
    await svc.start(host, port)
    yield host, port
    await svc.stop()


@pytest.fixture(scope="session")
async def regular_user_channel(service, regular_user):
    host, port = service
    interceptors = [
        BasicAuthClientInterceptor(regular_user.email, regular_user.password)
    ]
    async with grpc.aio.insecure_channel(
        f"{host}:{port}", interceptors=interceptors
    ) as channel:
        await channel.channel_ready()
        yield channel


@pytest.fixture(scope="session")
async def machine_user_channel(service, machine_user):
    host, port = service
    interceptors = [
        BasicAuthClientInterceptor(machine_user.email, machine_user.password)
    ]
    async with grpc.aio.insecure_channel(
        f"{host}:{port}", interceptors=interceptors
    ) as channel:
        await channel.channel_ready()
        yield channel


@pytest.fixture(scope="session")
async def jobs_stub(regular_user_channel):
    return JobsServiceStub(regular_user_channel)


@pytest.fixture(scope="session")
async def machine_user_jobs_stub(machine_user_channel):
    return JobsServiceStub(machine_user_channel)


@pytest.fixture(scope="session")
async def jupyter_stub(regular_user_channel):
    return JupyterServiceStub(regular_user_channel)


@pytest.fixture(scope="session")
async def secrets_stub(regular_user_channel):
    return SecretsServiceStub(regular_user_channel)
