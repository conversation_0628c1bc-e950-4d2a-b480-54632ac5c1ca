import json
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from datetime import timezone

import pytest
from gametime_protos.mlp.baseline.v1.service_pb2 import InstanceType
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceCancelRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceDescribeRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFinalizeRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFetchOutputUrlRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceInitializeRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceListRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceRunRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import <PERSON><PERSON>tat<PERSON>
from grpc import RpcError
from grpc import StatusCode
from servicers.jobs import REF_DATE_FORMAT
from config import config


RPC_EXTRA_KWARGS = dict(wait_for_ready=True, timeout=15)


async def test_run(jobs_stub, dynamo_table, httpserver):
    request = JobsServiceRunRequest(
        family=30 * "a", instance_type=InstanceType.INSTANCE_TYPE_M5_LARGE
    )
    with pytest.raises(RpcError) as exc_info:
        await jobs_stub.Run(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.INVALID_ARGUMENT
    assert "at most" in exc_info.value.details()

    request.family = "test"
    httpserver.expect_oneshot_request(
        "/repos/test/commits/master", method="GET"
    ).respond_with_json({"sha": "fakesha"}, status=200)
    httpserver.expect_oneshot_request("/", method="POST").respond_with_json(
        {"TrainingJobArn": "fakearn"}, status=400
    )
    response = await jobs_stub.Run(request, **RPC_EXTRA_KWARGS)
    assert request.family in response.ref
    dynamo_response = await dynamo_table.get_item(
        Key={"pk": f"job#{request.family}", "sk": response.ref.split("-", 1)[1]}
    )
    item = dynamo_response["Item"]
    assert item["status"] == "JOB_STATUS_FAILED"
    assert "error occurred" in item["status_reason"]

    httpserver.expect_oneshot_request(
        "/repos/test/commits/master", method="GET"
    ).respond_with_json({"sha": "fakesha"}, status=200)
    httpserver.expect_oneshot_request("/", method="POST").respond_with_json(
        {"TrainingJobArn": "fakearn"}, status=200
    )
    response = await jobs_stub.Run(request, **RPC_EXTRA_KWARGS)
    assert request.family in response.ref
    dynamo_response = await dynamo_table.get_item(
        Key={"pk": f"job#{request.family}", "sk": response.ref.split("-", 1)[1]}
    )
    item = dynamo_response["Item"]
    assert item["status"] == "JOB_STATUS_PENDING"
    assert "status_reason" not in item


async def test_cancel(jobs_stub, dynamo_table, httpserver):
    await dynamo_table.put_item(
        Item={"pk": "job#test", "sk": "20240627-abc123", "status": "JOB_STATUS_FAILED"}
    )
    request = JobsServiceCancelRequest(ref="test-20240627-abc123")
    with pytest.raises(RpcError) as exc_info:
        await jobs_stub.Cancel(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.FAILED_PRECONDITION
    assert "cannot be canceled" in exc_info.value.details()

    await dynamo_table.put_item(
        Item={"pk": "job#test", "sk": "20240627-abc123", "status": "JOB_STATUS_RUNNING"}
    )
    httpserver.expect_oneshot_request(
        "/", method="POST", json={"TrainingJobName": "baseline-test-20240627-abc123"}
    ).respond_with_data("", status=200)
    await jobs_stub.Cancel(request, **RPC_EXTRA_KWARGS)
    dynamo_response = await dynamo_table.get_item(
        Key={"pk": "job#test", "sk": "20240627-abc123"}
    )
    item = dynamo_response["Item"]
    assert item["status"] == "JOB_STATUS_CANCELED"


async def test_describe(jobs_stub, dynamo_table):
    request = JobsServiceDescribeRequest(ref="fake-20240627-abc123")
    with pytest.raises(RpcError) as exc_info:
        await jobs_stub.Describe(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.NOT_FOUND
    assert "not found" in exc_info.value.details()

    await dynamo_table.put_item(
        Item={
            "pk": "job#fake",
            "sk": "20240627-abc123",
            "ref": "fake-20240627-abc123",
            "family": "fake",
            "status": "JOB_STATUS_SUCCEEDED",
            "parameters": json.dumps({"test": "123"}),
            "image_url": "test",
            "instance_type": "ml.m5.4xlarge",
            "run_by": "<EMAIL>",
            "requested_at": datetime.now(timezone.utc).isoformat(),
            "git_sha": "abc123",
            "started_at": datetime.now(timezone.utc).isoformat(),
            "ended_at": datetime.now(timezone.utc).isoformat(),
            "asset_manifest": json.dumps({"test": "abc"}),
            "evaluation_metrics": json.dumps({"accuracy": 0.99}),
        }
    )
    response = await jobs_stub.Describe(request, **RPC_EXTRA_KWARGS)
    assert response.ref == "fake-20240627-abc123"
    assert response.family == "fake"
    assert response.instance_type == InstanceType.INSTANCE_TYPE_M5_4XLARGE
    assert response.parameters["test"] == "123"
    assert response.git_sha == "abc123"
    assert response.HasField("requested_at")
    assert response.HasField("started_at")
    assert response.HasField("ended_at")
    assert response.status == JobStatus.JOB_STATUS_SUCCEEDED
    assert response.status_reason == ""
    assert response.run_by == "<EMAIL>"
    assert response.asset_manifest["test"] == "abc"
    assert response.evaluation_metrics["accuracy"] == 0.99


async def test_list(jobs_stub, dynamo_table):
    now = datetime.now(timezone.utc)
    one_day_ago = now - timedelta(days=1)
    two_days_ago = now - timedelta(days=2)
    three_days_ago = now - timedelta(days=3)
    await dynamo_table.put_item(
        Item={
            "pk": "job#fake",
            "sk": f"{three_days_ago.strftime(REF_DATE_FORMAT)}-123",
            "ref": f"fake-{three_days_ago.strftime(REF_DATE_FORMAT)}-123",
            "family": "fake",
            "status": "JOB_STATUS_SUCCEEDED",
            "run_by": "<EMAIL>",
            "requested_at": three_days_ago.isoformat(),
            "started_at": three_days_ago.isoformat(),
            "ended_at": (three_days_ago + timedelta(hours=1)).isoformat(),
        }
    )
    await dynamo_table.put_item(
        Item={
            "pk": "job#fake",
            "sk": f"{two_days_ago.strftime(REF_DATE_FORMAT)}-123",
            "ref": f"fake-{two_days_ago.strftime(REF_DATE_FORMAT)}-123",
            "family": "fake",
            "status": "JOB_STATUS_RUNNING",
            "run_by": "<EMAIL>",
            "requested_at": two_days_ago.isoformat(),
            "started_at": two_days_ago.isoformat(),
        }
    )
    await dynamo_table.put_item(
        Item={
            "pk": "job#fake",
            "sk": f"{one_day_ago.strftime(REF_DATE_FORMAT)}-123",
            "ref": f"fake-{one_day_ago.strftime(REF_DATE_FORMAT)}-123",
            "family": "fake",
            "status": "JOB_STATUS_PENDING",
            "run_by": "<EMAIL>",
            "requested_at": one_day_ago.isoformat(),
        }
    )
    request = JobsServiceListRequest(family="fake")
    request.before.FromDatetime(one_day_ago - timedelta(minutes=1))
    request.after.FromDatetime(three_days_ago + timedelta(minutes=1))
    response = await jobs_stub.List(request, **RPC_EXTRA_KWARGS)
    assert len(response.jobs) == 1

    job = response.jobs[0]
    assert "fake" in job.ref
    assert job.family == "fake"
    assert job.HasField("requested_at")
    assert job.HasField("started_at")
    assert not job.HasField("ended_at")
    assert job.status == JobStatus.JOB_STATUS_RUNNING
    assert job.run_by == "<EMAIL>"


async def test_initialize(machine_user_jobs_stub, dynamo_table):
    await dynamo_table.put_item(
        Item={"pk": "job#test", "sk": "20240627-abc123", "status": "JOB_STATUS_FAILED"}
    )
    request = JobsServiceInitializeRequest(ref="test-20240627-abc123")
    with pytest.raises(RpcError) as exc_info:
        await machine_user_jobs_stub.Initialize(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.FAILED_PRECONDITION
    assert "cannot be initialized" in exc_info.value.details()

    await dynamo_table.put_item(
        Item={"pk": "job#test", "sk": "20240627-abc123", "status": "JOB_STATUS_PENDING"}
    )
    await machine_user_jobs_stub.Initialize(request, **RPC_EXTRA_KWARGS)
    dynamo_response = await dynamo_table.get_item(
        Key={"pk": "job#test", "sk": "20240627-abc123"}
    )
    item = dynamo_response["Item"]
    assert item["status"] == "JOB_STATUS_RUNNING"
    assert "started_at" in item


async def test_finalize(machine_user_jobs_stub, dynamo_table):
    await dynamo_table.put_item(
        Item={"pk": "job#test", "sk": "20240627-abc123", "status": "JOB_STATUS_FAILED"}
    )
    request = JobsServiceFinalizeRequest(ref="test-20240627-abc123")
    with pytest.raises(RpcError) as exc_info:
        await machine_user_jobs_stub.Finalize(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.FAILED_PRECONDITION
    assert "cannot be finalized" in exc_info.value.details()

    await dynamo_table.put_item(
        Item={"pk": "job#test", "sk": "20240627-abc123", "status": "JOB_STATUS_RUNNING"}
    )
    request.status = JobStatus.JOB_STATUS_FAILED
    request.status_reason = "fake reason"
    await machine_user_jobs_stub.Finalize(request, **RPC_EXTRA_KWARGS)
    dynamo_response = await dynamo_table.get_item(
        Key={"pk": "job#test", "sk": "20240627-abc123"}
    )
    item = dynamo_response["Item"]
    assert item["status"] == "JOB_STATUS_FAILED"
    assert item["status_reason"] == "fake reason"
    assert "ended_at" in item

    await dynamo_table.put_item(
        Item={"pk": "job#test", "sk": "20240627-abc123", "status": "JOB_STATUS_RUNNING"}
    )
    request.status = JobStatus.JOB_STATUS_SUCCEEDED
    request.asset_manifest.update({"fake": "123"})
    request.evaluation_metrics.update({"accuracy": 0.99})
    await machine_user_jobs_stub.Finalize(request, **RPC_EXTRA_KWARGS)
    dynamo_response = await dynamo_table.get_item(
        Key={"pk": "job#test", "sk": "20240627-abc123"}
    )
    item = dynamo_response["Item"]
    assert item["status"] == "JOB_STATUS_SUCCEEDED"
    assert json.loads(item["asset_manifest"]) == {"fake": "123"}
    assert json.loads(item["evaluation_metrics"]) == {"accuracy": 0.99}
    assert "ended_at" in item


async def test_fetch_output_url(jobs_stub, s3):
    request = JobsServiceFetchOutputUrlRequest(ref="fake-20240627-abc123")
    with pytest.raises(RpcError) as exc_info:
        await jobs_stub.FetchOutputUrl(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.NOT_FOUND
    assert "not found" in exc_info.value.details()

    await s3.put_object(
        Bucket=config.s3.bucket,
        Key="jobs/fake/20240627-abc123/output.ipynb",
        Body=b"fake",
    )
    response = await jobs_stub.FetchOutputUrl(request, **RPC_EXTRA_KWARGS)
    assert len(response.url) > 0
