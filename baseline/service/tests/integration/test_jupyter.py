import pytest
from gametime_protos.mlp.baseline.v1.service_pb2 import InstanceType
from gametime_protos.mlp.baseline.v1.service_pb2 import JupyterServiceDescribeRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JupyterServiceFetchUrlRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JupyterServiceStartRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JupyterServiceStopRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import <PERSON><PERSON><PERSON><PERSON>tatus
from grpc import RpcError
from grpc import StatusCode


RPC_EXTRA_KWARGS = dict(wait_for_ready=True, timeout=10)


def expect_describe(httpserver, status):
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        json={
            "DomainId": "test",
            "SpaceName": "baseline-jupyter--regularuser",
            "AppType": "JupyterLab",
            "AppName": "default",
        },
    ).respond_with_json(
        {"Status": status, "ResourceSpec": {"InstanceType": "ml.m5.4xlarge"}},
        status=200,
    )


async def test_start(jupyter_stub, httpserver):
    request = JupyterServiceStartRequest()
    with pytest.raises(RpcError) as exc_info:
        await jupyter_stub.Start(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.INVALID_ARGUMENT
    assert "invalid" in exc_info.value.details()

    request.instance_type = InstanceType.INSTANCE_TYPE_M5_4XLARGE
    expect_describe(httpserver, "InService")
    with pytest.raises(RpcError) as exc_info:
        await jupyter_stub.Start(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.FAILED_PRECONDITION
    assert "not in valid state" in exc_info.value.details()

    expect_describe(httpserver, "Deleted")
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        json={
            "DomainId": "test",
            "SpaceName": "baseline-jupyter--regularuser",
            "AppType": "JupyterLab",
            "AppName": "default",
            "ResourceSpec": {
                "SageMakerImageArn": "test",
                "SageMakerImageVersionArn": "testcpu",
                "InstanceType": "ml.m5.4xlarge",
                "LifecycleConfigArn": "test",
            },
        },
    ).respond_with_json({"AppArn": "test"}, status=200)
    response = await jupyter_stub.Start(request, **RPC_EXTRA_KWARGS)
    assert response is not None


async def test_stop(jupyter_stub, httpserver):
    request = JupyterServiceStopRequest()
    expect_describe(httpserver, "Deleted")
    with pytest.raises(RpcError) as exc_info:
        await jupyter_stub.Stop(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.FAILED_PRECONDITION
    assert "not in valid state" in exc_info.value.details()

    expect_describe(httpserver, "InService")
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        json={
            "DomainId": "test",
            "SpaceName": "baseline-jupyter--regularuser",
            "AppType": "JupyterLab",
            "AppName": "default",
        },
    ).respond_with_data("", status=200)
    response = await jupyter_stub.Stop(request, **RPC_EXTRA_KWARGS)
    assert response is not None


async def test_describe(jupyter_stub, httpserver):
    request = JupyterServiceDescribeRequest()
    expect_describe(httpserver, "Deleted")
    response = await jupyter_stub.Describe(request, **RPC_EXTRA_KWARGS)
    assert response.instance_type == InstanceType.INSTANCE_TYPE_M5_4XLARGE
    assert response.status == JupyterStatus.JUPYTER_STATUS_STOPPED


async def test_fetch_url(jupyter_stub, httpserver):
    request = JupyterServiceFetchUrlRequest()
    expect_describe(httpserver, "Deleted")
    with pytest.raises(RpcError) as exc_info:
        await jupyter_stub.FetchUrl(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.FAILED_PRECONDITION
    assert "not in valid state" in exc_info.value.details()

    expect_describe(httpserver, "InService")
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        json={
            "DomainId": "test",
            "UserProfileName": "regularuser",
            "SpaceName": "baseline-jupyter--regularuser",
            "LandingUri": "app:JupyterLab:lab/tree/notebooks/",
        },
    ).respond_with_json({"AuthorizedUrl": "fakeurl"}, status=200)
    response = await jupyter_stub.FetchUrl(request, **RPC_EXTRA_KWARGS)
    assert response.url == "fakeurl"
