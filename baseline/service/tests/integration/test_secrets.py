import pytest
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceAddRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceListRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceRemoveRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceUpdateRequest
from grpc import Rpc<PERSON>rror
from grpc import StatusCode


RPC_EXTRA_KWARGS = dict(wait_for_ready=True, timeout=10)


async def test_add(secrets_stub, httpserver):
    request = SecretsServiceAddRequest(name="invalid", description="fake", value="test")
    with pytest.raises(RpcError) as exc_info:
        await secrets_stub.Add(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.INVALID_ARGUMENT
    assert "must contain only" in exc_info.value.details()

    request.name = "VALID"
    request.value = ""
    with pytest.raises(RpcError) as exc_info:
        await secrets_stub.Add(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.INVALID_ARGUMENT
    assert "must provide" in exc_info.value.details()

    request.value = "test"
    request.description = ""
    with pytest.raises(RpcError) as exc_info:
        await secrets_stub.Add(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.INVALID_ARGUMENT
    assert "must provide" in exc_info.value.details()

    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        headers={
            "x-amz-target": "secretsmanager.CreateSecret",
        },
    ).respond_with_json(
        {
            "ARN": "arn:aws:secretsmanager:us-west-2:123456789012:secret:baseline/testing/VALID",
            "Name": "baseline/testing/VALID",
            "VersionId": "EXAMPLE1-90ab-cdef-fedc-ba987SECRET1",
        },
        status=200,
    )
    request.description = "fake"
    response = await secrets_stub.Add(request, **RPC_EXTRA_KWARGS)
    assert response is not None


async def test_update(secrets_stub, httpserver):
    request = SecretsServiceUpdateRequest(name="VALID")
    with pytest.raises(RpcError) as exc_info:
        await secrets_stub.Update(request, **RPC_EXTRA_KWARGS)

    assert exc_info.value.code() == StatusCode.INVALID_ARGUMENT
    assert "must provide" in exc_info.value.details()

    request.description = "fake"
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        headers={
            "x-amz-target": "secretsmanager.UpdateSecret",
        },
    ).respond_with_json(
        {
            "ARN": "arn:aws:secretsmanager:us-west-2:123456789012:secret:baseline/testing/VALID",
            "Name": "baseline/testing/VALID",
            "VersionId": "EXAMPLE1-90ab-cdef-fedc-ba987SECRET2",
        },
        status=200,
    )
    response = await secrets_stub.Update(request, **RPC_EXTRA_KWARGS)
    assert response is not None


async def test_remove(secrets_stub, httpserver):
    request = SecretsServiceRemoveRequest(name="VALID")
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        headers={
            "x-amz-target": "secretsmanager.DeleteSecret",
        },
    ).respond_with_json(
        {
            "ARN": "arn:aws:secretsmanager:us-west-2:123456789012:secret:baseline/testing/VALID",
            "DeletionDate": 1.523477145729e9,
            "Name": "baseline/testing/VALID",
        },
        status=200,
    )
    response = await secrets_stub.Remove(request, **RPC_EXTRA_KWARGS)
    assert response is not None


async def test_list(secrets_stub, httpserver):
    request = SecretsServiceListRequest()
    httpserver.expect_oneshot_request(
        "/",
        method="POST",
        headers={
            "x-amz-target": "secretsmanager.ListSecrets",
        },
    ).respond_with_json(
        {
            "SecretList": [
                {
                    "ARN": "arn:aws:secretsmanager:us-west-2:123456789012:secret:baseline/testing/VALID",
                    "Description": "fake",
                    "CreatedDate": 1.523477145729e9,
                    "LastChangedDate": 1.523477145729e9,
                    "Name": "baseline/testing/VALID",
                }
            ]
        },
        status=200,
    )
    response = await secrets_stub.List(request, **RPC_EXTRA_KWARGS)
    assert len(response.secrets) == 1

    secret = response.secrets[0]
    assert secret.name == "VALID"
    assert secret.description == "fake"
    assert secret.created_at is not None
    assert secret.updated_at is not None
