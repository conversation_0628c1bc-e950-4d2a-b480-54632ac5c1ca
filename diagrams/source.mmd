graph TB
    subgraph "Development & Tools"
        subgraph "Source Repositories"
            PROTOS[Protos]
            DATA[GameTime Data]
            NOTEBOOKS[Notebooks]
            MLPLAT[MLPlatform<br>mlctl CLI]
        end

        subgraph "CLI Commands"
            CMD_JUPYTER[jupyter start/stop]
            CMD_JOB[job run/list]
            CMD_SECRET[secret manage]
        end
    end

    subgraph "Machine Learning Platform"
        subgraph "Interactive Development"
            JUPYTER[Jupyter Service]
            SM_STUDIO[SageMaker Studio]
        end

        subgraph "Production Training"
            JOBS[Jobs Service]
            SM_JOBS[SageMaker Training]
            AIRFLOW[Baseline Airflow Hooks]
        end

        subgraph "Platform Services"
            BASELINE[Baseline Service]
            SECRETS_SVC[Secrets Service]
        end
    end

    subgraph "Production Applications"
        THE_ALGO[The Algo]
        PRICING[Pricing Service]
    end

    subgraph "Storage & State"
        S3[Baseline S3]
        DYNAMO[DynamoDB]
        SECRETS[Secrets Manager]
        MWAA_S3[MWAA S3]
    end

    %% Development Flows
    PROTOS -->|Generate| THE_ALGO & PRICING & BASELINE & AIRFLOW
    DATA -->|Deploy| MWAA_S3 --> AIRFLOW
    NOTEBOOKS -->|Run| BASELINE

    %% CLI Interactions
    MLPLAT --> CMD_JOB & CMD_JUPYTER & CMD_SECRET
    CMD_JOB -->|gRPC| JOBS
    CMD_JUPYTER -->|gRPC| JUPYTER
    CMD_SECRET -->|gRPC| SECRETS_SVC

    %% Platform Flows
    BASELINE --> JUPYTER & JOBS & SECRETS_SVC
    JUPYTER -->|Launch| SM_STUDIO
    JOBS -->|Launch| SM_JOBS
    AIRFLOW <-->|Schedule Jobs| JOBS

    %% Application Flows
    THE_ALGO <-->|Price Request/Response| PRICING

    %% Storage Access
    SM_JOBS & SM_STUDIO -->|Write| S3
    PRICING -->|Read Assets| S3
    JOBS -->|Track State| DYNAMO
    JOBS & JUPYTER -->|Access| SECRETS
    SECRETS_SVC -->|Manage| SECRETS

    classDef github fill:#24292E,stroke:#FFFFFF,stroke-width:2px,color:#FFFFFF
    classDef service fill:#FF9900,stroke:#232F3E,stroke-width:2px
    classDef storage fill:#3B48CC,stroke:#232F3E,stroke-width:2px
    classDef airflow fill:#017CEE,stroke:#232F3E,stroke-width:2px
    classDef ml fill:#248814,stroke:#232F3E,stroke-width:2px
    classDef sagemaker fill:#FF4F8B,stroke:#232F3E,stroke-width:2px
    classDef cli fill:#4B0082,stroke:#FFFFFF,stroke-width:2px,color:#FFFFFF

    class PROTOS,DATA,NOTEBOOKS,MLPLAT github
    class THE_ALGO,PRICING,BASELINE service
    class S3,DYNAMO,SECRETS,MWAA_S3 storage
    class AIRFLOW airflow
    class JUPYTER,JOBS,SECRETS_SVC ml
    class SM_STUDIO,SM_JOBS sagemaker
    class CMD_JOB,CMD_JUPYTER,CMD_SECRET cli
