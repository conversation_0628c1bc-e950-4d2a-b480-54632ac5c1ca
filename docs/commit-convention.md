# Commit Convention Documentation

## Commit Message Format

All commit messages should follow this format:
```
type(scope): subject
```

Where:
- `type` is one of: `feat`, `fix`, `chore`, `docs`, `style`, `refactor`, `perf`, `test`, `ci`
- `scope` indicates the area of the codebase affected
- `subject` is a clear description of the change

## Scope Categories

### Component Scopes
Changes that affect a specific component:

- `baseline-images`: Changes to baseline Docker images
- `baseline-sdk`: Changes to the baseline SDK
- `baseline-service`: Changes to the baseline service
- `mlctl`: Changes to the ML CLI tool
- `mlutils`: Changes to ML utilities library
- `terraform`: Changes to Terraform infrastructure code

### Cross-cutting Scopes
Changes that affect multiple components:

- `deps`: Dependency management (package versions, lockfiles)
- `infra`: Infrastructure or architecture changes
- `repo`: Repository-wide changes or restructuring

## Examples

```
feat(mlctl): add new command for viewing job status
fix(baseline-service): resolve race condition in job scheduler
chore(deps): update dependency versions
docs(mlutils): add documentation for sklearn transformers
ci(infra): add commitlint validation
refactor(baseline-sdk): improve error handling
```

---

## Commit Message

```
ci(infra): implement simplified commit scope convention

Replace dual-axis scope taxonomy with a streamlined approach that reduces
redundancy while maintaining clear distinction between component-specific
and cross-cutting changes.
```
