syntax = "proto3";

package mle.fraud.v1;

option go_package = "mle/fraud/v1;protos";

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

service FraudService {
  rpc ScoreOrder(FraudServiceScoreOrderRequest) returns (FraudServiceScoreOrderResponse) {}
}

message UserData {
  string id = 1;
  google.protobuf.Timestamp created_at = 2;
  google.protobuf.StringValue name = 3;
  string email = 4;
  google.protobuf.StringValue phone = 5;
  google.protobuf.Int64Value seconds_since_last_order = 6;
  int32 payment_method_count = 7;
  int32 order_count = 8;
}

message DeviceData {
  string id = 1;
  string manufacturer = 2;
  string model = 3;
  string os_type = 4;
  string os_version = 5;
  string platform = 6;
  string app_version = 7;
  google.protobuf.StringValue accept_language = 8;
  string ip_address = 9;
  string user_agent = 10;
}

message AddressData {
  google.protobuf.StringValue line1 = 1;
  google.protobuf.StringValue line2 = 2;
  google.protobuf.StringValue city = 3;
  google.protobuf.StringValue state = 4;
  google.protobuf.StringValue postal_code = 5;
  google.protobuf.StringValue country_code = 6;
}

message EventData {
  string id = 1;
  google.protobuf.Timestamp timestamp = 2;
  string league = 3;
  AddressData address = 4;
  google.protobuf.Int64Value market_sales_trailing_overall_cents = 5;
  google.protobuf.Int64Value market_sales_trailing_24_hours_cents = 6;
  google.protobuf.Int64Value performer_market_sales_trailing_overall_cents = 7;
  google.protobuf.Int64Value performer_market_sales_trailing_24_hours_cents = 8;
  float latitude = 9;
  float longitude = 10;
}

message OrderData {
  string id = 1;
  google.protobuf.Timestamp created_at = 2;
  int32 quantity = 3;
  string delivery_type = 4;
  google.protobuf.StringValue transfer_email = 5;
  int64 amount_cents = 6;
  int64 credits_used_cents = 7;
  int32 failed_attempt_count = 8;
}

message CardData {
  string brand = 1;
  string bin = 2;
  string fingerprint = 3;
  string funding = 4;
  google.protobuf.StringValue avs_check = 5;
  google.protobuf.StringValue cvv_check = 6;
  bool prepaid = 7;
}

message PaymentMethodData {
  string id = 1;
  google.protobuf.Timestamp created_at = 2;
  string gateway = 3;
  string kind = 4;
  CardData card = 5;
  AddressData address = 6;
}

message EmailageData {
  int32 score = 1;
  int32 email_age_years = 2;
  string fraud_level = 3;
  string reason = 4;
}

message TelesignData {
  string phone_type = 1;
  int32 score = 2;
  string risk = 3;
  string recommendation = 4;
}

message FastlyData {
  string postal_code = 1;
  string country_code = 2;
  float latitude = 3;
  float longitude = 4;
  string connection_speed = 5;
  string connection_type = 6;
  string proxy_type = 7;
  string proxy_description = 8;
}

message FraudServiceScoreOrderRequest {
  UserData user = 1;
  DeviceData device = 2;
  EventData event = 3;
  OrderData order = 4;
  PaymentMethodData payment_method = 5;
  EmailageData emailage = 6;
  TelesignData telesign = 7;
  FastlyData fastly = 8;
}

message FraudServiceScoreOrderResponse {
  float score = 1;
}
