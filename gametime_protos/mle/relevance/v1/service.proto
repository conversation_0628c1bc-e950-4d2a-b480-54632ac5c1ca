syntax = "proto3";

package mle.relevance.v1;

option go_package = "mle/relevance/v1;protos";

service RelevanceService {
  rpc Score(ScoreRequest) returns (ScoreResponse) {}
}

enum Platform {
  PLATFORM_UNSPECIFIED = 0;
  PLATFORM_MOBILE_APP_IOS = 1;
  PLATFORM_MOBILE_APP_ANDROID = 2;
  PLATFORM_MOBILE_WEB = 3;
  PLATFORM_DESKTOP_WEB = 4;
}

enum DeliveryType {
  DELIVERY_TYPE_UNSPECIFIED = 0;
  DELIVERY_TYPE_MOBILE = 1;
}

message Listing {
  string id = 1;
  int32 competition_price_cents = 2;
  int32 cost_cents = 3;
  int32 our_cost_cents = 4;
  int32 prefee_price_cents = 5;
  int32 price_cents = 6;
  int32 display_savings_cents = 7;
  int32 display_savings_percent = 8;
  string section_group = 9;
  string section = 10;
  string row = 11;
  float seat_quality_score = 12;
  float value_score = 13;
}

message ScoreRequest {
  Platform platform = 1;
  repeated Listing listings = 2;
}

message ScoreResponse {
  repeated float scores = 1;
}
