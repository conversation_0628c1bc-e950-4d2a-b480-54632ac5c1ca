syntax = "proto3";

package mlp.baseline.v1;

option go_package = "mlp/baseline/v1;protos";

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

service JupyterService {
  rpc Start(JupyterServiceStartRequest) returns (JupyterServiceStartResponse) {}
  rpc Stop(JupyterServiceStopRequest) returns (JupyterServiceStopResponse) {}
  rpc Describe(JupyterServiceDescribeRequest) returns (JupyterServiceDescribeResponse) {}
  rpc FetchUrl(JupyterServiceFetchUrlRequest) returns (JupyterServiceFetchUrlResponse) {}
  rpc FetchConnectionInfo(JupyterServiceFetchConnectionInfoRequest) returns (JupyterServiceFetchConnectionInfoResponse) {}
}

service JobsService {
  rpc Run(JobsServiceRunRequest) returns (JobsServiceRunResponse) {}
  rpc Cancel(JobsServiceCancelRequest) returns (JobsServiceCancelResponse) {}
  rpc Describe(JobsServiceDescribeRequest) returns (JobsServiceDescribeResponse) {}
  rpc List(JobsServiceListRequest) returns (JobsServiceListResponse) {}
  rpc Initialize(JobsServiceInitializeRequest) returns (JobsServiceInitializeResponse) {}
  rpc Finalize(JobsServiceFinalizeRequest) returns (JobsServiceFinalizeResponse) {}
  rpc FetchOutputUrl(JobsServiceFetchOutputUrlRequest) returns (JobsServiceFetchOutputUrlResponse) {}
  rpc FetchLogs(JobsServiceFetchLogsRequest) returns (JobsServiceFetchLogsResponse) {}
}

service SecretsService {
  rpc Add(SecretsServiceAddRequest) returns (SecretsServiceAddResponse) {}
  rpc Update(SecretsServiceUpdateRequest) returns (SecretsServiceUpdateResponse) {}
  rpc Remove(SecretsServiceRemoveRequest) returns (SecretsServiceRemoveResponse) {}
  rpc List(SecretsServiceListRequest) returns (SecretsServiceListResponse) {}
}

enum InstanceType {
  INSTANCE_TYPE_UNSPECIFIED = 0;
  INSTANCE_TYPE_M5_LARGE = 1;
  INSTANCE_TYPE_M5_4XLARGE = 2;
  INSTANCE_TYPE_M5_24XLARGE = 3;
  INSTANCE_TYPE_G4DN_4XLARGE = 4;
  INSTANCE_TYPE_G4DN_12XLARGE = 5;
}

enum JupyterStatus {
  JUPYTER_STATUS_UNSPECIFIED = 0;
  JUPYTER_STATUS_PENDING = 1;
  JUPYTER_STATUS_RUNNING = 2;
  JUPYTER_STATUS_STOPPING = 3;
  JUPYTER_STATUS_STOPPED = 4;
  JUPYTER_STATUS_FAILED = 5;
}

message JupyterServiceStartRequest {
  InstanceType instance_type = 1;
}

message JupyterServiceStartResponse {}

message JupyterServiceStopRequest {}

message JupyterServiceStopResponse {}

message JupyterServiceDescribeRequest {}

message JupyterServiceDescribeResponse {
  InstanceType instance_type = 1;
  JupyterStatus status = 2;
}

message JupyterServiceFetchUrlRequest {}

message JupyterServiceFetchUrlResponse {
  string url = 1;
}

message JupyterServiceFetchConnectionInfoRequest {}

message JupyterServiceFetchConnectionInfoResponse {
  string instance_id = 1;
  string domain_id = 2;
  string space_name = 3;
  string app_name = 4;
  string host = 5;
  int32 jupyter_port = 6;
}

enum JobStatus {
  JOB_STATUS_UNSPECIFIED = 0;
  JOB_STATUS_PENDING = 1;
  JOB_STATUS_RUNNING = 2;
  JOB_STATUS_SUCCEEDED = 3;
  JOB_STATUS_FAILED = 4;
  JOB_STATUS_CANCELED = 5;
}

message JobsServiceRunRequest {
  string family = 1;
  InstanceType instance_type = 2;
  map<string, string> parameters = 3;
  string git_ref = 4;
}

message JobsServiceRunResponse {
  string ref = 1;
}

message JobsServiceCancelRequest {
  string ref = 1;
}

message JobsServiceCancelResponse {}

message JobsServiceDescribeRequest {
  string ref = 1;
}

message JobsServiceDescribeResponse {
  string ref = 1;
  string family = 2;
  InstanceType instance_type = 3;
  map<string, string> parameters = 4;
  string git_sha = 5;
  google.protobuf.Timestamp requested_at = 6;
  google.protobuf.Timestamp started_at = 7;
  google.protobuf.Timestamp ended_at = 8;
  JobStatus status = 9;
  string status_reason = 10;
  string run_by = 11;
  google.protobuf.Struct asset_manifest = 12;
  google.protobuf.Struct evaluation_metrics = 13;
}

message JobListItem {
  string ref = 1;
  string family = 2;
  google.protobuf.Timestamp requested_at = 3;
  google.protobuf.Timestamp started_at = 4;
  google.protobuf.Timestamp ended_at = 5;
  JobStatus status = 6;
  string run_by = 7;
}

message JobsServiceListRequest {
  string family = 1;
  google.protobuf.Timestamp after = 2;
  google.protobuf.Timestamp before = 3;
  bool ascending = 4;
}

message JobsServiceListResponse {
  repeated JobListItem jobs = 1;
}

message JobsServiceInitializeRequest {
  string ref = 1;
}

message JobsServiceInitializeResponse {}

message JobsServiceFinalizeRequest {
  string ref = 1;
  JobStatus status = 2;
  string status_reason = 3;
  google.protobuf.Struct asset_manifest = 4;
  google.protobuf.Struct evaluation_metrics = 5;
}

message JobsServiceFinalizeResponse {}

message JobsServiceFetchOutputUrlRequest {
  string ref = 1;
}

message JobsServiceFetchOutputUrlResponse {
  string url = 1;
}

message JobsServiceFetchLogsRequest {
  string ref = 1;
  int32 tail = 2;
}

message JobsServiceFetchLogsResponse {
  string content = 1;
}

message SecretsServiceAddRequest {
  string name = 1;
  string description = 2;
  string value = 3;
}

message SecretsServiceAddResponse {}

message SecretsServiceUpdateRequest {
  string name = 1;
  string description = 2;
  string value = 3;
}

message SecretsServiceUpdateResponse {}

message SecretsServiceRemoveRequest {
  string name = 1;
}

message SecretsServiceRemoveResponse {}

message SecretListItem {
  string name = 1;
  string description = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
}


message SecretsServiceListRequest {}

message SecretsServiceListResponse {
  repeated SecretListItem secrets = 1;
}
