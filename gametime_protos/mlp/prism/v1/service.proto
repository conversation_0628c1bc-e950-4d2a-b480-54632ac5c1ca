syntax = "proto3";

package mlp.prism.v1;

option go_package = "mlp/prism/v1;protos";

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

service FamiliesService {
	rpc Create(FamiliesServiceCreateRequest) returns (FamiliesServiceCreateResponse) {}
	rpc Delete(FamiliesServiceDeleteRequest) returns (FamiliesServiceDeleteResponse) {}
	rpc Describe(FamiliesServiceDescribeRequest) returns (FamiliesServiceDescribeResponse) {}
	rpc List(FamiliesServiceListRequest) returns (FamiliesServiceListResponse) {}
}

service OnlineFeaturesService {
	rpc Fetch(OnlineFeaturesServiceFetchRequest) returns (OnlineFeaturesServiceFetchResponse) {}
}

service OfflineFeaturesService {
	rpc CreateDataset(OfflineFeaturesServiceCreateDatasetRequest) returns (OfflineFeaturesServiceCreateDatasetResponse) {}
	rpc DescribeDataset(OfflineFeaturesServiceDescribeDatasetRequest) returns (OfflineFeaturesServiceDescribeDatasetResponse) {}
	rpc FetchDataset(OfflineFeaturesServiceFetchDatasetRequest) returns (OfflineFeaturesServiceFetchDatasetResponse) {}
}

message BatchSourceConfig {
	string table = 1;
  int32 late_arriving_data_lag_seconds = 2;
}

message StreamingSourceConfig {
	string topic = 1;
}

message SourceConfig {
	BatchSourceConfig batch = 1;
	StreamingSourceConfig streaming = 2;
	google.protobuf.StringValue query = 3;
}

enum AggregationFunction {
  AGGREGATION_FUNCTION_UNSPECIFIED = 0;
  AGGREGATION_FUNCTION_COUNT = 1;
  AGGREGATION_FUNCTION_AVG = 2;
  AGGREGATION_FUNCTION_SUM = 3;
  AGGREGATION_FUNCTION_STDDEV = 4;
}

message FeatureConfig {
  string column = 1;
  repeated AggregationFunction aggregations = 2;
}

message FamilyConfig {
  SourceConfig source = 1;
  string id_column = 2;
  string timestamp_column = 3;
  repeated string identifier_columns = 4;
  repeated FeatureConfig features = 5;
}

message FamiliesServiceCreateRequest {
  string name = 1;
  FamilyConfig config = 2;
  bool draft = 3;
}

message FamiliesServiceCreateResponse {}

message FamiliesServiceDescribeRequest {
	string name = 1;
}

enum FamilyStatus {
  FAMILY_STATUS_UNSPECIFIED = 0;
  FAMILY_STATUS_INITIALIZING = 1;
  FAMILY_STATUS_INITIALIZING_FAILED = 2;
  FAMILY_STATUS_BACKFILLING = 3;
  FAMILY_STATUS_BACKFILLING_FAILED = 4;
  FAMILY_STATUS_RUNNING = 5;
  FAMILY_STATUS_RUNNING_FAILED = 6;
  FAMILY_STATUS_DELETING = 7;
  FAMILY_STATUS_DELETING_FAILED = 8;
}

message Family {
  string name = 1;
  FamilyConfig config = 2;
	bool draft = 3;
	google.protobuf.Timestamp inserted_at = 4;
  FamilyStatus status = 5;
  string status_detail = 6;
  google.protobuf.Timestamp last_fetched_at = 7;
  google.protobuf.Timestamp frontier = 8;
}

message FamiliesServiceDescribeResponse { Family family = 1; }

message FamiliesServiceListRequest {
  bool exclude_draft = 1;
}

message FamiliesServiceListResponse {
	repeated Family families = 1;
}

message FamiliesServiceDeleteRequest {
  string name = 1;
}

message FamiliesServiceDeleteResponse {}

message Window {
  int64 weeks = 1;
  int64 days = 2;
  int64 hours = 3;
  int64 minutes = 4;
  int64 seconds = 5;
  int64 milliseconds = 6;
  int64 microseconds = 7;
}

message Aggregation {
  AggregationFunction function = 1;
  Window window = 2;
}

message FeatureRequest {
  string family = 1;
  string column = 2;
  Aggregation aggregation = 3;
  map<string, string> identifier_mapping = 4;
}

message OnlineFeaturesServiceFetchRequest {
  map<string, FeatureRequest> feature_requests = 1;
  map<string, string> identifiers = 2;
}

message OnlineFeaturesServiceFetchResponse {
  bytes features = 1;
  google.protobuf.Timestamp frontier = 2;
}

message OfflineFeaturesServiceCreateDatasetRequest {
  int64 num_spine_chunks = 1;
  map<string, FeatureRequest> feature_requests = 2;
  string spine_s3_uri = 3;
}

message PresignedUrl {
  string url = 1;
  string fields = 2;
}

message OfflineFeaturesServiceCreateDatasetResponse {
  string ref = 1;
  repeated PresignedUrl presigned_urls = 2;
}

message OfflineFeaturesServiceDescribeDatasetRequest {
  string ref = 1;
}

enum DatasetStatus {
  DATASET_STATUS_UNSPECIFIED = 0;
  DATASET_STATUS_SPINE_UPLOADING = 1;
  DATASET_STATUS_SPINE_UPLOADING_FAILED = 2;
  DATASET_STATUS_GENERATING = 3;
  DATASET_STATUS_GENERATING_FAILED = 4;
  DATASET_STATUS_READY = 5;
}

message OfflineFeaturesServiceDescribeDatasetResponse {
  DatasetStatus status = 1;
	string status_detail = 2;
}

message OfflineFeaturesServiceFetchDatasetRequest {
  string ref = 1;
}

message OfflineFeaturesServiceFetchDatasetResponse {
  string ref = 1;
  repeated PresignedUrl presigned_urls = 2;
}
