syntax = "proto3";

package pandc.curation.v1;

option go_package = "pandc/curation/v1;protos";

import "google/protobuf/timestamp.proto";

service CurationService {
  rpc Curate(CurationServiceCurateRequest) returns (CurationServiceCurateResponse) {}
}

service TableService {
  rpc UpsertEssentials(TableServiceUpsertEssentialsRequest) returns (TableServiceUpsertEssentialsResponse) {}
  rpc DescribeEssentials(TableServiceDescribeEssentialsRequest) returns (TableServiceDescribeEssentialsResponse) {}
}

message ZoomLevel {
  int32 tag = 1;
  int32 count = 2;
}

message CurationOptions {
  bool include_overflow_listings = 1;
}

message CurationServiceCurateRequest {
  string event_id = 1;
  string venue = 2;
  string venue_config = 3;
  bytes listings = 4;
  int32 quantity = 5;
  repeated ZoomLevel zoom_levels = 6;
  google.protobuf.Timestamp event_time = 7;
  string league = 8;
  CurationOptions options = 9;
}

message CurationServiceCurateResponse {
  bytes results = 1;
}

message TableServiceUpsertEssentialsRequest {
  string venue = 1;
  string venue_config = 2;
  bytes body = 3;
  string version = 4;
}

message TableServiceUpsertEssentialsResponse {}

message TableServiceDescribeEssentialsRequest {
  string venue = 1;
  string venue_config = 2;
}

message TableServiceDescribeEssentialsResponse {
  string version = 1;
}
