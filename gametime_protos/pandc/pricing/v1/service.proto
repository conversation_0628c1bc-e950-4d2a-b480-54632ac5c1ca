syntax = "proto3";

package pandc.pricing.v1;

option go_package = "pandc/pricing/v1;protos";

service PricingService {
  rpc Price(PricingServicePriceRequest) returns (PricingServicePriceResponse) {}
  rpc InvertPrice(PricingServiceInvertPriceRequest) returns (PricingServiceInvertPriceResponse) {}
  rpc FetchSettings(PricingServiceFetchSettingsRequest) returns (PricingServiceFetchSettingsResponse) {}
  rpc UpdateMarkups(PricingServiceUpdateMarkupsRequest) returns (PricingServiceUpdateMarkupsResponse) {}
}

message PricingServicePriceRequest {
  string league = 1;
  string event_id = 2;
  bytes listings = 3;
}

message PricingServicePriceResponse {
  bytes prices = 1;
  bytes results = 2;
}

message PricingServiceInvertPriceRequest {
  string league = 1;
  string event_id = 2;
  int32 target_price_cents = 3;
  int32 unit_cost_cents = 4;
  double sell_fee = 5;
  bool is_spec = 6 [deprecated = true];
  bool is_high_ops_cost = 7;
}

message PricingServiceInvertPriceResponse {
  double target_promo_fee = 1;
}

message PricingServiceUpdateMarkupsRequest {
  map<string, float> markups = 1;
}

message PricingServiceUpdateMarkupsResponse {}

message PricingServiceFetchSettingsRequest {}

message PricingServiceFetchSettingsResponse {
  double default_markup = 1;
  map<string, double> default_league_markups = 2;
  double min_markup = 3;
  double max_markup = 4;
  double spec_markup = 5 [deprecated = true];
  int32 intercept_cents = 6;
  double high_ops_cost_markup = 7;
}

