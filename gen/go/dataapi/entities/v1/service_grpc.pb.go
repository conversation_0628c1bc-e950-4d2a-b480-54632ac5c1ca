// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: gametime_protos/dataapi/entities/v1/service.proto

package protos

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GenericService_GetGenericEntities_FullMethodName = "/dataapi.entities.v1.GenericService/GetGenericEntities"
	GenericService_SetGenericEntities_FullMethodName = "/dataapi.entities.v1.GenericService/SetGenericEntities"
)

// GenericServiceClient is the client API for GenericService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GenericServiceClient interface {
	GetGenericEntities(ctx context.Context, in *GetGenericEntitiesRequest, opts ...grpc.CallOption) (*GetGenericEntitiesResponse, error)
	SetGenericEntities(ctx context.Context, in *SetGenericEntitiesRequest, opts ...grpc.CallOption) (*SetGenericEntitiesResponse, error)
}

type genericServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGenericServiceClient(cc grpc.ClientConnInterface) GenericServiceClient {
	return &genericServiceClient{cc}
}

func (c *genericServiceClient) GetGenericEntities(ctx context.Context, in *GetGenericEntitiesRequest, opts ...grpc.CallOption) (*GetGenericEntitiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGenericEntitiesResponse)
	err := c.cc.Invoke(ctx, GenericService_GetGenericEntities_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genericServiceClient) SetGenericEntities(ctx context.Context, in *SetGenericEntitiesRequest, opts ...grpc.CallOption) (*SetGenericEntitiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetGenericEntitiesResponse)
	err := c.cc.Invoke(ctx, GenericService_SetGenericEntities_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GenericServiceServer is the server API for GenericService service.
// All implementations must embed UnimplementedGenericServiceServer
// for forward compatibility.
type GenericServiceServer interface {
	GetGenericEntities(context.Context, *GetGenericEntitiesRequest) (*GetGenericEntitiesResponse, error)
	SetGenericEntities(context.Context, *SetGenericEntitiesRequest) (*SetGenericEntitiesResponse, error)
	mustEmbedUnimplementedGenericServiceServer()
}

// UnimplementedGenericServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGenericServiceServer struct{}

func (UnimplementedGenericServiceServer) GetGenericEntities(context.Context, *GetGenericEntitiesRequest) (*GetGenericEntitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGenericEntities not implemented")
}
func (UnimplementedGenericServiceServer) SetGenericEntities(context.Context, *SetGenericEntitiesRequest) (*SetGenericEntitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetGenericEntities not implemented")
}
func (UnimplementedGenericServiceServer) mustEmbedUnimplementedGenericServiceServer() {}
func (UnimplementedGenericServiceServer) testEmbeddedByValue()                        {}

// UnsafeGenericServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GenericServiceServer will
// result in compilation errors.
type UnsafeGenericServiceServer interface {
	mustEmbedUnimplementedGenericServiceServer()
}

func RegisterGenericServiceServer(s grpc.ServiceRegistrar, srv GenericServiceServer) {
	// If the following call pancis, it indicates UnimplementedGenericServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GenericService_ServiceDesc, srv)
}

func _GenericService_GetGenericEntities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGenericEntitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenericServiceServer).GetGenericEntities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GenericService_GetGenericEntities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenericServiceServer).GetGenericEntities(ctx, req.(*GetGenericEntitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GenericService_SetGenericEntities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGenericEntitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenericServiceServer).SetGenericEntities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GenericService_SetGenericEntities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenericServiceServer).SetGenericEntities(ctx, req.(*SetGenericEntitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GenericService_ServiceDesc is the grpc.ServiceDesc for GenericService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GenericService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dataapi.entities.v1.GenericService",
	HandlerType: (*GenericServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGenericEntities",
			Handler:    _GenericService_GetGenericEntities_Handler,
		},
		{
			MethodName: "SetGenericEntities",
			Handler:    _GenericService_SetGenericEntities_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/dataapi/entities/v1/service.proto",
}
