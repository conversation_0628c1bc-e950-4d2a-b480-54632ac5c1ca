// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: gametime_protos/mle/fraud/v1/service.proto

package protos

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FraudService_ScoreOrder_FullMethodName = "/mle.fraud.v1.FraudService/ScoreOrder"
)

// FraudServiceClient is the client API for FraudService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FraudServiceClient interface {
	ScoreOrder(ctx context.Context, in *FraudServiceScoreOrderRequest, opts ...grpc.CallOption) (*FraudServiceScoreOrderResponse, error)
}

type fraudServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFraudServiceClient(cc grpc.ClientConnInterface) FraudServiceClient {
	return &fraudServiceClient{cc}
}

func (c *fraudServiceClient) ScoreOrder(ctx context.Context, in *FraudServiceScoreOrderRequest, opts ...grpc.CallOption) (*FraudServiceScoreOrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FraudServiceScoreOrderResponse)
	err := c.cc.Invoke(ctx, FraudService_ScoreOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FraudServiceServer is the server API for FraudService service.
// All implementations must embed UnimplementedFraudServiceServer
// for forward compatibility.
type FraudServiceServer interface {
	ScoreOrder(context.Context, *FraudServiceScoreOrderRequest) (*FraudServiceScoreOrderResponse, error)
	mustEmbedUnimplementedFraudServiceServer()
}

// UnimplementedFraudServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFraudServiceServer struct{}

func (UnimplementedFraudServiceServer) ScoreOrder(context.Context, *FraudServiceScoreOrderRequest) (*FraudServiceScoreOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScoreOrder not implemented")
}
func (UnimplementedFraudServiceServer) mustEmbedUnimplementedFraudServiceServer() {}
func (UnimplementedFraudServiceServer) testEmbeddedByValue()                      {}

// UnsafeFraudServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FraudServiceServer will
// result in compilation errors.
type UnsafeFraudServiceServer interface {
	mustEmbedUnimplementedFraudServiceServer()
}

func RegisterFraudServiceServer(s grpc.ServiceRegistrar, srv FraudServiceServer) {
	// If the following call pancis, it indicates UnimplementedFraudServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FraudService_ServiceDesc, srv)
}

func _FraudService_ScoreOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FraudServiceScoreOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FraudServiceServer).ScoreOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FraudService_ScoreOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FraudServiceServer).ScoreOrder(ctx, req.(*FraudServiceScoreOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FraudService_ServiceDesc is the grpc.ServiceDesc for FraudService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FraudService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mle.fraud.v1.FraudService",
	HandlerType: (*FraudServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ScoreOrder",
			Handler:    _FraudService_ScoreOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mle/fraud/v1/service.proto",
}
