// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: gametime_protos/mle/relevance/v1/service.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Platform int32

const (
	Platform_PLATFORM_UNSPECIFIED        Platform = 0
	Platform_PLATFORM_MOBILE_APP_IOS     Platform = 1
	Platform_PLATFORM_MOBILE_APP_ANDROID Platform = 2
	Platform_PLATFORM_MOBILE_WEB         Platform = 3
	Platform_PLATFORM_DESKTOP_WEB        Platform = 4
)

// Enum value maps for Platform.
var (
	Platform_name = map[int32]string{
		0: "PLATFORM_UNSPECIFIED",
		1: "PLATFORM_MOBILE_APP_IOS",
		2: "PLATFORM_MOBILE_APP_ANDROID",
		3: "PLATFORM_MOBILE_WEB",
		4: "PLATFORM_DESKTOP_WEB",
	}
	Platform_value = map[string]int32{
		"PLATFORM_UNSPECIFIED":        0,
		"PLATFORM_MOBILE_APP_IOS":     1,
		"PLATFORM_MOBILE_APP_ANDROID": 2,
		"PLATFORM_MOBILE_WEB":         3,
		"PLATFORM_DESKTOP_WEB":        4,
	}
)

func (x Platform) Enum() *Platform {
	p := new(Platform)
	*p = x
	return p
}

func (x Platform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Platform) Descriptor() protoreflect.EnumDescriptor {
	return file_gametime_protos_mle_relevance_v1_service_proto_enumTypes[0].Descriptor()
}

func (Platform) Type() protoreflect.EnumType {
	return &file_gametime_protos_mle_relevance_v1_service_proto_enumTypes[0]
}

func (x Platform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Platform.Descriptor instead.
func (Platform) EnumDescriptor() ([]byte, []int) {
	return file_gametime_protos_mle_relevance_v1_service_proto_rawDescGZIP(), []int{0}
}

type DeliveryType int32

const (
	DeliveryType_DELIVERY_TYPE_UNSPECIFIED DeliveryType = 0
	DeliveryType_DELIVERY_TYPE_MOBILE      DeliveryType = 1
)

// Enum value maps for DeliveryType.
var (
	DeliveryType_name = map[int32]string{
		0: "DELIVERY_TYPE_UNSPECIFIED",
		1: "DELIVERY_TYPE_MOBILE",
	}
	DeliveryType_value = map[string]int32{
		"DELIVERY_TYPE_UNSPECIFIED": 0,
		"DELIVERY_TYPE_MOBILE":      1,
	}
)

func (x DeliveryType) Enum() *DeliveryType {
	p := new(DeliveryType)
	*p = x
	return p
}

func (x DeliveryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeliveryType) Descriptor() protoreflect.EnumDescriptor {
	return file_gametime_protos_mle_relevance_v1_service_proto_enumTypes[1].Descriptor()
}

func (DeliveryType) Type() protoreflect.EnumType {
	return &file_gametime_protos_mle_relevance_v1_service_proto_enumTypes[1]
}

func (x DeliveryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeliveryType.Descriptor instead.
func (DeliveryType) EnumDescriptor() ([]byte, []int) {
	return file_gametime_protos_mle_relevance_v1_service_proto_rawDescGZIP(), []int{1}
}

type Listing struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CompetitionPriceCents int32                  `protobuf:"varint,2,opt,name=competition_price_cents,json=competitionPriceCents,proto3" json:"competition_price_cents,omitempty"`
	CostCents             int32                  `protobuf:"varint,3,opt,name=cost_cents,json=costCents,proto3" json:"cost_cents,omitempty"`
	OurCostCents          int32                  `protobuf:"varint,4,opt,name=our_cost_cents,json=ourCostCents,proto3" json:"our_cost_cents,omitempty"`
	PrefeePriceCents      int32                  `protobuf:"varint,5,opt,name=prefee_price_cents,json=prefeePriceCents,proto3" json:"prefee_price_cents,omitempty"`
	PriceCents            int32                  `protobuf:"varint,6,opt,name=price_cents,json=priceCents,proto3" json:"price_cents,omitempty"`
	DisplaySavingsCents   int32                  `protobuf:"varint,7,opt,name=display_savings_cents,json=displaySavingsCents,proto3" json:"display_savings_cents,omitempty"`
	DisplaySavingsPercent int32                  `protobuf:"varint,8,opt,name=display_savings_percent,json=displaySavingsPercent,proto3" json:"display_savings_percent,omitempty"`
	SectionGroup          string                 `protobuf:"bytes,9,opt,name=section_group,json=sectionGroup,proto3" json:"section_group,omitempty"`
	Section               string                 `protobuf:"bytes,10,opt,name=section,proto3" json:"section,omitempty"`
	Row                   string                 `protobuf:"bytes,11,opt,name=row,proto3" json:"row,omitempty"`
	SeatQualityScore      float32                `protobuf:"fixed32,12,opt,name=seat_quality_score,json=seatQualityScore,proto3" json:"seat_quality_score,omitempty"`
	ValueScore            float32                `protobuf:"fixed32,13,opt,name=value_score,json=valueScore,proto3" json:"value_score,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *Listing) Reset() {
	*x = Listing{}
	mi := &file_gametime_protos_mle_relevance_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Listing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Listing) ProtoMessage() {}

func (x *Listing) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_relevance_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Listing.ProtoReflect.Descriptor instead.
func (*Listing) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_relevance_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *Listing) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Listing) GetCompetitionPriceCents() int32 {
	if x != nil {
		return x.CompetitionPriceCents
	}
	return 0
}

func (x *Listing) GetCostCents() int32 {
	if x != nil {
		return x.CostCents
	}
	return 0
}

func (x *Listing) GetOurCostCents() int32 {
	if x != nil {
		return x.OurCostCents
	}
	return 0
}

func (x *Listing) GetPrefeePriceCents() int32 {
	if x != nil {
		return x.PrefeePriceCents
	}
	return 0
}

func (x *Listing) GetPriceCents() int32 {
	if x != nil {
		return x.PriceCents
	}
	return 0
}

func (x *Listing) GetDisplaySavingsCents() int32 {
	if x != nil {
		return x.DisplaySavingsCents
	}
	return 0
}

func (x *Listing) GetDisplaySavingsPercent() int32 {
	if x != nil {
		return x.DisplaySavingsPercent
	}
	return 0
}

func (x *Listing) GetSectionGroup() string {
	if x != nil {
		return x.SectionGroup
	}
	return ""
}

func (x *Listing) GetSection() string {
	if x != nil {
		return x.Section
	}
	return ""
}

func (x *Listing) GetRow() string {
	if x != nil {
		return x.Row
	}
	return ""
}

func (x *Listing) GetSeatQualityScore() float32 {
	if x != nil {
		return x.SeatQualityScore
	}
	return 0
}

func (x *Listing) GetValueScore() float32 {
	if x != nil {
		return x.ValueScore
	}
	return 0
}

type ScoreRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Platform      Platform               `protobuf:"varint,1,opt,name=platform,proto3,enum=mle.relevance.v1.Platform" json:"platform,omitempty"`
	Listings      []*Listing             `protobuf:"bytes,2,rep,name=listings,proto3" json:"listings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScoreRequest) Reset() {
	*x = ScoreRequest{}
	mi := &file_gametime_protos_mle_relevance_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScoreRequest) ProtoMessage() {}

func (x *ScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_relevance_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScoreRequest.ProtoReflect.Descriptor instead.
func (*ScoreRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_relevance_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *ScoreRequest) GetPlatform() Platform {
	if x != nil {
		return x.Platform
	}
	return Platform_PLATFORM_UNSPECIFIED
}

func (x *ScoreRequest) GetListings() []*Listing {
	if x != nil {
		return x.Listings
	}
	return nil
}

type ScoreResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Scores        []float32              `protobuf:"fixed32,1,rep,packed,name=scores,proto3" json:"scores,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScoreResponse) Reset() {
	*x = ScoreResponse{}
	mi := &file_gametime_protos_mle_relevance_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScoreResponse) ProtoMessage() {}

func (x *ScoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mle_relevance_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScoreResponse.ProtoReflect.Descriptor instead.
func (*ScoreResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mle_relevance_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *ScoreResponse) GetScores() []float32 {
	if x != nil {
		return x.Scores
	}
	return nil
}

var File_gametime_protos_mle_relevance_v1_service_proto protoreflect.FileDescriptor

const file_gametime_protos_mle_relevance_v1_service_proto_rawDesc = "" +
	"\n" +
	".gametime_protos/mle/relevance/v1/service.proto\x12\x10mle.relevance.v1\"\xf1\x03\n" +
	"\aListing\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x126\n" +
	"\x17competition_price_cents\x18\x02 \x01(\x05R\x15competitionPriceCents\x12\x1d\n" +
	"\n" +
	"cost_cents\x18\x03 \x01(\x05R\tcostCents\x12$\n" +
	"\x0eour_cost_cents\x18\x04 \x01(\x05R\fourCostCents\x12,\n" +
	"\x12prefee_price_cents\x18\x05 \x01(\x05R\x10prefeePriceCents\x12\x1f\n" +
	"\vprice_cents\x18\x06 \x01(\x05R\n" +
	"priceCents\x122\n" +
	"\x15display_savings_cents\x18\a \x01(\x05R\x13displaySavingsCents\x126\n" +
	"\x17display_savings_percent\x18\b \x01(\x05R\x15displaySavingsPercent\x12#\n" +
	"\rsection_group\x18\t \x01(\tR\fsectionGroup\x12\x18\n" +
	"\asection\x18\n" +
	" \x01(\tR\asection\x12\x10\n" +
	"\x03row\x18\v \x01(\tR\x03row\x12,\n" +
	"\x12seat_quality_score\x18\f \x01(\x02R\x10seatQualityScore\x12\x1f\n" +
	"\vvalue_score\x18\r \x01(\x02R\n" +
	"valueScore\"}\n" +
	"\fScoreRequest\x126\n" +
	"\bplatform\x18\x01 \x01(\x0e2\x1a.mle.relevance.v1.PlatformR\bplatform\x125\n" +
	"\blistings\x18\x02 \x03(\v2\x19.mle.relevance.v1.ListingR\blistings\"'\n" +
	"\rScoreResponse\x12\x16\n" +
	"\x06scores\x18\x01 \x03(\x02R\x06scores*\x95\x01\n" +
	"\bPlatform\x12\x18\n" +
	"\x14PLATFORM_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17PLATFORM_MOBILE_APP_IOS\x10\x01\x12\x1f\n" +
	"\x1bPLATFORM_MOBILE_APP_ANDROID\x10\x02\x12\x17\n" +
	"\x13PLATFORM_MOBILE_WEB\x10\x03\x12\x18\n" +
	"\x14PLATFORM_DESKTOP_WEB\x10\x04*G\n" +
	"\fDeliveryType\x12\x1d\n" +
	"\x19DELIVERY_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14DELIVERY_TYPE_MOBILE\x10\x012^\n" +
	"\x10RelevanceService\x12J\n" +
	"\x05Score\x12\x1e.mle.relevance.v1.ScoreRequest\x1a\x1f.mle.relevance.v1.ScoreResponse\"\x00B\x9f\x01\n" +
	"\x14com.mle.relevance.v1B\fServiceProtoP\x01Z\x17mle/relevance/v1;protos\xa2\x02\x03MRX\xaa\x02\x10Mle.Relevance.V1\xca\x02\x10Mle\\Relevance\\V1\xe2\x02\x1cMle\\Relevance\\V1\\GPBMetadata\xea\x02\x12Mle::Relevance::V1b\x06proto3"

var (
	file_gametime_protos_mle_relevance_v1_service_proto_rawDescOnce sync.Once
	file_gametime_protos_mle_relevance_v1_service_proto_rawDescData []byte
)

func file_gametime_protos_mle_relevance_v1_service_proto_rawDescGZIP() []byte {
	file_gametime_protos_mle_relevance_v1_service_proto_rawDescOnce.Do(func() {
		file_gametime_protos_mle_relevance_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gametime_protos_mle_relevance_v1_service_proto_rawDesc), len(file_gametime_protos_mle_relevance_v1_service_proto_rawDesc)))
	})
	return file_gametime_protos_mle_relevance_v1_service_proto_rawDescData
}

var file_gametime_protos_mle_relevance_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_gametime_protos_mle_relevance_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_gametime_protos_mle_relevance_v1_service_proto_goTypes = []any{
	(Platform)(0),         // 0: mle.relevance.v1.Platform
	(DeliveryType)(0),     // 1: mle.relevance.v1.DeliveryType
	(*Listing)(nil),       // 2: mle.relevance.v1.Listing
	(*ScoreRequest)(nil),  // 3: mle.relevance.v1.ScoreRequest
	(*ScoreResponse)(nil), // 4: mle.relevance.v1.ScoreResponse
}
var file_gametime_protos_mle_relevance_v1_service_proto_depIdxs = []int32{
	0, // 0: mle.relevance.v1.ScoreRequest.platform:type_name -> mle.relevance.v1.Platform
	2, // 1: mle.relevance.v1.ScoreRequest.listings:type_name -> mle.relevance.v1.Listing
	3, // 2: mle.relevance.v1.RelevanceService.Score:input_type -> mle.relevance.v1.ScoreRequest
	4, // 3: mle.relevance.v1.RelevanceService.Score:output_type -> mle.relevance.v1.ScoreResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_gametime_protos_mle_relevance_v1_service_proto_init() }
func file_gametime_protos_mle_relevance_v1_service_proto_init() {
	if File_gametime_protos_mle_relevance_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gametime_protos_mle_relevance_v1_service_proto_rawDesc), len(file_gametime_protos_mle_relevance_v1_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gametime_protos_mle_relevance_v1_service_proto_goTypes,
		DependencyIndexes: file_gametime_protos_mle_relevance_v1_service_proto_depIdxs,
		EnumInfos:         file_gametime_protos_mle_relevance_v1_service_proto_enumTypes,
		MessageInfos:      file_gametime_protos_mle_relevance_v1_service_proto_msgTypes,
	}.Build()
	File_gametime_protos_mle_relevance_v1_service_proto = out.File
	file_gametime_protos_mle_relevance_v1_service_proto_goTypes = nil
	file_gametime_protos_mle_relevance_v1_service_proto_depIdxs = nil
}
