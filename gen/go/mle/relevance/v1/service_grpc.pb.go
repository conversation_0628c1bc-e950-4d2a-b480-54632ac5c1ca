// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: gametime_protos/mle/relevance/v1/service.proto

package protos

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RelevanceService_Score_FullMethodName = "/mle.relevance.v1.RelevanceService/Score"
)

// RelevanceServiceClient is the client API for RelevanceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RelevanceServiceClient interface {
	Score(ctx context.Context, in *ScoreRequest, opts ...grpc.CallOption) (*ScoreResponse, error)
}

type relevanceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRelevanceServiceClient(cc grpc.ClientConnInterface) RelevanceServiceClient {
	return &relevanceServiceClient{cc}
}

func (c *relevanceServiceClient) Score(ctx context.Context, in *ScoreRequest, opts ...grpc.CallOption) (*ScoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScoreResponse)
	err := c.cc.Invoke(ctx, RelevanceService_Score_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RelevanceServiceServer is the server API for RelevanceService service.
// All implementations must embed UnimplementedRelevanceServiceServer
// for forward compatibility.
type RelevanceServiceServer interface {
	Score(context.Context, *ScoreRequest) (*ScoreResponse, error)
	mustEmbedUnimplementedRelevanceServiceServer()
}

// UnimplementedRelevanceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRelevanceServiceServer struct{}

func (UnimplementedRelevanceServiceServer) Score(context.Context, *ScoreRequest) (*ScoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Score not implemented")
}
func (UnimplementedRelevanceServiceServer) mustEmbedUnimplementedRelevanceServiceServer() {}
func (UnimplementedRelevanceServiceServer) testEmbeddedByValue()                          {}

// UnsafeRelevanceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RelevanceServiceServer will
// result in compilation errors.
type UnsafeRelevanceServiceServer interface {
	mustEmbedUnimplementedRelevanceServiceServer()
}

func RegisterRelevanceServiceServer(s grpc.ServiceRegistrar, srv RelevanceServiceServer) {
	// If the following call pancis, it indicates UnimplementedRelevanceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RelevanceService_ServiceDesc, srv)
}

func _RelevanceService_Score_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RelevanceServiceServer).Score(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RelevanceService_Score_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RelevanceServiceServer).Score(ctx, req.(*ScoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RelevanceService_ServiceDesc is the grpc.ServiceDesc for RelevanceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RelevanceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mle.relevance.v1.RelevanceService",
	HandlerType: (*RelevanceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Score",
			Handler:    _RelevanceService_Score_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mle/relevance/v1/service.proto",
}
