// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: gametime_protos/mlp/baseline/v1/service.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InstanceType int32

const (
	InstanceType_INSTANCE_TYPE_UNSPECIFIED   InstanceType = 0
	InstanceType_INSTANCE_TYPE_M5_LARGE      InstanceType = 1
	InstanceType_INSTANCE_TYPE_M5_4XLARGE    InstanceType = 2
	InstanceType_INSTANCE_TYPE_M5_24XLARGE   InstanceType = 3
	InstanceType_INSTANCE_TYPE_G4DN_4XLARGE  InstanceType = 4
	InstanceType_INSTANCE_TYPE_G4DN_12XLARGE InstanceType = 5
)

// Enum value maps for InstanceType.
var (
	InstanceType_name = map[int32]string{
		0: "INSTANCE_TYPE_UNSPECIFIED",
		1: "INSTANCE_TYPE_M5_LARGE",
		2: "INSTANCE_TYPE_M5_4XLARGE",
		3: "INSTANCE_TYPE_M5_24XLARGE",
		4: "INSTANCE_TYPE_G4DN_4XLARGE",
		5: "INSTANCE_TYPE_G4DN_12XLARGE",
	}
	InstanceType_value = map[string]int32{
		"INSTANCE_TYPE_UNSPECIFIED":   0,
		"INSTANCE_TYPE_M5_LARGE":      1,
		"INSTANCE_TYPE_M5_4XLARGE":    2,
		"INSTANCE_TYPE_M5_24XLARGE":   3,
		"INSTANCE_TYPE_G4DN_4XLARGE":  4,
		"INSTANCE_TYPE_G4DN_12XLARGE": 5,
	}
)

func (x InstanceType) Enum() *InstanceType {
	p := new(InstanceType)
	*p = x
	return p
}

func (x InstanceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InstanceType) Descriptor() protoreflect.EnumDescriptor {
	return file_gametime_protos_mlp_baseline_v1_service_proto_enumTypes[0].Descriptor()
}

func (InstanceType) Type() protoreflect.EnumType {
	return &file_gametime_protos_mlp_baseline_v1_service_proto_enumTypes[0]
}

func (x InstanceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InstanceType.Descriptor instead.
func (InstanceType) EnumDescriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{0}
}

type JupyterStatus int32

const (
	JupyterStatus_JUPYTER_STATUS_UNSPECIFIED JupyterStatus = 0
	JupyterStatus_JUPYTER_STATUS_PENDING     JupyterStatus = 1
	JupyterStatus_JUPYTER_STATUS_RUNNING     JupyterStatus = 2
	JupyterStatus_JUPYTER_STATUS_STOPPING    JupyterStatus = 3
	JupyterStatus_JUPYTER_STATUS_STOPPED     JupyterStatus = 4
	JupyterStatus_JUPYTER_STATUS_FAILED      JupyterStatus = 5
)

// Enum value maps for JupyterStatus.
var (
	JupyterStatus_name = map[int32]string{
		0: "JUPYTER_STATUS_UNSPECIFIED",
		1: "JUPYTER_STATUS_PENDING",
		2: "JUPYTER_STATUS_RUNNING",
		3: "JUPYTER_STATUS_STOPPING",
		4: "JUPYTER_STATUS_STOPPED",
		5: "JUPYTER_STATUS_FAILED",
	}
	JupyterStatus_value = map[string]int32{
		"JUPYTER_STATUS_UNSPECIFIED": 0,
		"JUPYTER_STATUS_PENDING":     1,
		"JUPYTER_STATUS_RUNNING":     2,
		"JUPYTER_STATUS_STOPPING":    3,
		"JUPYTER_STATUS_STOPPED":     4,
		"JUPYTER_STATUS_FAILED":      5,
	}
)

func (x JupyterStatus) Enum() *JupyterStatus {
	p := new(JupyterStatus)
	*p = x
	return p
}

func (x JupyterStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JupyterStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gametime_protos_mlp_baseline_v1_service_proto_enumTypes[1].Descriptor()
}

func (JupyterStatus) Type() protoreflect.EnumType {
	return &file_gametime_protos_mlp_baseline_v1_service_proto_enumTypes[1]
}

func (x JupyterStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JupyterStatus.Descriptor instead.
func (JupyterStatus) EnumDescriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{1}
}

type JobStatus int32

const (
	JobStatus_JOB_STATUS_UNSPECIFIED JobStatus = 0
	JobStatus_JOB_STATUS_PENDING     JobStatus = 1
	JobStatus_JOB_STATUS_RUNNING     JobStatus = 2
	JobStatus_JOB_STATUS_SUCCEEDED   JobStatus = 3
	JobStatus_JOB_STATUS_FAILED      JobStatus = 4
	JobStatus_JOB_STATUS_CANCELED    JobStatus = 5
)

// Enum value maps for JobStatus.
var (
	JobStatus_name = map[int32]string{
		0: "JOB_STATUS_UNSPECIFIED",
		1: "JOB_STATUS_PENDING",
		2: "JOB_STATUS_RUNNING",
		3: "JOB_STATUS_SUCCEEDED",
		4: "JOB_STATUS_FAILED",
		5: "JOB_STATUS_CANCELED",
	}
	JobStatus_value = map[string]int32{
		"JOB_STATUS_UNSPECIFIED": 0,
		"JOB_STATUS_PENDING":     1,
		"JOB_STATUS_RUNNING":     2,
		"JOB_STATUS_SUCCEEDED":   3,
		"JOB_STATUS_FAILED":      4,
		"JOB_STATUS_CANCELED":    5,
	}
)

func (x JobStatus) Enum() *JobStatus {
	p := new(JobStatus)
	*p = x
	return p
}

func (x JobStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gametime_protos_mlp_baseline_v1_service_proto_enumTypes[2].Descriptor()
}

func (JobStatus) Type() protoreflect.EnumType {
	return &file_gametime_protos_mlp_baseline_v1_service_proto_enumTypes[2]
}

func (x JobStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobStatus.Descriptor instead.
func (JobStatus) EnumDescriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{2}
}

type JupyterServiceStartRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InstanceType  InstanceType           `protobuf:"varint,1,opt,name=instance_type,json=instanceType,proto3,enum=mlp.baseline.v1.InstanceType" json:"instance_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceStartRequest) Reset() {
	*x = JupyterServiceStartRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceStartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceStartRequest) ProtoMessage() {}

func (x *JupyterServiceStartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceStartRequest.ProtoReflect.Descriptor instead.
func (*JupyterServiceStartRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *JupyterServiceStartRequest) GetInstanceType() InstanceType {
	if x != nil {
		return x.InstanceType
	}
	return InstanceType_INSTANCE_TYPE_UNSPECIFIED
}

type JupyterServiceStartResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceStartResponse) Reset() {
	*x = JupyterServiceStartResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceStartResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceStartResponse) ProtoMessage() {}

func (x *JupyterServiceStartResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceStartResponse.ProtoReflect.Descriptor instead.
func (*JupyterServiceStartResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{1}
}

type JupyterServiceStopRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceStopRequest) Reset() {
	*x = JupyterServiceStopRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceStopRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceStopRequest) ProtoMessage() {}

func (x *JupyterServiceStopRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceStopRequest.ProtoReflect.Descriptor instead.
func (*JupyterServiceStopRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{2}
}

type JupyterServiceStopResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceStopResponse) Reset() {
	*x = JupyterServiceStopResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceStopResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceStopResponse) ProtoMessage() {}

func (x *JupyterServiceStopResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceStopResponse.ProtoReflect.Descriptor instead.
func (*JupyterServiceStopResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{3}
}

type JupyterServiceDescribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceDescribeRequest) Reset() {
	*x = JupyterServiceDescribeRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceDescribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceDescribeRequest) ProtoMessage() {}

func (x *JupyterServiceDescribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceDescribeRequest.ProtoReflect.Descriptor instead.
func (*JupyterServiceDescribeRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{4}
}

type JupyterServiceDescribeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InstanceType  InstanceType           `protobuf:"varint,1,opt,name=instance_type,json=instanceType,proto3,enum=mlp.baseline.v1.InstanceType" json:"instance_type,omitempty"`
	Status        JupyterStatus          `protobuf:"varint,2,opt,name=status,proto3,enum=mlp.baseline.v1.JupyterStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceDescribeResponse) Reset() {
	*x = JupyterServiceDescribeResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceDescribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceDescribeResponse) ProtoMessage() {}

func (x *JupyterServiceDescribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceDescribeResponse.ProtoReflect.Descriptor instead.
func (*JupyterServiceDescribeResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *JupyterServiceDescribeResponse) GetInstanceType() InstanceType {
	if x != nil {
		return x.InstanceType
	}
	return InstanceType_INSTANCE_TYPE_UNSPECIFIED
}

func (x *JupyterServiceDescribeResponse) GetStatus() JupyterStatus {
	if x != nil {
		return x.Status
	}
	return JupyterStatus_JUPYTER_STATUS_UNSPECIFIED
}

type JupyterServiceFetchUrlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceFetchUrlRequest) Reset() {
	*x = JupyterServiceFetchUrlRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceFetchUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceFetchUrlRequest) ProtoMessage() {}

func (x *JupyterServiceFetchUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceFetchUrlRequest.ProtoReflect.Descriptor instead.
func (*JupyterServiceFetchUrlRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{6}
}

type JupyterServiceFetchUrlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceFetchUrlResponse) Reset() {
	*x = JupyterServiceFetchUrlResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceFetchUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceFetchUrlResponse) ProtoMessage() {}

func (x *JupyterServiceFetchUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceFetchUrlResponse.ProtoReflect.Descriptor instead.
func (*JupyterServiceFetchUrlResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *JupyterServiceFetchUrlResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type JupyterServiceFetchConnectionInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceFetchConnectionInfoRequest) Reset() {
	*x = JupyterServiceFetchConnectionInfoRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceFetchConnectionInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceFetchConnectionInfoRequest) ProtoMessage() {}

func (x *JupyterServiceFetchConnectionInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceFetchConnectionInfoRequest.ProtoReflect.Descriptor instead.
func (*JupyterServiceFetchConnectionInfoRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{8}
}

type JupyterServiceFetchConnectionInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InstanceId    string                 `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	DomainId      string                 `protobuf:"bytes,2,opt,name=domain_id,json=domainId,proto3" json:"domain_id,omitempty"`
	SpaceName     string                 `protobuf:"bytes,3,opt,name=space_name,json=spaceName,proto3" json:"space_name,omitempty"`
	AppName       string                 `protobuf:"bytes,4,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	Host          string                 `protobuf:"bytes,5,opt,name=host,proto3" json:"host,omitempty"`
	JupyterPort   int32                  `protobuf:"varint,6,opt,name=jupyter_port,json=jupyterPort,proto3" json:"jupyter_port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JupyterServiceFetchConnectionInfoResponse) Reset() {
	*x = JupyterServiceFetchConnectionInfoResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JupyterServiceFetchConnectionInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JupyterServiceFetchConnectionInfoResponse) ProtoMessage() {}

func (x *JupyterServiceFetchConnectionInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JupyterServiceFetchConnectionInfoResponse.ProtoReflect.Descriptor instead.
func (*JupyterServiceFetchConnectionInfoResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *JupyterServiceFetchConnectionInfoResponse) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *JupyterServiceFetchConnectionInfoResponse) GetDomainId() string {
	if x != nil {
		return x.DomainId
	}
	return ""
}

func (x *JupyterServiceFetchConnectionInfoResponse) GetSpaceName() string {
	if x != nil {
		return x.SpaceName
	}
	return ""
}

func (x *JupyterServiceFetchConnectionInfoResponse) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *JupyterServiceFetchConnectionInfoResponse) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *JupyterServiceFetchConnectionInfoResponse) GetJupyterPort() int32 {
	if x != nil {
		return x.JupyterPort
	}
	return 0
}

type JobsServiceRunRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Family        string                 `protobuf:"bytes,1,opt,name=family,proto3" json:"family,omitempty"`
	InstanceType  InstanceType           `protobuf:"varint,2,opt,name=instance_type,json=instanceType,proto3,enum=mlp.baseline.v1.InstanceType" json:"instance_type,omitempty"`
	Parameters    map[string]string      `protobuf:"bytes,3,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	GitRef        string                 `protobuf:"bytes,4,opt,name=git_ref,json=gitRef,proto3" json:"git_ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceRunRequest) Reset() {
	*x = JobsServiceRunRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceRunRequest) ProtoMessage() {}

func (x *JobsServiceRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceRunRequest.ProtoReflect.Descriptor instead.
func (*JobsServiceRunRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *JobsServiceRunRequest) GetFamily() string {
	if x != nil {
		return x.Family
	}
	return ""
}

func (x *JobsServiceRunRequest) GetInstanceType() InstanceType {
	if x != nil {
		return x.InstanceType
	}
	return InstanceType_INSTANCE_TYPE_UNSPECIFIED
}

func (x *JobsServiceRunRequest) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *JobsServiceRunRequest) GetGitRef() string {
	if x != nil {
		return x.GitRef
	}
	return ""
}

type JobsServiceRunResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceRunResponse) Reset() {
	*x = JobsServiceRunResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceRunResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceRunResponse) ProtoMessage() {}

func (x *JobsServiceRunResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceRunResponse.ProtoReflect.Descriptor instead.
func (*JobsServiceRunResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *JobsServiceRunResponse) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type JobsServiceCancelRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceCancelRequest) Reset() {
	*x = JobsServiceCancelRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceCancelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceCancelRequest) ProtoMessage() {}

func (x *JobsServiceCancelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceCancelRequest.ProtoReflect.Descriptor instead.
func (*JobsServiceCancelRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *JobsServiceCancelRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type JobsServiceCancelResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceCancelResponse) Reset() {
	*x = JobsServiceCancelResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceCancelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceCancelResponse) ProtoMessage() {}

func (x *JobsServiceCancelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceCancelResponse.ProtoReflect.Descriptor instead.
func (*JobsServiceCancelResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{13}
}

type JobsServiceDescribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceDescribeRequest) Reset() {
	*x = JobsServiceDescribeRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceDescribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceDescribeRequest) ProtoMessage() {}

func (x *JobsServiceDescribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceDescribeRequest.ProtoReflect.Descriptor instead.
func (*JobsServiceDescribeRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{14}
}

func (x *JobsServiceDescribeRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type JobsServiceDescribeResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Ref               string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	Family            string                 `protobuf:"bytes,2,opt,name=family,proto3" json:"family,omitempty"`
	InstanceType      InstanceType           `protobuf:"varint,3,opt,name=instance_type,json=instanceType,proto3,enum=mlp.baseline.v1.InstanceType" json:"instance_type,omitempty"`
	Parameters        map[string]string      `protobuf:"bytes,4,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	GitSha            string                 `protobuf:"bytes,5,opt,name=git_sha,json=gitSha,proto3" json:"git_sha,omitempty"`
	RequestedAt       *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=requested_at,json=requestedAt,proto3" json:"requested_at,omitempty"`
	StartedAt         *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt           *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	Status            JobStatus              `protobuf:"varint,9,opt,name=status,proto3,enum=mlp.baseline.v1.JobStatus" json:"status,omitempty"`
	StatusReason      string                 `protobuf:"bytes,10,opt,name=status_reason,json=statusReason,proto3" json:"status_reason,omitempty"`
	RunBy             string                 `protobuf:"bytes,11,opt,name=run_by,json=runBy,proto3" json:"run_by,omitempty"`
	AssetManifest     *structpb.Struct       `protobuf:"bytes,12,opt,name=asset_manifest,json=assetManifest,proto3" json:"asset_manifest,omitempty"`
	EvaluationMetrics *structpb.Struct       `protobuf:"bytes,13,opt,name=evaluation_metrics,json=evaluationMetrics,proto3" json:"evaluation_metrics,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *JobsServiceDescribeResponse) Reset() {
	*x = JobsServiceDescribeResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceDescribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceDescribeResponse) ProtoMessage() {}

func (x *JobsServiceDescribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceDescribeResponse.ProtoReflect.Descriptor instead.
func (*JobsServiceDescribeResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{15}
}

func (x *JobsServiceDescribeResponse) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *JobsServiceDescribeResponse) GetFamily() string {
	if x != nil {
		return x.Family
	}
	return ""
}

func (x *JobsServiceDescribeResponse) GetInstanceType() InstanceType {
	if x != nil {
		return x.InstanceType
	}
	return InstanceType_INSTANCE_TYPE_UNSPECIFIED
}

func (x *JobsServiceDescribeResponse) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *JobsServiceDescribeResponse) GetGitSha() string {
	if x != nil {
		return x.GitSha
	}
	return ""
}

func (x *JobsServiceDescribeResponse) GetRequestedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RequestedAt
	}
	return nil
}

func (x *JobsServiceDescribeResponse) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *JobsServiceDescribeResponse) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

func (x *JobsServiceDescribeResponse) GetStatus() JobStatus {
	if x != nil {
		return x.Status
	}
	return JobStatus_JOB_STATUS_UNSPECIFIED
}

func (x *JobsServiceDescribeResponse) GetStatusReason() string {
	if x != nil {
		return x.StatusReason
	}
	return ""
}

func (x *JobsServiceDescribeResponse) GetRunBy() string {
	if x != nil {
		return x.RunBy
	}
	return ""
}

func (x *JobsServiceDescribeResponse) GetAssetManifest() *structpb.Struct {
	if x != nil {
		return x.AssetManifest
	}
	return nil
}

func (x *JobsServiceDescribeResponse) GetEvaluationMetrics() *structpb.Struct {
	if x != nil {
		return x.EvaluationMetrics
	}
	return nil
}

type JobListItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	Family        string                 `protobuf:"bytes,2,opt,name=family,proto3" json:"family,omitempty"`
	RequestedAt   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=requested_at,json=requestedAt,proto3" json:"requested_at,omitempty"`
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	EndedAt       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`
	Status        JobStatus              `protobuf:"varint,6,opt,name=status,proto3,enum=mlp.baseline.v1.JobStatus" json:"status,omitempty"`
	RunBy         string                 `protobuf:"bytes,7,opt,name=run_by,json=runBy,proto3" json:"run_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobListItem) Reset() {
	*x = JobListItem{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobListItem) ProtoMessage() {}

func (x *JobListItem) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobListItem.ProtoReflect.Descriptor instead.
func (*JobListItem) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{16}
}

func (x *JobListItem) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *JobListItem) GetFamily() string {
	if x != nil {
		return x.Family
	}
	return ""
}

func (x *JobListItem) GetRequestedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RequestedAt
	}
	return nil
}

func (x *JobListItem) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *JobListItem) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

func (x *JobListItem) GetStatus() JobStatus {
	if x != nil {
		return x.Status
	}
	return JobStatus_JOB_STATUS_UNSPECIFIED
}

func (x *JobListItem) GetRunBy() string {
	if x != nil {
		return x.RunBy
	}
	return ""
}

type JobsServiceListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Family        string                 `protobuf:"bytes,1,opt,name=family,proto3" json:"family,omitempty"`
	After         *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=after,proto3" json:"after,omitempty"`
	Before        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=before,proto3" json:"before,omitempty"`
	Ascending     bool                   `protobuf:"varint,4,opt,name=ascending,proto3" json:"ascending,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceListRequest) Reset() {
	*x = JobsServiceListRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceListRequest) ProtoMessage() {}

func (x *JobsServiceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceListRequest.ProtoReflect.Descriptor instead.
func (*JobsServiceListRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{17}
}

func (x *JobsServiceListRequest) GetFamily() string {
	if x != nil {
		return x.Family
	}
	return ""
}

func (x *JobsServiceListRequest) GetAfter() *timestamppb.Timestamp {
	if x != nil {
		return x.After
	}
	return nil
}

func (x *JobsServiceListRequest) GetBefore() *timestamppb.Timestamp {
	if x != nil {
		return x.Before
	}
	return nil
}

func (x *JobsServiceListRequest) GetAscending() bool {
	if x != nil {
		return x.Ascending
	}
	return false
}

type JobsServiceListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*JobListItem         `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceListResponse) Reset() {
	*x = JobsServiceListResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceListResponse) ProtoMessage() {}

func (x *JobsServiceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceListResponse.ProtoReflect.Descriptor instead.
func (*JobsServiceListResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{18}
}

func (x *JobsServiceListResponse) GetJobs() []*JobListItem {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type JobsServiceInitializeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceInitializeRequest) Reset() {
	*x = JobsServiceInitializeRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceInitializeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceInitializeRequest) ProtoMessage() {}

func (x *JobsServiceInitializeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceInitializeRequest.ProtoReflect.Descriptor instead.
func (*JobsServiceInitializeRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{19}
}

func (x *JobsServiceInitializeRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type JobsServiceInitializeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceInitializeResponse) Reset() {
	*x = JobsServiceInitializeResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceInitializeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceInitializeResponse) ProtoMessage() {}

func (x *JobsServiceInitializeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceInitializeResponse.ProtoReflect.Descriptor instead.
func (*JobsServiceInitializeResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{20}
}

type JobsServiceFinalizeRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Ref               string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	Status            JobStatus              `protobuf:"varint,2,opt,name=status,proto3,enum=mlp.baseline.v1.JobStatus" json:"status,omitempty"`
	StatusReason      string                 `protobuf:"bytes,3,opt,name=status_reason,json=statusReason,proto3" json:"status_reason,omitempty"`
	AssetManifest     *structpb.Struct       `protobuf:"bytes,4,opt,name=asset_manifest,json=assetManifest,proto3" json:"asset_manifest,omitempty"`
	EvaluationMetrics *structpb.Struct       `protobuf:"bytes,5,opt,name=evaluation_metrics,json=evaluationMetrics,proto3" json:"evaluation_metrics,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *JobsServiceFinalizeRequest) Reset() {
	*x = JobsServiceFinalizeRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceFinalizeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceFinalizeRequest) ProtoMessage() {}

func (x *JobsServiceFinalizeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceFinalizeRequest.ProtoReflect.Descriptor instead.
func (*JobsServiceFinalizeRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{21}
}

func (x *JobsServiceFinalizeRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *JobsServiceFinalizeRequest) GetStatus() JobStatus {
	if x != nil {
		return x.Status
	}
	return JobStatus_JOB_STATUS_UNSPECIFIED
}

func (x *JobsServiceFinalizeRequest) GetStatusReason() string {
	if x != nil {
		return x.StatusReason
	}
	return ""
}

func (x *JobsServiceFinalizeRequest) GetAssetManifest() *structpb.Struct {
	if x != nil {
		return x.AssetManifest
	}
	return nil
}

func (x *JobsServiceFinalizeRequest) GetEvaluationMetrics() *structpb.Struct {
	if x != nil {
		return x.EvaluationMetrics
	}
	return nil
}

type JobsServiceFinalizeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceFinalizeResponse) Reset() {
	*x = JobsServiceFinalizeResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceFinalizeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceFinalizeResponse) ProtoMessage() {}

func (x *JobsServiceFinalizeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceFinalizeResponse.ProtoReflect.Descriptor instead.
func (*JobsServiceFinalizeResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{22}
}

type JobsServiceFetchOutputUrlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceFetchOutputUrlRequest) Reset() {
	*x = JobsServiceFetchOutputUrlRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceFetchOutputUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceFetchOutputUrlRequest) ProtoMessage() {}

func (x *JobsServiceFetchOutputUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceFetchOutputUrlRequest.ProtoReflect.Descriptor instead.
func (*JobsServiceFetchOutputUrlRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{23}
}

func (x *JobsServiceFetchOutputUrlRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type JobsServiceFetchOutputUrlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceFetchOutputUrlResponse) Reset() {
	*x = JobsServiceFetchOutputUrlResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceFetchOutputUrlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceFetchOutputUrlResponse) ProtoMessage() {}

func (x *JobsServiceFetchOutputUrlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceFetchOutputUrlResponse.ProtoReflect.Descriptor instead.
func (*JobsServiceFetchOutputUrlResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{24}
}

func (x *JobsServiceFetchOutputUrlResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type JobsServiceFetchLogsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	Tail          int32                  `protobuf:"varint,2,opt,name=tail,proto3" json:"tail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceFetchLogsRequest) Reset() {
	*x = JobsServiceFetchLogsRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceFetchLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceFetchLogsRequest) ProtoMessage() {}

func (x *JobsServiceFetchLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceFetchLogsRequest.ProtoReflect.Descriptor instead.
func (*JobsServiceFetchLogsRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{25}
}

func (x *JobsServiceFetchLogsRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *JobsServiceFetchLogsRequest) GetTail() int32 {
	if x != nil {
		return x.Tail
	}
	return 0
}

type JobsServiceFetchLogsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobsServiceFetchLogsResponse) Reset() {
	*x = JobsServiceFetchLogsResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobsServiceFetchLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobsServiceFetchLogsResponse) ProtoMessage() {}

func (x *JobsServiceFetchLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobsServiceFetchLogsResponse.ProtoReflect.Descriptor instead.
func (*JobsServiceFetchLogsResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{26}
}

func (x *JobsServiceFetchLogsResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type SecretsServiceAddRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretsServiceAddRequest) Reset() {
	*x = SecretsServiceAddRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretsServiceAddRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsServiceAddRequest) ProtoMessage() {}

func (x *SecretsServiceAddRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsServiceAddRequest.ProtoReflect.Descriptor instead.
func (*SecretsServiceAddRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{27}
}

func (x *SecretsServiceAddRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SecretsServiceAddRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SecretsServiceAddRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type SecretsServiceAddResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretsServiceAddResponse) Reset() {
	*x = SecretsServiceAddResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretsServiceAddResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsServiceAddResponse) ProtoMessage() {}

func (x *SecretsServiceAddResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsServiceAddResponse.ProtoReflect.Descriptor instead.
func (*SecretsServiceAddResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{28}
}

type SecretsServiceUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretsServiceUpdateRequest) Reset() {
	*x = SecretsServiceUpdateRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretsServiceUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsServiceUpdateRequest) ProtoMessage() {}

func (x *SecretsServiceUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsServiceUpdateRequest.ProtoReflect.Descriptor instead.
func (*SecretsServiceUpdateRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{29}
}

func (x *SecretsServiceUpdateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SecretsServiceUpdateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SecretsServiceUpdateRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type SecretsServiceUpdateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretsServiceUpdateResponse) Reset() {
	*x = SecretsServiceUpdateResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretsServiceUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsServiceUpdateResponse) ProtoMessage() {}

func (x *SecretsServiceUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsServiceUpdateResponse.ProtoReflect.Descriptor instead.
func (*SecretsServiceUpdateResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{30}
}

type SecretsServiceRemoveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretsServiceRemoveRequest) Reset() {
	*x = SecretsServiceRemoveRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretsServiceRemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsServiceRemoveRequest) ProtoMessage() {}

func (x *SecretsServiceRemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsServiceRemoveRequest.ProtoReflect.Descriptor instead.
func (*SecretsServiceRemoveRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{31}
}

func (x *SecretsServiceRemoveRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SecretsServiceRemoveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretsServiceRemoveResponse) Reset() {
	*x = SecretsServiceRemoveResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretsServiceRemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsServiceRemoveResponse) ProtoMessage() {}

func (x *SecretsServiceRemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsServiceRemoveResponse.ProtoReflect.Descriptor instead.
func (*SecretsServiceRemoveResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{32}
}

type SecretListItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretListItem) Reset() {
	*x = SecretListItem{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretListItem) ProtoMessage() {}

func (x *SecretListItem) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretListItem.ProtoReflect.Descriptor instead.
func (*SecretListItem) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{33}
}

func (x *SecretListItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SecretListItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SecretListItem) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SecretListItem) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type SecretsServiceListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretsServiceListRequest) Reset() {
	*x = SecretsServiceListRequest{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretsServiceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsServiceListRequest) ProtoMessage() {}

func (x *SecretsServiceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsServiceListRequest.ProtoReflect.Descriptor instead.
func (*SecretsServiceListRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{34}
}

type SecretsServiceListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Secrets       []*SecretListItem      `protobuf:"bytes,1,rep,name=secrets,proto3" json:"secrets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretsServiceListResponse) Reset() {
	*x = SecretsServiceListResponse{}
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretsServiceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsServiceListResponse) ProtoMessage() {}

func (x *SecretsServiceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsServiceListResponse.ProtoReflect.Descriptor instead.
func (*SecretsServiceListResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP(), []int{35}
}

func (x *SecretsServiceListResponse) GetSecrets() []*SecretListItem {
	if x != nil {
		return x.Secrets
	}
	return nil
}

var File_gametime_protos_mlp_baseline_v1_service_proto protoreflect.FileDescriptor

const file_gametime_protos_mlp_baseline_v1_service_proto_rawDesc = "" +
	"\n" +
	"-gametime_protos/mlp/baseline/v1/service.proto\x12\x0fmlp.baseline.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"`\n" +
	"\x1aJupyterServiceStartRequest\x12B\n" +
	"\rinstance_type\x18\x01 \x01(\x0e2\x1d.mlp.baseline.v1.InstanceTypeR\finstanceType\"\x1d\n" +
	"\x1bJupyterServiceStartResponse\"\x1b\n" +
	"\x19JupyterServiceStopRequest\"\x1c\n" +
	"\x1aJupyterServiceStopResponse\"\x1f\n" +
	"\x1dJupyterServiceDescribeRequest\"\x9c\x01\n" +
	"\x1eJupyterServiceDescribeResponse\x12B\n" +
	"\rinstance_type\x18\x01 \x01(\x0e2\x1d.mlp.baseline.v1.InstanceTypeR\finstanceType\x126\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1e.mlp.baseline.v1.JupyterStatusR\x06status\"\x1f\n" +
	"\x1dJupyterServiceFetchUrlRequest\"2\n" +
	"\x1eJupyterServiceFetchUrlResponse\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\"*\n" +
	"(JupyterServiceFetchConnectionInfoRequest\"\xda\x01\n" +
	")JupyterServiceFetchConnectionInfoResponse\x12\x1f\n" +
	"\vinstance_id\x18\x01 \x01(\tR\n" +
	"instanceId\x12\x1b\n" +
	"\tdomain_id\x18\x02 \x01(\tR\bdomainId\x12\x1d\n" +
	"\n" +
	"space_name\x18\x03 \x01(\tR\tspaceName\x12\x19\n" +
	"\bapp_name\x18\x04 \x01(\tR\aappName\x12\x12\n" +
	"\x04host\x18\x05 \x01(\tR\x04host\x12!\n" +
	"\fjupyter_port\x18\x06 \x01(\x05R\vjupyterPort\"\xa3\x02\n" +
	"\x15JobsServiceRunRequest\x12\x16\n" +
	"\x06family\x18\x01 \x01(\tR\x06family\x12B\n" +
	"\rinstance_type\x18\x02 \x01(\x0e2\x1d.mlp.baseline.v1.InstanceTypeR\finstanceType\x12V\n" +
	"\n" +
	"parameters\x18\x03 \x03(\v26.mlp.baseline.v1.JobsServiceRunRequest.ParametersEntryR\n" +
	"parameters\x12\x17\n" +
	"\agit_ref\x18\x04 \x01(\tR\x06gitRef\x1a=\n" +
	"\x0fParametersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"*\n" +
	"\x16JobsServiceRunResponse\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\",\n" +
	"\x18JobsServiceCancelRequest\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\"\x1b\n" +
	"\x19JobsServiceCancelResponse\".\n" +
	"\x1aJobsServiceDescribeRequest\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\"\xea\x05\n" +
	"\x1bJobsServiceDescribeResponse\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\x12\x16\n" +
	"\x06family\x18\x02 \x01(\tR\x06family\x12B\n" +
	"\rinstance_type\x18\x03 \x01(\x0e2\x1d.mlp.baseline.v1.InstanceTypeR\finstanceType\x12\\\n" +
	"\n" +
	"parameters\x18\x04 \x03(\v2<.mlp.baseline.v1.JobsServiceDescribeResponse.ParametersEntryR\n" +
	"parameters\x12\x17\n" +
	"\agit_sha\x18\x05 \x01(\tR\x06gitSha\x12=\n" +
	"\frequested_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\vrequestedAt\x129\n" +
	"\n" +
	"started_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x125\n" +
	"\bended_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\aendedAt\x122\n" +
	"\x06status\x18\t \x01(\x0e2\x1a.mlp.baseline.v1.JobStatusR\x06status\x12#\n" +
	"\rstatus_reason\x18\n" +
	" \x01(\tR\fstatusReason\x12\x15\n" +
	"\x06run_by\x18\v \x01(\tR\x05runBy\x12>\n" +
	"\x0easset_manifest\x18\f \x01(\v2\x17.google.protobuf.StructR\rassetManifest\x12F\n" +
	"\x12evaluation_metrics\x18\r \x01(\v2\x17.google.protobuf.StructR\x11evaluationMetrics\x1a=\n" +
	"\x0fParametersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb3\x02\n" +
	"\vJobListItem\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\x12\x16\n" +
	"\x06family\x18\x02 \x01(\tR\x06family\x12=\n" +
	"\frequested_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\vrequestedAt\x129\n" +
	"\n" +
	"started_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x125\n" +
	"\bended_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendedAt\x122\n" +
	"\x06status\x18\x06 \x01(\x0e2\x1a.mlp.baseline.v1.JobStatusR\x06status\x12\x15\n" +
	"\x06run_by\x18\a \x01(\tR\x05runBy\"\xb4\x01\n" +
	"\x16JobsServiceListRequest\x12\x16\n" +
	"\x06family\x18\x01 \x01(\tR\x06family\x120\n" +
	"\x05after\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x05after\x122\n" +
	"\x06before\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x06before\x12\x1c\n" +
	"\tascending\x18\x04 \x01(\bR\tascending\"K\n" +
	"\x17JobsServiceListResponse\x120\n" +
	"\x04jobs\x18\x01 \x03(\v2\x1c.mlp.baseline.v1.JobListItemR\x04jobs\"0\n" +
	"\x1cJobsServiceInitializeRequest\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\"\x1f\n" +
	"\x1dJobsServiceInitializeResponse\"\x8f\x02\n" +
	"\x1aJobsServiceFinalizeRequest\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\x122\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1a.mlp.baseline.v1.JobStatusR\x06status\x12#\n" +
	"\rstatus_reason\x18\x03 \x01(\tR\fstatusReason\x12>\n" +
	"\x0easset_manifest\x18\x04 \x01(\v2\x17.google.protobuf.StructR\rassetManifest\x12F\n" +
	"\x12evaluation_metrics\x18\x05 \x01(\v2\x17.google.protobuf.StructR\x11evaluationMetrics\"\x1d\n" +
	"\x1bJobsServiceFinalizeResponse\"4\n" +
	" JobsServiceFetchOutputUrlRequest\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\"5\n" +
	"!JobsServiceFetchOutputUrlResponse\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\"C\n" +
	"\x1bJobsServiceFetchLogsRequest\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\x12\x12\n" +
	"\x04tail\x18\x02 \x01(\x05R\x04tail\"8\n" +
	"\x1cJobsServiceFetchLogsResponse\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"f\n" +
	"\x18SecretsServiceAddRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\"\x1b\n" +
	"\x19SecretsServiceAddResponse\"i\n" +
	"\x1bSecretsServiceUpdateRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\"\x1e\n" +
	"\x1cSecretsServiceUpdateResponse\"1\n" +
	"\x1bSecretsServiceRemoveRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x1e\n" +
	"\x1cSecretsServiceRemoveResponse\"\xbc\x01\n" +
	"\x0eSecretListItem\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"created_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x1b\n" +
	"\x19SecretsServiceListRequest\"W\n" +
	"\x1aSecretsServiceListResponse\x129\n" +
	"\asecrets\x18\x01 \x03(\v2\x1f.mlp.baseline.v1.SecretListItemR\asecrets*\xc7\x01\n" +
	"\fInstanceType\x12\x1d\n" +
	"\x19INSTANCE_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16INSTANCE_TYPE_M5_LARGE\x10\x01\x12\x1c\n" +
	"\x18INSTANCE_TYPE_M5_4XLARGE\x10\x02\x12\x1d\n" +
	"\x19INSTANCE_TYPE_M5_24XLARGE\x10\x03\x12\x1e\n" +
	"\x1aINSTANCE_TYPE_G4DN_4XLARGE\x10\x04\x12\x1f\n" +
	"\x1bINSTANCE_TYPE_G4DN_12XLARGE\x10\x05*\xbb\x01\n" +
	"\rJupyterStatus\x12\x1e\n" +
	"\x1aJUPYTER_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16JUPYTER_STATUS_PENDING\x10\x01\x12\x1a\n" +
	"\x16JUPYTER_STATUS_RUNNING\x10\x02\x12\x1b\n" +
	"\x17JUPYTER_STATUS_STOPPING\x10\x03\x12\x1a\n" +
	"\x16JUPYTER_STATUS_STOPPED\x10\x04\x12\x19\n" +
	"\x15JUPYTER_STATUS_FAILED\x10\x05*\xa1\x01\n" +
	"\tJobStatus\x12\x1a\n" +
	"\x16JOB_STATUS_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12JOB_STATUS_PENDING\x10\x01\x12\x16\n" +
	"\x12JOB_STATUS_RUNNING\x10\x02\x12\x18\n" +
	"\x14JOB_STATUS_SUCCEEDED\x10\x03\x12\x15\n" +
	"\x11JOB_STATUS_FAILED\x10\x04\x12\x17\n" +
	"\x13JOB_STATUS_CANCELED\x10\x052\xc8\x04\n" +
	"\x0eJupyterService\x12d\n" +
	"\x05Start\x12+.mlp.baseline.v1.JupyterServiceStartRequest\x1a,.mlp.baseline.v1.JupyterServiceStartResponse\"\x00\x12a\n" +
	"\x04Stop\x12*.mlp.baseline.v1.JupyterServiceStopRequest\x1a+.mlp.baseline.v1.JupyterServiceStopResponse\"\x00\x12m\n" +
	"\bDescribe\x12..mlp.baseline.v1.JupyterServiceDescribeRequest\x1a/.mlp.baseline.v1.JupyterServiceDescribeResponse\"\x00\x12m\n" +
	"\bFetchUrl\x12..mlp.baseline.v1.JupyterServiceFetchUrlRequest\x1a/.mlp.baseline.v1.JupyterServiceFetchUrlResponse\"\x00\x12\x8e\x01\n" +
	"\x13FetchConnectionInfo\x129.mlp.baseline.v1.JupyterServiceFetchConnectionInfoRequest\x1a:.mlp.baseline.v1.JupyterServiceFetchConnectionInfoResponse\"\x002\xcf\x06\n" +
	"\vJobsService\x12X\n" +
	"\x03Run\x12&.mlp.baseline.v1.JobsServiceRunRequest\x1a'.mlp.baseline.v1.JobsServiceRunResponse\"\x00\x12a\n" +
	"\x06Cancel\x12).mlp.baseline.v1.JobsServiceCancelRequest\x1a*.mlp.baseline.v1.JobsServiceCancelResponse\"\x00\x12g\n" +
	"\bDescribe\x12+.mlp.baseline.v1.JobsServiceDescribeRequest\x1a,.mlp.baseline.v1.JobsServiceDescribeResponse\"\x00\x12[\n" +
	"\x04List\x12'.mlp.baseline.v1.JobsServiceListRequest\x1a(.mlp.baseline.v1.JobsServiceListResponse\"\x00\x12m\n" +
	"\n" +
	"Initialize\x12-.mlp.baseline.v1.JobsServiceInitializeRequest\x1a..mlp.baseline.v1.JobsServiceInitializeResponse\"\x00\x12g\n" +
	"\bFinalize\x12+.mlp.baseline.v1.JobsServiceFinalizeRequest\x1a,.mlp.baseline.v1.JobsServiceFinalizeResponse\"\x00\x12y\n" +
	"\x0eFetchOutputUrl\x121.mlp.baseline.v1.JobsServiceFetchOutputUrlRequest\x1a2.mlp.baseline.v1.JobsServiceFetchOutputUrlResponse\"\x00\x12j\n" +
	"\tFetchLogs\x12,.mlp.baseline.v1.JobsServiceFetchLogsRequest\x1a-.mlp.baseline.v1.JobsServiceFetchLogsResponse\"\x002\xa5\x03\n" +
	"\x0eSecretsService\x12^\n" +
	"\x03Add\x12).mlp.baseline.v1.SecretsServiceAddRequest\x1a*.mlp.baseline.v1.SecretsServiceAddResponse\"\x00\x12g\n" +
	"\x06Update\x12,.mlp.baseline.v1.SecretsServiceUpdateRequest\x1a-.mlp.baseline.v1.SecretsServiceUpdateResponse\"\x00\x12g\n" +
	"\x06Remove\x12,.mlp.baseline.v1.SecretsServiceRemoveRequest\x1a-.mlp.baseline.v1.SecretsServiceRemoveResponse\"\x00\x12a\n" +
	"\x04List\x12*.mlp.baseline.v1.SecretsServiceListRequest\x1a+.mlp.baseline.v1.SecretsServiceListResponse\"\x00B\x99\x01\n" +
	"\x13com.mlp.baseline.v1B\fServiceProtoP\x01Z\x16mlp/baseline/v1;protos\xa2\x02\x03MBX\xaa\x02\x0fMlp.Baseline.V1\xca\x02\x0fMlp\\Baseline\\V1\xe2\x02\x1bMlp\\Baseline\\V1\\GPBMetadata\xea\x02\x11Mlp::Baseline::V1b\x06proto3"

var (
	file_gametime_protos_mlp_baseline_v1_service_proto_rawDescOnce sync.Once
	file_gametime_protos_mlp_baseline_v1_service_proto_rawDescData []byte
)

func file_gametime_protos_mlp_baseline_v1_service_proto_rawDescGZIP() []byte {
	file_gametime_protos_mlp_baseline_v1_service_proto_rawDescOnce.Do(func() {
		file_gametime_protos_mlp_baseline_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gametime_protos_mlp_baseline_v1_service_proto_rawDesc), len(file_gametime_protos_mlp_baseline_v1_service_proto_rawDesc)))
	})
	return file_gametime_protos_mlp_baseline_v1_service_proto_rawDescData
}

var file_gametime_protos_mlp_baseline_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_gametime_protos_mlp_baseline_v1_service_proto_goTypes = []any{
	(InstanceType)(0),                                 // 0: mlp.baseline.v1.InstanceType
	(JupyterStatus)(0),                                // 1: mlp.baseline.v1.JupyterStatus
	(JobStatus)(0),                                    // 2: mlp.baseline.v1.JobStatus
	(*JupyterServiceStartRequest)(nil),                // 3: mlp.baseline.v1.JupyterServiceStartRequest
	(*JupyterServiceStartResponse)(nil),               // 4: mlp.baseline.v1.JupyterServiceStartResponse
	(*JupyterServiceStopRequest)(nil),                 // 5: mlp.baseline.v1.JupyterServiceStopRequest
	(*JupyterServiceStopResponse)(nil),                // 6: mlp.baseline.v1.JupyterServiceStopResponse
	(*JupyterServiceDescribeRequest)(nil),             // 7: mlp.baseline.v1.JupyterServiceDescribeRequest
	(*JupyterServiceDescribeResponse)(nil),            // 8: mlp.baseline.v1.JupyterServiceDescribeResponse
	(*JupyterServiceFetchUrlRequest)(nil),             // 9: mlp.baseline.v1.JupyterServiceFetchUrlRequest
	(*JupyterServiceFetchUrlResponse)(nil),            // 10: mlp.baseline.v1.JupyterServiceFetchUrlResponse
	(*JupyterServiceFetchConnectionInfoRequest)(nil),  // 11: mlp.baseline.v1.JupyterServiceFetchConnectionInfoRequest
	(*JupyterServiceFetchConnectionInfoResponse)(nil), // 12: mlp.baseline.v1.JupyterServiceFetchConnectionInfoResponse
	(*JobsServiceRunRequest)(nil),                     // 13: mlp.baseline.v1.JobsServiceRunRequest
	(*JobsServiceRunResponse)(nil),                    // 14: mlp.baseline.v1.JobsServiceRunResponse
	(*JobsServiceCancelRequest)(nil),                  // 15: mlp.baseline.v1.JobsServiceCancelRequest
	(*JobsServiceCancelResponse)(nil),                 // 16: mlp.baseline.v1.JobsServiceCancelResponse
	(*JobsServiceDescribeRequest)(nil),                // 17: mlp.baseline.v1.JobsServiceDescribeRequest
	(*JobsServiceDescribeResponse)(nil),               // 18: mlp.baseline.v1.JobsServiceDescribeResponse
	(*JobListItem)(nil),                               // 19: mlp.baseline.v1.JobListItem
	(*JobsServiceListRequest)(nil),                    // 20: mlp.baseline.v1.JobsServiceListRequest
	(*JobsServiceListResponse)(nil),                   // 21: mlp.baseline.v1.JobsServiceListResponse
	(*JobsServiceInitializeRequest)(nil),              // 22: mlp.baseline.v1.JobsServiceInitializeRequest
	(*JobsServiceInitializeResponse)(nil),             // 23: mlp.baseline.v1.JobsServiceInitializeResponse
	(*JobsServiceFinalizeRequest)(nil),                // 24: mlp.baseline.v1.JobsServiceFinalizeRequest
	(*JobsServiceFinalizeResponse)(nil),               // 25: mlp.baseline.v1.JobsServiceFinalizeResponse
	(*JobsServiceFetchOutputUrlRequest)(nil),          // 26: mlp.baseline.v1.JobsServiceFetchOutputUrlRequest
	(*JobsServiceFetchOutputUrlResponse)(nil),         // 27: mlp.baseline.v1.JobsServiceFetchOutputUrlResponse
	(*JobsServiceFetchLogsRequest)(nil),               // 28: mlp.baseline.v1.JobsServiceFetchLogsRequest
	(*JobsServiceFetchLogsResponse)(nil),              // 29: mlp.baseline.v1.JobsServiceFetchLogsResponse
	(*SecretsServiceAddRequest)(nil),                  // 30: mlp.baseline.v1.SecretsServiceAddRequest
	(*SecretsServiceAddResponse)(nil),                 // 31: mlp.baseline.v1.SecretsServiceAddResponse
	(*SecretsServiceUpdateRequest)(nil),               // 32: mlp.baseline.v1.SecretsServiceUpdateRequest
	(*SecretsServiceUpdateResponse)(nil),              // 33: mlp.baseline.v1.SecretsServiceUpdateResponse
	(*SecretsServiceRemoveRequest)(nil),               // 34: mlp.baseline.v1.SecretsServiceRemoveRequest
	(*SecretsServiceRemoveResponse)(nil),              // 35: mlp.baseline.v1.SecretsServiceRemoveResponse
	(*SecretListItem)(nil),                            // 36: mlp.baseline.v1.SecretListItem
	(*SecretsServiceListRequest)(nil),                 // 37: mlp.baseline.v1.SecretsServiceListRequest
	(*SecretsServiceListResponse)(nil),                // 38: mlp.baseline.v1.SecretsServiceListResponse
	nil,                                               // 39: mlp.baseline.v1.JobsServiceRunRequest.ParametersEntry
	nil,                                               // 40: mlp.baseline.v1.JobsServiceDescribeResponse.ParametersEntry
	(*timestamppb.Timestamp)(nil),                     // 41: google.protobuf.Timestamp
	(*structpb.Struct)(nil),                           // 42: google.protobuf.Struct
}
var file_gametime_protos_mlp_baseline_v1_service_proto_depIdxs = []int32{
	0,  // 0: mlp.baseline.v1.JupyterServiceStartRequest.instance_type:type_name -> mlp.baseline.v1.InstanceType
	0,  // 1: mlp.baseline.v1.JupyterServiceDescribeResponse.instance_type:type_name -> mlp.baseline.v1.InstanceType
	1,  // 2: mlp.baseline.v1.JupyterServiceDescribeResponse.status:type_name -> mlp.baseline.v1.JupyterStatus
	0,  // 3: mlp.baseline.v1.JobsServiceRunRequest.instance_type:type_name -> mlp.baseline.v1.InstanceType
	39, // 4: mlp.baseline.v1.JobsServiceRunRequest.parameters:type_name -> mlp.baseline.v1.JobsServiceRunRequest.ParametersEntry
	0,  // 5: mlp.baseline.v1.JobsServiceDescribeResponse.instance_type:type_name -> mlp.baseline.v1.InstanceType
	40, // 6: mlp.baseline.v1.JobsServiceDescribeResponse.parameters:type_name -> mlp.baseline.v1.JobsServiceDescribeResponse.ParametersEntry
	41, // 7: mlp.baseline.v1.JobsServiceDescribeResponse.requested_at:type_name -> google.protobuf.Timestamp
	41, // 8: mlp.baseline.v1.JobsServiceDescribeResponse.started_at:type_name -> google.protobuf.Timestamp
	41, // 9: mlp.baseline.v1.JobsServiceDescribeResponse.ended_at:type_name -> google.protobuf.Timestamp
	2,  // 10: mlp.baseline.v1.JobsServiceDescribeResponse.status:type_name -> mlp.baseline.v1.JobStatus
	42, // 11: mlp.baseline.v1.JobsServiceDescribeResponse.asset_manifest:type_name -> google.protobuf.Struct
	42, // 12: mlp.baseline.v1.JobsServiceDescribeResponse.evaluation_metrics:type_name -> google.protobuf.Struct
	41, // 13: mlp.baseline.v1.JobListItem.requested_at:type_name -> google.protobuf.Timestamp
	41, // 14: mlp.baseline.v1.JobListItem.started_at:type_name -> google.protobuf.Timestamp
	41, // 15: mlp.baseline.v1.JobListItem.ended_at:type_name -> google.protobuf.Timestamp
	2,  // 16: mlp.baseline.v1.JobListItem.status:type_name -> mlp.baseline.v1.JobStatus
	41, // 17: mlp.baseline.v1.JobsServiceListRequest.after:type_name -> google.protobuf.Timestamp
	41, // 18: mlp.baseline.v1.JobsServiceListRequest.before:type_name -> google.protobuf.Timestamp
	19, // 19: mlp.baseline.v1.JobsServiceListResponse.jobs:type_name -> mlp.baseline.v1.JobListItem
	2,  // 20: mlp.baseline.v1.JobsServiceFinalizeRequest.status:type_name -> mlp.baseline.v1.JobStatus
	42, // 21: mlp.baseline.v1.JobsServiceFinalizeRequest.asset_manifest:type_name -> google.protobuf.Struct
	42, // 22: mlp.baseline.v1.JobsServiceFinalizeRequest.evaluation_metrics:type_name -> google.protobuf.Struct
	41, // 23: mlp.baseline.v1.SecretListItem.created_at:type_name -> google.protobuf.Timestamp
	41, // 24: mlp.baseline.v1.SecretListItem.updated_at:type_name -> google.protobuf.Timestamp
	36, // 25: mlp.baseline.v1.SecretsServiceListResponse.secrets:type_name -> mlp.baseline.v1.SecretListItem
	3,  // 26: mlp.baseline.v1.JupyterService.Start:input_type -> mlp.baseline.v1.JupyterServiceStartRequest
	5,  // 27: mlp.baseline.v1.JupyterService.Stop:input_type -> mlp.baseline.v1.JupyterServiceStopRequest
	7,  // 28: mlp.baseline.v1.JupyterService.Describe:input_type -> mlp.baseline.v1.JupyterServiceDescribeRequest
	9,  // 29: mlp.baseline.v1.JupyterService.FetchUrl:input_type -> mlp.baseline.v1.JupyterServiceFetchUrlRequest
	11, // 30: mlp.baseline.v1.JupyterService.FetchConnectionInfo:input_type -> mlp.baseline.v1.JupyterServiceFetchConnectionInfoRequest
	13, // 31: mlp.baseline.v1.JobsService.Run:input_type -> mlp.baseline.v1.JobsServiceRunRequest
	15, // 32: mlp.baseline.v1.JobsService.Cancel:input_type -> mlp.baseline.v1.JobsServiceCancelRequest
	17, // 33: mlp.baseline.v1.JobsService.Describe:input_type -> mlp.baseline.v1.JobsServiceDescribeRequest
	20, // 34: mlp.baseline.v1.JobsService.List:input_type -> mlp.baseline.v1.JobsServiceListRequest
	22, // 35: mlp.baseline.v1.JobsService.Initialize:input_type -> mlp.baseline.v1.JobsServiceInitializeRequest
	24, // 36: mlp.baseline.v1.JobsService.Finalize:input_type -> mlp.baseline.v1.JobsServiceFinalizeRequest
	26, // 37: mlp.baseline.v1.JobsService.FetchOutputUrl:input_type -> mlp.baseline.v1.JobsServiceFetchOutputUrlRequest
	28, // 38: mlp.baseline.v1.JobsService.FetchLogs:input_type -> mlp.baseline.v1.JobsServiceFetchLogsRequest
	30, // 39: mlp.baseline.v1.SecretsService.Add:input_type -> mlp.baseline.v1.SecretsServiceAddRequest
	32, // 40: mlp.baseline.v1.SecretsService.Update:input_type -> mlp.baseline.v1.SecretsServiceUpdateRequest
	34, // 41: mlp.baseline.v1.SecretsService.Remove:input_type -> mlp.baseline.v1.SecretsServiceRemoveRequest
	37, // 42: mlp.baseline.v1.SecretsService.List:input_type -> mlp.baseline.v1.SecretsServiceListRequest
	4,  // 43: mlp.baseline.v1.JupyterService.Start:output_type -> mlp.baseline.v1.JupyterServiceStartResponse
	6,  // 44: mlp.baseline.v1.JupyterService.Stop:output_type -> mlp.baseline.v1.JupyterServiceStopResponse
	8,  // 45: mlp.baseline.v1.JupyterService.Describe:output_type -> mlp.baseline.v1.JupyterServiceDescribeResponse
	10, // 46: mlp.baseline.v1.JupyterService.FetchUrl:output_type -> mlp.baseline.v1.JupyterServiceFetchUrlResponse
	12, // 47: mlp.baseline.v1.JupyterService.FetchConnectionInfo:output_type -> mlp.baseline.v1.JupyterServiceFetchConnectionInfoResponse
	14, // 48: mlp.baseline.v1.JobsService.Run:output_type -> mlp.baseline.v1.JobsServiceRunResponse
	16, // 49: mlp.baseline.v1.JobsService.Cancel:output_type -> mlp.baseline.v1.JobsServiceCancelResponse
	18, // 50: mlp.baseline.v1.JobsService.Describe:output_type -> mlp.baseline.v1.JobsServiceDescribeResponse
	21, // 51: mlp.baseline.v1.JobsService.List:output_type -> mlp.baseline.v1.JobsServiceListResponse
	23, // 52: mlp.baseline.v1.JobsService.Initialize:output_type -> mlp.baseline.v1.JobsServiceInitializeResponse
	25, // 53: mlp.baseline.v1.JobsService.Finalize:output_type -> mlp.baseline.v1.JobsServiceFinalizeResponse
	27, // 54: mlp.baseline.v1.JobsService.FetchOutputUrl:output_type -> mlp.baseline.v1.JobsServiceFetchOutputUrlResponse
	29, // 55: mlp.baseline.v1.JobsService.FetchLogs:output_type -> mlp.baseline.v1.JobsServiceFetchLogsResponse
	31, // 56: mlp.baseline.v1.SecretsService.Add:output_type -> mlp.baseline.v1.SecretsServiceAddResponse
	33, // 57: mlp.baseline.v1.SecretsService.Update:output_type -> mlp.baseline.v1.SecretsServiceUpdateResponse
	35, // 58: mlp.baseline.v1.SecretsService.Remove:output_type -> mlp.baseline.v1.SecretsServiceRemoveResponse
	38, // 59: mlp.baseline.v1.SecretsService.List:output_type -> mlp.baseline.v1.SecretsServiceListResponse
	43, // [43:60] is the sub-list for method output_type
	26, // [26:43] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_gametime_protos_mlp_baseline_v1_service_proto_init() }
func file_gametime_protos_mlp_baseline_v1_service_proto_init() {
	if File_gametime_protos_mlp_baseline_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gametime_protos_mlp_baseline_v1_service_proto_rawDesc), len(file_gametime_protos_mlp_baseline_v1_service_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_gametime_protos_mlp_baseline_v1_service_proto_goTypes,
		DependencyIndexes: file_gametime_protos_mlp_baseline_v1_service_proto_depIdxs,
		EnumInfos:         file_gametime_protos_mlp_baseline_v1_service_proto_enumTypes,
		MessageInfos:      file_gametime_protos_mlp_baseline_v1_service_proto_msgTypes,
	}.Build()
	File_gametime_protos_mlp_baseline_v1_service_proto = out.File
	file_gametime_protos_mlp_baseline_v1_service_proto_goTypes = nil
	file_gametime_protos_mlp_baseline_v1_service_proto_depIdxs = nil
}
