// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: gametime_protos/mlp/baseline/v1/service.proto

package protos

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	JupyterService_Start_FullMethodName               = "/mlp.baseline.v1.JupyterService/Start"
	JupyterService_Stop_FullMethodName                = "/mlp.baseline.v1.JupyterService/Stop"
	JupyterService_Describe_FullMethodName            = "/mlp.baseline.v1.JupyterService/Describe"
	JupyterService_FetchUrl_FullMethodName            = "/mlp.baseline.v1.JupyterService/FetchUrl"
	JupyterService_FetchConnectionInfo_FullMethodName = "/mlp.baseline.v1.JupyterService/FetchConnectionInfo"
)

// JupyterServiceClient is the client API for JupyterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JupyterServiceClient interface {
	Start(ctx context.Context, in *JupyterServiceStartRequest, opts ...grpc.CallOption) (*JupyterServiceStartResponse, error)
	Stop(ctx context.Context, in *JupyterServiceStopRequest, opts ...grpc.CallOption) (*JupyterServiceStopResponse, error)
	Describe(ctx context.Context, in *JupyterServiceDescribeRequest, opts ...grpc.CallOption) (*JupyterServiceDescribeResponse, error)
	FetchUrl(ctx context.Context, in *JupyterServiceFetchUrlRequest, opts ...grpc.CallOption) (*JupyterServiceFetchUrlResponse, error)
	FetchConnectionInfo(ctx context.Context, in *JupyterServiceFetchConnectionInfoRequest, opts ...grpc.CallOption) (*JupyterServiceFetchConnectionInfoResponse, error)
}

type jupyterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewJupyterServiceClient(cc grpc.ClientConnInterface) JupyterServiceClient {
	return &jupyterServiceClient{cc}
}

func (c *jupyterServiceClient) Start(ctx context.Context, in *JupyterServiceStartRequest, opts ...grpc.CallOption) (*JupyterServiceStartResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JupyterServiceStartResponse)
	err := c.cc.Invoke(ctx, JupyterService_Start_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jupyterServiceClient) Stop(ctx context.Context, in *JupyterServiceStopRequest, opts ...grpc.CallOption) (*JupyterServiceStopResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JupyterServiceStopResponse)
	err := c.cc.Invoke(ctx, JupyterService_Stop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jupyterServiceClient) Describe(ctx context.Context, in *JupyterServiceDescribeRequest, opts ...grpc.CallOption) (*JupyterServiceDescribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JupyterServiceDescribeResponse)
	err := c.cc.Invoke(ctx, JupyterService_Describe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jupyterServiceClient) FetchUrl(ctx context.Context, in *JupyterServiceFetchUrlRequest, opts ...grpc.CallOption) (*JupyterServiceFetchUrlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JupyterServiceFetchUrlResponse)
	err := c.cc.Invoke(ctx, JupyterService_FetchUrl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jupyterServiceClient) FetchConnectionInfo(ctx context.Context, in *JupyterServiceFetchConnectionInfoRequest, opts ...grpc.CallOption) (*JupyterServiceFetchConnectionInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JupyterServiceFetchConnectionInfoResponse)
	err := c.cc.Invoke(ctx, JupyterService_FetchConnectionInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JupyterServiceServer is the server API for JupyterService service.
// All implementations must embed UnimplementedJupyterServiceServer
// for forward compatibility.
type JupyterServiceServer interface {
	Start(context.Context, *JupyterServiceStartRequest) (*JupyterServiceStartResponse, error)
	Stop(context.Context, *JupyterServiceStopRequest) (*JupyterServiceStopResponse, error)
	Describe(context.Context, *JupyterServiceDescribeRequest) (*JupyterServiceDescribeResponse, error)
	FetchUrl(context.Context, *JupyterServiceFetchUrlRequest) (*JupyterServiceFetchUrlResponse, error)
	FetchConnectionInfo(context.Context, *JupyterServiceFetchConnectionInfoRequest) (*JupyterServiceFetchConnectionInfoResponse, error)
	mustEmbedUnimplementedJupyterServiceServer()
}

// UnimplementedJupyterServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedJupyterServiceServer struct{}

func (UnimplementedJupyterServiceServer) Start(context.Context, *JupyterServiceStartRequest) (*JupyterServiceStartResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Start not implemented")
}
func (UnimplementedJupyterServiceServer) Stop(context.Context, *JupyterServiceStopRequest) (*JupyterServiceStopResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Stop not implemented")
}
func (UnimplementedJupyterServiceServer) Describe(context.Context, *JupyterServiceDescribeRequest) (*JupyterServiceDescribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Describe not implemented")
}
func (UnimplementedJupyterServiceServer) FetchUrl(context.Context, *JupyterServiceFetchUrlRequest) (*JupyterServiceFetchUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchUrl not implemented")
}
func (UnimplementedJupyterServiceServer) FetchConnectionInfo(context.Context, *JupyterServiceFetchConnectionInfoRequest) (*JupyterServiceFetchConnectionInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchConnectionInfo not implemented")
}
func (UnimplementedJupyterServiceServer) mustEmbedUnimplementedJupyterServiceServer() {}
func (UnimplementedJupyterServiceServer) testEmbeddedByValue()                        {}

// UnsafeJupyterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JupyterServiceServer will
// result in compilation errors.
type UnsafeJupyterServiceServer interface {
	mustEmbedUnimplementedJupyterServiceServer()
}

func RegisterJupyterServiceServer(s grpc.ServiceRegistrar, srv JupyterServiceServer) {
	// If the following call pancis, it indicates UnimplementedJupyterServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&JupyterService_ServiceDesc, srv)
}

func _JupyterService_Start_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JupyterServiceStartRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JupyterServiceServer).Start(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JupyterService_Start_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JupyterServiceServer).Start(ctx, req.(*JupyterServiceStartRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JupyterService_Stop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JupyterServiceStopRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JupyterServiceServer).Stop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JupyterService_Stop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JupyterServiceServer).Stop(ctx, req.(*JupyterServiceStopRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JupyterService_Describe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JupyterServiceDescribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JupyterServiceServer).Describe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JupyterService_Describe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JupyterServiceServer).Describe(ctx, req.(*JupyterServiceDescribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JupyterService_FetchUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JupyterServiceFetchUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JupyterServiceServer).FetchUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JupyterService_FetchUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JupyterServiceServer).FetchUrl(ctx, req.(*JupyterServiceFetchUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JupyterService_FetchConnectionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JupyterServiceFetchConnectionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JupyterServiceServer).FetchConnectionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JupyterService_FetchConnectionInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JupyterServiceServer).FetchConnectionInfo(ctx, req.(*JupyterServiceFetchConnectionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// JupyterService_ServiceDesc is the grpc.ServiceDesc for JupyterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var JupyterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mlp.baseline.v1.JupyterService",
	HandlerType: (*JupyterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Start",
			Handler:    _JupyterService_Start_Handler,
		},
		{
			MethodName: "Stop",
			Handler:    _JupyterService_Stop_Handler,
		},
		{
			MethodName: "Describe",
			Handler:    _JupyterService_Describe_Handler,
		},
		{
			MethodName: "FetchUrl",
			Handler:    _JupyterService_FetchUrl_Handler,
		},
		{
			MethodName: "FetchConnectionInfo",
			Handler:    _JupyterService_FetchConnectionInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mlp/baseline/v1/service.proto",
}

const (
	JobsService_Run_FullMethodName            = "/mlp.baseline.v1.JobsService/Run"
	JobsService_Cancel_FullMethodName         = "/mlp.baseline.v1.JobsService/Cancel"
	JobsService_Describe_FullMethodName       = "/mlp.baseline.v1.JobsService/Describe"
	JobsService_List_FullMethodName           = "/mlp.baseline.v1.JobsService/List"
	JobsService_Initialize_FullMethodName     = "/mlp.baseline.v1.JobsService/Initialize"
	JobsService_Finalize_FullMethodName       = "/mlp.baseline.v1.JobsService/Finalize"
	JobsService_FetchOutputUrl_FullMethodName = "/mlp.baseline.v1.JobsService/FetchOutputUrl"
	JobsService_FetchLogs_FullMethodName      = "/mlp.baseline.v1.JobsService/FetchLogs"
)

// JobsServiceClient is the client API for JobsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JobsServiceClient interface {
	Run(ctx context.Context, in *JobsServiceRunRequest, opts ...grpc.CallOption) (*JobsServiceRunResponse, error)
	Cancel(ctx context.Context, in *JobsServiceCancelRequest, opts ...grpc.CallOption) (*JobsServiceCancelResponse, error)
	Describe(ctx context.Context, in *JobsServiceDescribeRequest, opts ...grpc.CallOption) (*JobsServiceDescribeResponse, error)
	List(ctx context.Context, in *JobsServiceListRequest, opts ...grpc.CallOption) (*JobsServiceListResponse, error)
	Initialize(ctx context.Context, in *JobsServiceInitializeRequest, opts ...grpc.CallOption) (*JobsServiceInitializeResponse, error)
	Finalize(ctx context.Context, in *JobsServiceFinalizeRequest, opts ...grpc.CallOption) (*JobsServiceFinalizeResponse, error)
	FetchOutputUrl(ctx context.Context, in *JobsServiceFetchOutputUrlRequest, opts ...grpc.CallOption) (*JobsServiceFetchOutputUrlResponse, error)
	FetchLogs(ctx context.Context, in *JobsServiceFetchLogsRequest, opts ...grpc.CallOption) (*JobsServiceFetchLogsResponse, error)
}

type jobsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewJobsServiceClient(cc grpc.ClientConnInterface) JobsServiceClient {
	return &jobsServiceClient{cc}
}

func (c *jobsServiceClient) Run(ctx context.Context, in *JobsServiceRunRequest, opts ...grpc.CallOption) (*JobsServiceRunResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobsServiceRunResponse)
	err := c.cc.Invoke(ctx, JobsService_Run_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) Cancel(ctx context.Context, in *JobsServiceCancelRequest, opts ...grpc.CallOption) (*JobsServiceCancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobsServiceCancelResponse)
	err := c.cc.Invoke(ctx, JobsService_Cancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) Describe(ctx context.Context, in *JobsServiceDescribeRequest, opts ...grpc.CallOption) (*JobsServiceDescribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobsServiceDescribeResponse)
	err := c.cc.Invoke(ctx, JobsService_Describe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) List(ctx context.Context, in *JobsServiceListRequest, opts ...grpc.CallOption) (*JobsServiceListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobsServiceListResponse)
	err := c.cc.Invoke(ctx, JobsService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) Initialize(ctx context.Context, in *JobsServiceInitializeRequest, opts ...grpc.CallOption) (*JobsServiceInitializeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobsServiceInitializeResponse)
	err := c.cc.Invoke(ctx, JobsService_Initialize_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) Finalize(ctx context.Context, in *JobsServiceFinalizeRequest, opts ...grpc.CallOption) (*JobsServiceFinalizeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobsServiceFinalizeResponse)
	err := c.cc.Invoke(ctx, JobsService_Finalize_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) FetchOutputUrl(ctx context.Context, in *JobsServiceFetchOutputUrlRequest, opts ...grpc.CallOption) (*JobsServiceFetchOutputUrlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobsServiceFetchOutputUrlResponse)
	err := c.cc.Invoke(ctx, JobsService_FetchOutputUrl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobsServiceClient) FetchLogs(ctx context.Context, in *JobsServiceFetchLogsRequest, opts ...grpc.CallOption) (*JobsServiceFetchLogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobsServiceFetchLogsResponse)
	err := c.cc.Invoke(ctx, JobsService_FetchLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JobsServiceServer is the server API for JobsService service.
// All implementations must embed UnimplementedJobsServiceServer
// for forward compatibility.
type JobsServiceServer interface {
	Run(context.Context, *JobsServiceRunRequest) (*JobsServiceRunResponse, error)
	Cancel(context.Context, *JobsServiceCancelRequest) (*JobsServiceCancelResponse, error)
	Describe(context.Context, *JobsServiceDescribeRequest) (*JobsServiceDescribeResponse, error)
	List(context.Context, *JobsServiceListRequest) (*JobsServiceListResponse, error)
	Initialize(context.Context, *JobsServiceInitializeRequest) (*JobsServiceInitializeResponse, error)
	Finalize(context.Context, *JobsServiceFinalizeRequest) (*JobsServiceFinalizeResponse, error)
	FetchOutputUrl(context.Context, *JobsServiceFetchOutputUrlRequest) (*JobsServiceFetchOutputUrlResponse, error)
	FetchLogs(context.Context, *JobsServiceFetchLogsRequest) (*JobsServiceFetchLogsResponse, error)
	mustEmbedUnimplementedJobsServiceServer()
}

// UnimplementedJobsServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedJobsServiceServer struct{}

func (UnimplementedJobsServiceServer) Run(context.Context, *JobsServiceRunRequest) (*JobsServiceRunResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Run not implemented")
}
func (UnimplementedJobsServiceServer) Cancel(context.Context, *JobsServiceCancelRequest) (*JobsServiceCancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}
func (UnimplementedJobsServiceServer) Describe(context.Context, *JobsServiceDescribeRequest) (*JobsServiceDescribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Describe not implemented")
}
func (UnimplementedJobsServiceServer) List(context.Context, *JobsServiceListRequest) (*JobsServiceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedJobsServiceServer) Initialize(context.Context, *JobsServiceInitializeRequest) (*JobsServiceInitializeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Initialize not implemented")
}
func (UnimplementedJobsServiceServer) Finalize(context.Context, *JobsServiceFinalizeRequest) (*JobsServiceFinalizeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Finalize not implemented")
}
func (UnimplementedJobsServiceServer) FetchOutputUrl(context.Context, *JobsServiceFetchOutputUrlRequest) (*JobsServiceFetchOutputUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchOutputUrl not implemented")
}
func (UnimplementedJobsServiceServer) FetchLogs(context.Context, *JobsServiceFetchLogsRequest) (*JobsServiceFetchLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchLogs not implemented")
}
func (UnimplementedJobsServiceServer) mustEmbedUnimplementedJobsServiceServer() {}
func (UnimplementedJobsServiceServer) testEmbeddedByValue()                     {}

// UnsafeJobsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JobsServiceServer will
// result in compilation errors.
type UnsafeJobsServiceServer interface {
	mustEmbedUnimplementedJobsServiceServer()
}

func RegisterJobsServiceServer(s grpc.ServiceRegistrar, srv JobsServiceServer) {
	// If the following call pancis, it indicates UnimplementedJobsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&JobsService_ServiceDesc, srv)
}

func _JobsService_Run_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobsServiceRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).Run(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_Run_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).Run(ctx, req.(*JobsServiceRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobsServiceCancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_Cancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).Cancel(ctx, req.(*JobsServiceCancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_Describe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobsServiceDescribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).Describe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_Describe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).Describe(ctx, req.(*JobsServiceDescribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobsServiceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).List(ctx, req.(*JobsServiceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_Initialize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobsServiceInitializeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).Initialize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_Initialize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).Initialize(ctx, req.(*JobsServiceInitializeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_Finalize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobsServiceFinalizeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).Finalize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_Finalize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).Finalize(ctx, req.(*JobsServiceFinalizeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_FetchOutputUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobsServiceFetchOutputUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).FetchOutputUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_FetchOutputUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).FetchOutputUrl(ctx, req.(*JobsServiceFetchOutputUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobsService_FetchLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobsServiceFetchLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobsServiceServer).FetchLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobsService_FetchLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobsServiceServer).FetchLogs(ctx, req.(*JobsServiceFetchLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// JobsService_ServiceDesc is the grpc.ServiceDesc for JobsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var JobsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mlp.baseline.v1.JobsService",
	HandlerType: (*JobsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Run",
			Handler:    _JobsService_Run_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _JobsService_Cancel_Handler,
		},
		{
			MethodName: "Describe",
			Handler:    _JobsService_Describe_Handler,
		},
		{
			MethodName: "List",
			Handler:    _JobsService_List_Handler,
		},
		{
			MethodName: "Initialize",
			Handler:    _JobsService_Initialize_Handler,
		},
		{
			MethodName: "Finalize",
			Handler:    _JobsService_Finalize_Handler,
		},
		{
			MethodName: "FetchOutputUrl",
			Handler:    _JobsService_FetchOutputUrl_Handler,
		},
		{
			MethodName: "FetchLogs",
			Handler:    _JobsService_FetchLogs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mlp/baseline/v1/service.proto",
}

const (
	SecretsService_Add_FullMethodName    = "/mlp.baseline.v1.SecretsService/Add"
	SecretsService_Update_FullMethodName = "/mlp.baseline.v1.SecretsService/Update"
	SecretsService_Remove_FullMethodName = "/mlp.baseline.v1.SecretsService/Remove"
	SecretsService_List_FullMethodName   = "/mlp.baseline.v1.SecretsService/List"
)

// SecretsServiceClient is the client API for SecretsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SecretsServiceClient interface {
	Add(ctx context.Context, in *SecretsServiceAddRequest, opts ...grpc.CallOption) (*SecretsServiceAddResponse, error)
	Update(ctx context.Context, in *SecretsServiceUpdateRequest, opts ...grpc.CallOption) (*SecretsServiceUpdateResponse, error)
	Remove(ctx context.Context, in *SecretsServiceRemoveRequest, opts ...grpc.CallOption) (*SecretsServiceRemoveResponse, error)
	List(ctx context.Context, in *SecretsServiceListRequest, opts ...grpc.CallOption) (*SecretsServiceListResponse, error)
}

type secretsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSecretsServiceClient(cc grpc.ClientConnInterface) SecretsServiceClient {
	return &secretsServiceClient{cc}
}

func (c *secretsServiceClient) Add(ctx context.Context, in *SecretsServiceAddRequest, opts ...grpc.CallOption) (*SecretsServiceAddResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SecretsServiceAddResponse)
	err := c.cc.Invoke(ctx, SecretsService_Add_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *secretsServiceClient) Update(ctx context.Context, in *SecretsServiceUpdateRequest, opts ...grpc.CallOption) (*SecretsServiceUpdateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SecretsServiceUpdateResponse)
	err := c.cc.Invoke(ctx, SecretsService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *secretsServiceClient) Remove(ctx context.Context, in *SecretsServiceRemoveRequest, opts ...grpc.CallOption) (*SecretsServiceRemoveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SecretsServiceRemoveResponse)
	err := c.cc.Invoke(ctx, SecretsService_Remove_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *secretsServiceClient) List(ctx context.Context, in *SecretsServiceListRequest, opts ...grpc.CallOption) (*SecretsServiceListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SecretsServiceListResponse)
	err := c.cc.Invoke(ctx, SecretsService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SecretsServiceServer is the server API for SecretsService service.
// All implementations must embed UnimplementedSecretsServiceServer
// for forward compatibility.
type SecretsServiceServer interface {
	Add(context.Context, *SecretsServiceAddRequest) (*SecretsServiceAddResponse, error)
	Update(context.Context, *SecretsServiceUpdateRequest) (*SecretsServiceUpdateResponse, error)
	Remove(context.Context, *SecretsServiceRemoveRequest) (*SecretsServiceRemoveResponse, error)
	List(context.Context, *SecretsServiceListRequest) (*SecretsServiceListResponse, error)
	mustEmbedUnimplementedSecretsServiceServer()
}

// UnimplementedSecretsServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSecretsServiceServer struct{}

func (UnimplementedSecretsServiceServer) Add(context.Context, *SecretsServiceAddRequest) (*SecretsServiceAddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Add not implemented")
}
func (UnimplementedSecretsServiceServer) Update(context.Context, *SecretsServiceUpdateRequest) (*SecretsServiceUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedSecretsServiceServer) Remove(context.Context, *SecretsServiceRemoveRequest) (*SecretsServiceRemoveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Remove not implemented")
}
func (UnimplementedSecretsServiceServer) List(context.Context, *SecretsServiceListRequest) (*SecretsServiceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedSecretsServiceServer) mustEmbedUnimplementedSecretsServiceServer() {}
func (UnimplementedSecretsServiceServer) testEmbeddedByValue()                        {}

// UnsafeSecretsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SecretsServiceServer will
// result in compilation errors.
type UnsafeSecretsServiceServer interface {
	mustEmbedUnimplementedSecretsServiceServer()
}

func RegisterSecretsServiceServer(s grpc.ServiceRegistrar, srv SecretsServiceServer) {
	// If the following call pancis, it indicates UnimplementedSecretsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SecretsService_ServiceDesc, srv)
}

func _SecretsService_Add_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SecretsServiceAddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServiceServer).Add(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecretsService_Add_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServiceServer).Add(ctx, req.(*SecretsServiceAddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecretsService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SecretsServiceUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecretsService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServiceServer).Update(ctx, req.(*SecretsServiceUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecretsService_Remove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SecretsServiceRemoveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServiceServer).Remove(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecretsService_Remove_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServiceServer).Remove(ctx, req.(*SecretsServiceRemoveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SecretsService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SecretsServiceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecretsServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SecretsService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecretsServiceServer).List(ctx, req.(*SecretsServiceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SecretsService_ServiceDesc is the grpc.ServiceDesc for SecretsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SecretsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mlp.baseline.v1.SecretsService",
	HandlerType: (*SecretsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Add",
			Handler:    _SecretsService_Add_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _SecretsService_Update_Handler,
		},
		{
			MethodName: "Remove",
			Handler:    _SecretsService_Remove_Handler,
		},
		{
			MethodName: "List",
			Handler:    _SecretsService_List_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mlp/baseline/v1/service.proto",
}
