// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: gametime_protos/mlp/prism/v1/service.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AggregationFunction int32

const (
	AggregationFunction_AGGREGATION_FUNCTION_UNSPECIFIED AggregationFunction = 0
	AggregationFunction_AGGREGATION_FUNCTION_COUNT       AggregationFunction = 1
	AggregationFunction_AGGREGATION_FUNCTION_AVG         AggregationFunction = 2
	AggregationFunction_AGGREGATION_FUNCTION_SUM         AggregationFunction = 3
	AggregationFunction_AGGREGATION_FUNCTION_STDDEV      AggregationFunction = 4
)

// Enum value maps for AggregationFunction.
var (
	AggregationFunction_name = map[int32]string{
		0: "AGGREGATION_FUNCTION_UNSPECIFIED",
		1: "AGGREGATION_FUNCTION_COUNT",
		2: "AGGREGATION_FUNCTION_AVG",
		3: "AGGREGATION_FUNCTION_SUM",
		4: "AGGREGATION_FUNCTION_STDDEV",
	}
	AggregationFunction_value = map[string]int32{
		"AGGREGATION_FUNCTION_UNSPECIFIED": 0,
		"AGGREGATION_FUNCTION_COUNT":       1,
		"AGGREGATION_FUNCTION_AVG":         2,
		"AGGREGATION_FUNCTION_SUM":         3,
		"AGGREGATION_FUNCTION_STDDEV":      4,
	}
)

func (x AggregationFunction) Enum() *AggregationFunction {
	p := new(AggregationFunction)
	*p = x
	return p
}

func (x AggregationFunction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AggregationFunction) Descriptor() protoreflect.EnumDescriptor {
	return file_gametime_protos_mlp_prism_v1_service_proto_enumTypes[0].Descriptor()
}

func (AggregationFunction) Type() protoreflect.EnumType {
	return &file_gametime_protos_mlp_prism_v1_service_proto_enumTypes[0]
}

func (x AggregationFunction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AggregationFunction.Descriptor instead.
func (AggregationFunction) EnumDescriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{0}
}

type FamilyStatus int32

const (
	FamilyStatus_FAMILY_STATUS_UNSPECIFIED         FamilyStatus = 0
	FamilyStatus_FAMILY_STATUS_INITIALIZING        FamilyStatus = 1
	FamilyStatus_FAMILY_STATUS_INITIALIZING_FAILED FamilyStatus = 2
	FamilyStatus_FAMILY_STATUS_BACKFILLING         FamilyStatus = 3
	FamilyStatus_FAMILY_STATUS_BACKFILLING_FAILED  FamilyStatus = 4
	FamilyStatus_FAMILY_STATUS_RUNNING             FamilyStatus = 5
	FamilyStatus_FAMILY_STATUS_RUNNING_FAILED      FamilyStatus = 6
	FamilyStatus_FAMILY_STATUS_DELETING            FamilyStatus = 7
	FamilyStatus_FAMILY_STATUS_DELETING_FAILED     FamilyStatus = 8
)

// Enum value maps for FamilyStatus.
var (
	FamilyStatus_name = map[int32]string{
		0: "FAMILY_STATUS_UNSPECIFIED",
		1: "FAMILY_STATUS_INITIALIZING",
		2: "FAMILY_STATUS_INITIALIZING_FAILED",
		3: "FAMILY_STATUS_BACKFILLING",
		4: "FAMILY_STATUS_BACKFILLING_FAILED",
		5: "FAMILY_STATUS_RUNNING",
		6: "FAMILY_STATUS_RUNNING_FAILED",
		7: "FAMILY_STATUS_DELETING",
		8: "FAMILY_STATUS_DELETING_FAILED",
	}
	FamilyStatus_value = map[string]int32{
		"FAMILY_STATUS_UNSPECIFIED":         0,
		"FAMILY_STATUS_INITIALIZING":        1,
		"FAMILY_STATUS_INITIALIZING_FAILED": 2,
		"FAMILY_STATUS_BACKFILLING":         3,
		"FAMILY_STATUS_BACKFILLING_FAILED":  4,
		"FAMILY_STATUS_RUNNING":             5,
		"FAMILY_STATUS_RUNNING_FAILED":      6,
		"FAMILY_STATUS_DELETING":            7,
		"FAMILY_STATUS_DELETING_FAILED":     8,
	}
)

func (x FamilyStatus) Enum() *FamilyStatus {
	p := new(FamilyStatus)
	*p = x
	return p
}

func (x FamilyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FamilyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gametime_protos_mlp_prism_v1_service_proto_enumTypes[1].Descriptor()
}

func (FamilyStatus) Type() protoreflect.EnumType {
	return &file_gametime_protos_mlp_prism_v1_service_proto_enumTypes[1]
}

func (x FamilyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FamilyStatus.Descriptor instead.
func (FamilyStatus) EnumDescriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{1}
}

type DatasetStatus int32

const (
	DatasetStatus_DATASET_STATUS_UNSPECIFIED            DatasetStatus = 0
	DatasetStatus_DATASET_STATUS_SPINE_UPLOADING        DatasetStatus = 1
	DatasetStatus_DATASET_STATUS_SPINE_UPLOADING_FAILED DatasetStatus = 2
	DatasetStatus_DATASET_STATUS_GENERATING             DatasetStatus = 3
	DatasetStatus_DATASET_STATUS_GENERATING_FAILED      DatasetStatus = 4
	DatasetStatus_DATASET_STATUS_READY                  DatasetStatus = 5
)

// Enum value maps for DatasetStatus.
var (
	DatasetStatus_name = map[int32]string{
		0: "DATASET_STATUS_UNSPECIFIED",
		1: "DATASET_STATUS_SPINE_UPLOADING",
		2: "DATASET_STATUS_SPINE_UPLOADING_FAILED",
		3: "DATASET_STATUS_GENERATING",
		4: "DATASET_STATUS_GENERATING_FAILED",
		5: "DATASET_STATUS_READY",
	}
	DatasetStatus_value = map[string]int32{
		"DATASET_STATUS_UNSPECIFIED":            0,
		"DATASET_STATUS_SPINE_UPLOADING":        1,
		"DATASET_STATUS_SPINE_UPLOADING_FAILED": 2,
		"DATASET_STATUS_GENERATING":             3,
		"DATASET_STATUS_GENERATING_FAILED":      4,
		"DATASET_STATUS_READY":                  5,
	}
)

func (x DatasetStatus) Enum() *DatasetStatus {
	p := new(DatasetStatus)
	*p = x
	return p
}

func (x DatasetStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DatasetStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gametime_protos_mlp_prism_v1_service_proto_enumTypes[2].Descriptor()
}

func (DatasetStatus) Type() protoreflect.EnumType {
	return &file_gametime_protos_mlp_prism_v1_service_proto_enumTypes[2]
}

func (x DatasetStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DatasetStatus.Descriptor instead.
func (DatasetStatus) EnumDescriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{2}
}

type BatchSourceConfig struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	Table                      string                 `protobuf:"bytes,1,opt,name=table,proto3" json:"table,omitempty"`
	LateArrivingDataLagSeconds int32                  `protobuf:"varint,2,opt,name=late_arriving_data_lag_seconds,json=lateArrivingDataLagSeconds,proto3" json:"late_arriving_data_lag_seconds,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *BatchSourceConfig) Reset() {
	*x = BatchSourceConfig{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchSourceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSourceConfig) ProtoMessage() {}

func (x *BatchSourceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSourceConfig.ProtoReflect.Descriptor instead.
func (*BatchSourceConfig) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *BatchSourceConfig) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *BatchSourceConfig) GetLateArrivingDataLagSeconds() int32 {
	if x != nil {
		return x.LateArrivingDataLagSeconds
	}
	return 0
}

type StreamingSourceConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Topic         string                 `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamingSourceConfig) Reset() {
	*x = StreamingSourceConfig{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamingSourceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamingSourceConfig) ProtoMessage() {}

func (x *StreamingSourceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamingSourceConfig.ProtoReflect.Descriptor instead.
func (*StreamingSourceConfig) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *StreamingSourceConfig) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type SourceConfig struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Batch         *BatchSourceConfig      `protobuf:"bytes,1,opt,name=batch,proto3" json:"batch,omitempty"`
	Streaming     *StreamingSourceConfig  `protobuf:"bytes,2,opt,name=streaming,proto3" json:"streaming,omitempty"`
	Query         *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SourceConfig) Reset() {
	*x = SourceConfig{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SourceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceConfig) ProtoMessage() {}

func (x *SourceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceConfig.ProtoReflect.Descriptor instead.
func (*SourceConfig) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *SourceConfig) GetBatch() *BatchSourceConfig {
	if x != nil {
		return x.Batch
	}
	return nil
}

func (x *SourceConfig) GetStreaming() *StreamingSourceConfig {
	if x != nil {
		return x.Streaming
	}
	return nil
}

func (x *SourceConfig) GetQuery() *wrapperspb.StringValue {
	if x != nil {
		return x.Query
	}
	return nil
}

type FeatureConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Column        string                 `protobuf:"bytes,1,opt,name=column,proto3" json:"column,omitempty"`
	Aggregations  []AggregationFunction  `protobuf:"varint,2,rep,packed,name=aggregations,proto3,enum=mlp.prism.v1.AggregationFunction" json:"aggregations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeatureConfig) Reset() {
	*x = FeatureConfig{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeatureConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureConfig) ProtoMessage() {}

func (x *FeatureConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureConfig.ProtoReflect.Descriptor instead.
func (*FeatureConfig) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *FeatureConfig) GetColumn() string {
	if x != nil {
		return x.Column
	}
	return ""
}

func (x *FeatureConfig) GetAggregations() []AggregationFunction {
	if x != nil {
		return x.Aggregations
	}
	return nil
}

type FamilyConfig struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Source            *SourceConfig          `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	IdColumn          string                 `protobuf:"bytes,2,opt,name=id_column,json=idColumn,proto3" json:"id_column,omitempty"`
	TimestampColumn   string                 `protobuf:"bytes,3,opt,name=timestamp_column,json=timestampColumn,proto3" json:"timestamp_column,omitempty"`
	IdentifierColumns []string               `protobuf:"bytes,4,rep,name=identifier_columns,json=identifierColumns,proto3" json:"identifier_columns,omitempty"`
	Features          []*FeatureConfig       `protobuf:"bytes,5,rep,name=features,proto3" json:"features,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *FamilyConfig) Reset() {
	*x = FamilyConfig{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamilyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamilyConfig) ProtoMessage() {}

func (x *FamilyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamilyConfig.ProtoReflect.Descriptor instead.
func (*FamilyConfig) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *FamilyConfig) GetSource() *SourceConfig {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *FamilyConfig) GetIdColumn() string {
	if x != nil {
		return x.IdColumn
	}
	return ""
}

func (x *FamilyConfig) GetTimestampColumn() string {
	if x != nil {
		return x.TimestampColumn
	}
	return ""
}

func (x *FamilyConfig) GetIdentifierColumns() []string {
	if x != nil {
		return x.IdentifierColumns
	}
	return nil
}

func (x *FamilyConfig) GetFeatures() []*FeatureConfig {
	if x != nil {
		return x.Features
	}
	return nil
}

type FamiliesServiceCreateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Config        *FamilyConfig          `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	Draft         bool                   `protobuf:"varint,3,opt,name=draft,proto3" json:"draft,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamiliesServiceCreateRequest) Reset() {
	*x = FamiliesServiceCreateRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamiliesServiceCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamiliesServiceCreateRequest) ProtoMessage() {}

func (x *FamiliesServiceCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamiliesServiceCreateRequest.ProtoReflect.Descriptor instead.
func (*FamiliesServiceCreateRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *FamiliesServiceCreateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FamiliesServiceCreateRequest) GetConfig() *FamilyConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *FamiliesServiceCreateRequest) GetDraft() bool {
	if x != nil {
		return x.Draft
	}
	return false
}

type FamiliesServiceCreateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamiliesServiceCreateResponse) Reset() {
	*x = FamiliesServiceCreateResponse{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamiliesServiceCreateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamiliesServiceCreateResponse) ProtoMessage() {}

func (x *FamiliesServiceCreateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamiliesServiceCreateResponse.ProtoReflect.Descriptor instead.
func (*FamiliesServiceCreateResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{6}
}

type FamiliesServiceDescribeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamiliesServiceDescribeRequest) Reset() {
	*x = FamiliesServiceDescribeRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamiliesServiceDescribeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamiliesServiceDescribeRequest) ProtoMessage() {}

func (x *FamiliesServiceDescribeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamiliesServiceDescribeRequest.ProtoReflect.Descriptor instead.
func (*FamiliesServiceDescribeRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *FamiliesServiceDescribeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Family struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Config        *FamilyConfig          `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	Draft         bool                   `protobuf:"varint,3,opt,name=draft,proto3" json:"draft,omitempty"`
	InsertedAt    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=inserted_at,json=insertedAt,proto3" json:"inserted_at,omitempty"`
	Status        FamilyStatus           `protobuf:"varint,5,opt,name=status,proto3,enum=mlp.prism.v1.FamilyStatus" json:"status,omitempty"`
	StatusDetail  string                 `protobuf:"bytes,6,opt,name=status_detail,json=statusDetail,proto3" json:"status_detail,omitempty"`
	LastFetchedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_fetched_at,json=lastFetchedAt,proto3" json:"last_fetched_at,omitempty"`
	Frontier      *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=frontier,proto3" json:"frontier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Family) Reset() {
	*x = Family{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Family) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Family) ProtoMessage() {}

func (x *Family) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Family.ProtoReflect.Descriptor instead.
func (*Family) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *Family) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Family) GetConfig() *FamilyConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *Family) GetDraft() bool {
	if x != nil {
		return x.Draft
	}
	return false
}

func (x *Family) GetInsertedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InsertedAt
	}
	return nil
}

func (x *Family) GetStatus() FamilyStatus {
	if x != nil {
		return x.Status
	}
	return FamilyStatus_FAMILY_STATUS_UNSPECIFIED
}

func (x *Family) GetStatusDetail() string {
	if x != nil {
		return x.StatusDetail
	}
	return ""
}

func (x *Family) GetLastFetchedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastFetchedAt
	}
	return nil
}

func (x *Family) GetFrontier() *timestamppb.Timestamp {
	if x != nil {
		return x.Frontier
	}
	return nil
}

type FamiliesServiceDescribeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Family        *Family                `protobuf:"bytes,1,opt,name=family,proto3" json:"family,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamiliesServiceDescribeResponse) Reset() {
	*x = FamiliesServiceDescribeResponse{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamiliesServiceDescribeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamiliesServiceDescribeResponse) ProtoMessage() {}

func (x *FamiliesServiceDescribeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamiliesServiceDescribeResponse.ProtoReflect.Descriptor instead.
func (*FamiliesServiceDescribeResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *FamiliesServiceDescribeResponse) GetFamily() *Family {
	if x != nil {
		return x.Family
	}
	return nil
}

type FamiliesServiceListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ExcludeDraft  bool                   `protobuf:"varint,1,opt,name=exclude_draft,json=excludeDraft,proto3" json:"exclude_draft,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamiliesServiceListRequest) Reset() {
	*x = FamiliesServiceListRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamiliesServiceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamiliesServiceListRequest) ProtoMessage() {}

func (x *FamiliesServiceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamiliesServiceListRequest.ProtoReflect.Descriptor instead.
func (*FamiliesServiceListRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *FamiliesServiceListRequest) GetExcludeDraft() bool {
	if x != nil {
		return x.ExcludeDraft
	}
	return false
}

type FamiliesServiceListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Families      []*Family              `protobuf:"bytes,1,rep,name=families,proto3" json:"families,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamiliesServiceListResponse) Reset() {
	*x = FamiliesServiceListResponse{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamiliesServiceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamiliesServiceListResponse) ProtoMessage() {}

func (x *FamiliesServiceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamiliesServiceListResponse.ProtoReflect.Descriptor instead.
func (*FamiliesServiceListResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *FamiliesServiceListResponse) GetFamilies() []*Family {
	if x != nil {
		return x.Families
	}
	return nil
}

type FamiliesServiceDeleteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamiliesServiceDeleteRequest) Reset() {
	*x = FamiliesServiceDeleteRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamiliesServiceDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamiliesServiceDeleteRequest) ProtoMessage() {}

func (x *FamiliesServiceDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamiliesServiceDeleteRequest.ProtoReflect.Descriptor instead.
func (*FamiliesServiceDeleteRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *FamiliesServiceDeleteRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type FamiliesServiceDeleteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FamiliesServiceDeleteResponse) Reset() {
	*x = FamiliesServiceDeleteResponse{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FamiliesServiceDeleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FamiliesServiceDeleteResponse) ProtoMessage() {}

func (x *FamiliesServiceDeleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FamiliesServiceDeleteResponse.ProtoReflect.Descriptor instead.
func (*FamiliesServiceDeleteResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{13}
}

type Window struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Weeks         int64                  `protobuf:"varint,1,opt,name=weeks,proto3" json:"weeks,omitempty"`
	Days          int64                  `protobuf:"varint,2,opt,name=days,proto3" json:"days,omitempty"`
	Hours         int64                  `protobuf:"varint,3,opt,name=hours,proto3" json:"hours,omitempty"`
	Minutes       int64                  `protobuf:"varint,4,opt,name=minutes,proto3" json:"minutes,omitempty"`
	Seconds       int64                  `protobuf:"varint,5,opt,name=seconds,proto3" json:"seconds,omitempty"`
	Milliseconds  int64                  `protobuf:"varint,6,opt,name=milliseconds,proto3" json:"milliseconds,omitempty"`
	Microseconds  int64                  `protobuf:"varint,7,opt,name=microseconds,proto3" json:"microseconds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Window) Reset() {
	*x = Window{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Window) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Window) ProtoMessage() {}

func (x *Window) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Window.ProtoReflect.Descriptor instead.
func (*Window) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{14}
}

func (x *Window) GetWeeks() int64 {
	if x != nil {
		return x.Weeks
	}
	return 0
}

func (x *Window) GetDays() int64 {
	if x != nil {
		return x.Days
	}
	return 0
}

func (x *Window) GetHours() int64 {
	if x != nil {
		return x.Hours
	}
	return 0
}

func (x *Window) GetMinutes() int64 {
	if x != nil {
		return x.Minutes
	}
	return 0
}

func (x *Window) GetSeconds() int64 {
	if x != nil {
		return x.Seconds
	}
	return 0
}

func (x *Window) GetMilliseconds() int64 {
	if x != nil {
		return x.Milliseconds
	}
	return 0
}

func (x *Window) GetMicroseconds() int64 {
	if x != nil {
		return x.Microseconds
	}
	return 0
}

type Aggregation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Function      AggregationFunction    `protobuf:"varint,1,opt,name=function,proto3,enum=mlp.prism.v1.AggregationFunction" json:"function,omitempty"`
	Window        *Window                `protobuf:"bytes,2,opt,name=window,proto3" json:"window,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Aggregation) Reset() {
	*x = Aggregation{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Aggregation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Aggregation) ProtoMessage() {}

func (x *Aggregation) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Aggregation.ProtoReflect.Descriptor instead.
func (*Aggregation) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{15}
}

func (x *Aggregation) GetFunction() AggregationFunction {
	if x != nil {
		return x.Function
	}
	return AggregationFunction_AGGREGATION_FUNCTION_UNSPECIFIED
}

func (x *Aggregation) GetWindow() *Window {
	if x != nil {
		return x.Window
	}
	return nil
}

type FeatureRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Family            string                 `protobuf:"bytes,1,opt,name=family,proto3" json:"family,omitempty"`
	Column            string                 `protobuf:"bytes,2,opt,name=column,proto3" json:"column,omitempty"`
	Aggregation       *Aggregation           `protobuf:"bytes,3,opt,name=aggregation,proto3" json:"aggregation,omitempty"`
	IdentifierMapping map[string]string      `protobuf:"bytes,4,rep,name=identifier_mapping,json=identifierMapping,proto3" json:"identifier_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *FeatureRequest) Reset() {
	*x = FeatureRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeatureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureRequest) ProtoMessage() {}

func (x *FeatureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureRequest.ProtoReflect.Descriptor instead.
func (*FeatureRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{16}
}

func (x *FeatureRequest) GetFamily() string {
	if x != nil {
		return x.Family
	}
	return ""
}

func (x *FeatureRequest) GetColumn() string {
	if x != nil {
		return x.Column
	}
	return ""
}

func (x *FeatureRequest) GetAggregation() *Aggregation {
	if x != nil {
		return x.Aggregation
	}
	return nil
}

func (x *FeatureRequest) GetIdentifierMapping() map[string]string {
	if x != nil {
		return x.IdentifierMapping
	}
	return nil
}

type OnlineFeaturesServiceFetchRequest struct {
	state           protoimpl.MessageState     `protogen:"open.v1"`
	FeatureRequests map[string]*FeatureRequest `protobuf:"bytes,1,rep,name=feature_requests,json=featureRequests,proto3" json:"feature_requests,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Identifiers     map[string]string          `protobuf:"bytes,2,rep,name=identifiers,proto3" json:"identifiers,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *OnlineFeaturesServiceFetchRequest) Reset() {
	*x = OnlineFeaturesServiceFetchRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnlineFeaturesServiceFetchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineFeaturesServiceFetchRequest) ProtoMessage() {}

func (x *OnlineFeaturesServiceFetchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineFeaturesServiceFetchRequest.ProtoReflect.Descriptor instead.
func (*OnlineFeaturesServiceFetchRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{17}
}

func (x *OnlineFeaturesServiceFetchRequest) GetFeatureRequests() map[string]*FeatureRequest {
	if x != nil {
		return x.FeatureRequests
	}
	return nil
}

func (x *OnlineFeaturesServiceFetchRequest) GetIdentifiers() map[string]string {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

type OnlineFeaturesServiceFetchResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Features      []byte                 `protobuf:"bytes,1,opt,name=features,proto3" json:"features,omitempty"`
	Frontier      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=frontier,proto3" json:"frontier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OnlineFeaturesServiceFetchResponse) Reset() {
	*x = OnlineFeaturesServiceFetchResponse{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnlineFeaturesServiceFetchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineFeaturesServiceFetchResponse) ProtoMessage() {}

func (x *OnlineFeaturesServiceFetchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineFeaturesServiceFetchResponse.ProtoReflect.Descriptor instead.
func (*OnlineFeaturesServiceFetchResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{18}
}

func (x *OnlineFeaturesServiceFetchResponse) GetFeatures() []byte {
	if x != nil {
		return x.Features
	}
	return nil
}

func (x *OnlineFeaturesServiceFetchResponse) GetFrontier() *timestamppb.Timestamp {
	if x != nil {
		return x.Frontier
	}
	return nil
}

type OfflineFeaturesServiceCreateDatasetRequest struct {
	state           protoimpl.MessageState     `protogen:"open.v1"`
	NumSpineChunks  int64                      `protobuf:"varint,1,opt,name=num_spine_chunks,json=numSpineChunks,proto3" json:"num_spine_chunks,omitempty"`
	FeatureRequests map[string]*FeatureRequest `protobuf:"bytes,2,rep,name=feature_requests,json=featureRequests,proto3" json:"feature_requests,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	SpineS3Uri      string                     `protobuf:"bytes,3,opt,name=spine_s3_uri,json=spineS3Uri,proto3" json:"spine_s3_uri,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *OfflineFeaturesServiceCreateDatasetRequest) Reset() {
	*x = OfflineFeaturesServiceCreateDatasetRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OfflineFeaturesServiceCreateDatasetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineFeaturesServiceCreateDatasetRequest) ProtoMessage() {}

func (x *OfflineFeaturesServiceCreateDatasetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineFeaturesServiceCreateDatasetRequest.ProtoReflect.Descriptor instead.
func (*OfflineFeaturesServiceCreateDatasetRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{19}
}

func (x *OfflineFeaturesServiceCreateDatasetRequest) GetNumSpineChunks() int64 {
	if x != nil {
		return x.NumSpineChunks
	}
	return 0
}

func (x *OfflineFeaturesServiceCreateDatasetRequest) GetFeatureRequests() map[string]*FeatureRequest {
	if x != nil {
		return x.FeatureRequests
	}
	return nil
}

func (x *OfflineFeaturesServiceCreateDatasetRequest) GetSpineS3Uri() string {
	if x != nil {
		return x.SpineS3Uri
	}
	return ""
}

type PresignedUrl struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Fields        string                 `protobuf:"bytes,2,opt,name=fields,proto3" json:"fields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresignedUrl) Reset() {
	*x = PresignedUrl{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresignedUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresignedUrl) ProtoMessage() {}

func (x *PresignedUrl) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresignedUrl.ProtoReflect.Descriptor instead.
func (*PresignedUrl) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{20}
}

func (x *PresignedUrl) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *PresignedUrl) GetFields() string {
	if x != nil {
		return x.Fields
	}
	return ""
}

type OfflineFeaturesServiceCreateDatasetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	PresignedUrls []*PresignedUrl        `protobuf:"bytes,2,rep,name=presigned_urls,json=presignedUrls,proto3" json:"presigned_urls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OfflineFeaturesServiceCreateDatasetResponse) Reset() {
	*x = OfflineFeaturesServiceCreateDatasetResponse{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OfflineFeaturesServiceCreateDatasetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineFeaturesServiceCreateDatasetResponse) ProtoMessage() {}

func (x *OfflineFeaturesServiceCreateDatasetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineFeaturesServiceCreateDatasetResponse.ProtoReflect.Descriptor instead.
func (*OfflineFeaturesServiceCreateDatasetResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{21}
}

func (x *OfflineFeaturesServiceCreateDatasetResponse) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *OfflineFeaturesServiceCreateDatasetResponse) GetPresignedUrls() []*PresignedUrl {
	if x != nil {
		return x.PresignedUrls
	}
	return nil
}

type OfflineFeaturesServiceDescribeDatasetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OfflineFeaturesServiceDescribeDatasetRequest) Reset() {
	*x = OfflineFeaturesServiceDescribeDatasetRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OfflineFeaturesServiceDescribeDatasetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineFeaturesServiceDescribeDatasetRequest) ProtoMessage() {}

func (x *OfflineFeaturesServiceDescribeDatasetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineFeaturesServiceDescribeDatasetRequest.ProtoReflect.Descriptor instead.
func (*OfflineFeaturesServiceDescribeDatasetRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{22}
}

func (x *OfflineFeaturesServiceDescribeDatasetRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type OfflineFeaturesServiceDescribeDatasetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        DatasetStatus          `protobuf:"varint,1,opt,name=status,proto3,enum=mlp.prism.v1.DatasetStatus" json:"status,omitempty"`
	StatusDetail  string                 `protobuf:"bytes,2,opt,name=status_detail,json=statusDetail,proto3" json:"status_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OfflineFeaturesServiceDescribeDatasetResponse) Reset() {
	*x = OfflineFeaturesServiceDescribeDatasetResponse{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OfflineFeaturesServiceDescribeDatasetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineFeaturesServiceDescribeDatasetResponse) ProtoMessage() {}

func (x *OfflineFeaturesServiceDescribeDatasetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineFeaturesServiceDescribeDatasetResponse.ProtoReflect.Descriptor instead.
func (*OfflineFeaturesServiceDescribeDatasetResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{23}
}

func (x *OfflineFeaturesServiceDescribeDatasetResponse) GetStatus() DatasetStatus {
	if x != nil {
		return x.Status
	}
	return DatasetStatus_DATASET_STATUS_UNSPECIFIED
}

func (x *OfflineFeaturesServiceDescribeDatasetResponse) GetStatusDetail() string {
	if x != nil {
		return x.StatusDetail
	}
	return ""
}

type OfflineFeaturesServiceFetchDatasetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OfflineFeaturesServiceFetchDatasetRequest) Reset() {
	*x = OfflineFeaturesServiceFetchDatasetRequest{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OfflineFeaturesServiceFetchDatasetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineFeaturesServiceFetchDatasetRequest) ProtoMessage() {}

func (x *OfflineFeaturesServiceFetchDatasetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineFeaturesServiceFetchDatasetRequest.ProtoReflect.Descriptor instead.
func (*OfflineFeaturesServiceFetchDatasetRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{24}
}

func (x *OfflineFeaturesServiceFetchDatasetRequest) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type OfflineFeaturesServiceFetchDatasetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ref           string                 `protobuf:"bytes,1,opt,name=ref,proto3" json:"ref,omitempty"`
	PresignedUrls []*PresignedUrl        `protobuf:"bytes,2,rep,name=presigned_urls,json=presignedUrls,proto3" json:"presigned_urls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OfflineFeaturesServiceFetchDatasetResponse) Reset() {
	*x = OfflineFeaturesServiceFetchDatasetResponse{}
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OfflineFeaturesServiceFetchDatasetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineFeaturesServiceFetchDatasetResponse) ProtoMessage() {}

func (x *OfflineFeaturesServiceFetchDatasetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_mlp_prism_v1_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineFeaturesServiceFetchDatasetResponse.ProtoReflect.Descriptor instead.
func (*OfflineFeaturesServiceFetchDatasetResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP(), []int{25}
}

func (x *OfflineFeaturesServiceFetchDatasetResponse) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *OfflineFeaturesServiceFetchDatasetResponse) GetPresignedUrls() []*PresignedUrl {
	if x != nil {
		return x.PresignedUrls
	}
	return nil
}

var File_gametime_protos_mlp_prism_v1_service_proto protoreflect.FileDescriptor

const file_gametime_protos_mlp_prism_v1_service_proto_rawDesc = "" +
	"\n" +
	"*gametime_protos/mlp/prism/v1/service.proto\x12\fmlp.prism.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/wrappers.proto\"m\n" +
	"\x11BatchSourceConfig\x12\x14\n" +
	"\x05table\x18\x01 \x01(\tR\x05table\x12B\n" +
	"\x1elate_arriving_data_lag_seconds\x18\x02 \x01(\x05R\x1alateArrivingDataLagSeconds\"-\n" +
	"\x15StreamingSourceConfig\x12\x14\n" +
	"\x05topic\x18\x01 \x01(\tR\x05topic\"\xbc\x01\n" +
	"\fSourceConfig\x125\n" +
	"\x05batch\x18\x01 \x01(\v2\x1f.mlp.prism.v1.BatchSourceConfigR\x05batch\x12A\n" +
	"\tstreaming\x18\x02 \x01(\v2#.mlp.prism.v1.StreamingSourceConfigR\tstreaming\x122\n" +
	"\x05query\x18\x03 \x01(\v2\x1c.google.protobuf.StringValueR\x05query\"n\n" +
	"\rFeatureConfig\x12\x16\n" +
	"\x06column\x18\x01 \x01(\tR\x06column\x12E\n" +
	"\faggregations\x18\x02 \x03(\x0e2!.mlp.prism.v1.AggregationFunctionR\faggregations\"\xf2\x01\n" +
	"\fFamilyConfig\x122\n" +
	"\x06source\x18\x01 \x01(\v2\x1a.mlp.prism.v1.SourceConfigR\x06source\x12\x1b\n" +
	"\tid_column\x18\x02 \x01(\tR\bidColumn\x12)\n" +
	"\x10timestamp_column\x18\x03 \x01(\tR\x0ftimestampColumn\x12-\n" +
	"\x12identifier_columns\x18\x04 \x03(\tR\x11identifierColumns\x127\n" +
	"\bfeatures\x18\x05 \x03(\v2\x1b.mlp.prism.v1.FeatureConfigR\bfeatures\"|\n" +
	"\x1cFamiliesServiceCreateRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x122\n" +
	"\x06config\x18\x02 \x01(\v2\x1a.mlp.prism.v1.FamilyConfigR\x06config\x12\x14\n" +
	"\x05draft\x18\x03 \x01(\bR\x05draft\"\x1f\n" +
	"\x1dFamiliesServiceCreateResponse\"4\n" +
	"\x1eFamiliesServiceDescribeRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\xf8\x02\n" +
	"\x06Family\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x122\n" +
	"\x06config\x18\x02 \x01(\v2\x1a.mlp.prism.v1.FamilyConfigR\x06config\x12\x14\n" +
	"\x05draft\x18\x03 \x01(\bR\x05draft\x12;\n" +
	"\vinserted_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"insertedAt\x122\n" +
	"\x06status\x18\x05 \x01(\x0e2\x1a.mlp.prism.v1.FamilyStatusR\x06status\x12#\n" +
	"\rstatus_detail\x18\x06 \x01(\tR\fstatusDetail\x12B\n" +
	"\x0flast_fetched_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\rlastFetchedAt\x126\n" +
	"\bfrontier\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\bfrontier\"O\n" +
	"\x1fFamiliesServiceDescribeResponse\x12,\n" +
	"\x06family\x18\x01 \x01(\v2\x14.mlp.prism.v1.FamilyR\x06family\"A\n" +
	"\x1aFamiliesServiceListRequest\x12#\n" +
	"\rexclude_draft\x18\x01 \x01(\bR\fexcludeDraft\"O\n" +
	"\x1bFamiliesServiceListResponse\x120\n" +
	"\bfamilies\x18\x01 \x03(\v2\x14.mlp.prism.v1.FamilyR\bfamilies\"2\n" +
	"\x1cFamiliesServiceDeleteRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x1f\n" +
	"\x1dFamiliesServiceDeleteResponse\"\xc4\x01\n" +
	"\x06Window\x12\x14\n" +
	"\x05weeks\x18\x01 \x01(\x03R\x05weeks\x12\x12\n" +
	"\x04days\x18\x02 \x01(\x03R\x04days\x12\x14\n" +
	"\x05hours\x18\x03 \x01(\x03R\x05hours\x12\x18\n" +
	"\aminutes\x18\x04 \x01(\x03R\aminutes\x12\x18\n" +
	"\aseconds\x18\x05 \x01(\x03R\aseconds\x12\"\n" +
	"\fmilliseconds\x18\x06 \x01(\x03R\fmilliseconds\x12\"\n" +
	"\fmicroseconds\x18\a \x01(\x03R\fmicroseconds\"z\n" +
	"\vAggregation\x12=\n" +
	"\bfunction\x18\x01 \x01(\x0e2!.mlp.prism.v1.AggregationFunctionR\bfunction\x12,\n" +
	"\x06window\x18\x02 \x01(\v2\x14.mlp.prism.v1.WindowR\x06window\"\xa7\x02\n" +
	"\x0eFeatureRequest\x12\x16\n" +
	"\x06family\x18\x01 \x01(\tR\x06family\x12\x16\n" +
	"\x06column\x18\x02 \x01(\tR\x06column\x12;\n" +
	"\vaggregation\x18\x03 \x01(\v2\x19.mlp.prism.v1.AggregationR\vaggregation\x12b\n" +
	"\x12identifier_mapping\x18\x04 \x03(\v23.mlp.prism.v1.FeatureRequest.IdentifierMappingEntryR\x11identifierMapping\x1aD\n" +
	"\x16IdentifierMappingEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x9a\x03\n" +
	"!OnlineFeaturesServiceFetchRequest\x12o\n" +
	"\x10feature_requests\x18\x01 \x03(\v2D.mlp.prism.v1.OnlineFeaturesServiceFetchRequest.FeatureRequestsEntryR\x0ffeatureRequests\x12b\n" +
	"\videntifiers\x18\x02 \x03(\<EMAIL>\videntifiers\x1a`\n" +
	"\x14FeatureRequestsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x122\n" +
	"\x05value\x18\x02 \x01(\v2\x1c.mlp.prism.v1.FeatureRequestR\x05value:\x028\x01\x1a>\n" +
	"\x10IdentifiersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"x\n" +
	"\"OnlineFeaturesServiceFetchResponse\x12\x1a\n" +
	"\bfeatures\x18\x01 \x01(\fR\bfeatures\x126\n" +
	"\bfrontier\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\bfrontier\"\xd4\x02\n" +
	"*OfflineFeaturesServiceCreateDatasetRequest\x12(\n" +
	"\x10num_spine_chunks\x18\x01 \x01(\x03R\x0enumSpineChunks\x12x\n" +
	"\x10feature_requests\x18\x02 \x03(\v2M.mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest.FeatureRequestsEntryR\x0ffeatureRequests\x12 \n" +
	"\fspine_s3_uri\x18\x03 \x01(\tR\n" +
	"spineS3Uri\x1a`\n" +
	"\x14FeatureRequestsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x122\n" +
	"\x05value\x18\x02 \x01(\v2\x1c.mlp.prism.v1.FeatureRequestR\x05value:\x028\x01\"8\n" +
	"\fPresignedUrl\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x16\n" +
	"\x06fields\x18\x02 \x01(\tR\x06fields\"\x82\x01\n" +
	"+OfflineFeaturesServiceCreateDatasetResponse\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\x12A\n" +
	"\x0epresigned_urls\x18\x02 \x03(\v2\x1a.mlp.prism.v1.PresignedUrlR\rpresignedUrls\"@\n" +
	",OfflineFeaturesServiceDescribeDatasetRequest\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\"\x89\x01\n" +
	"-OfflineFeaturesServiceDescribeDatasetResponse\x123\n" +
	"\x06status\x18\x01 \x01(\x0e2\x1b.mlp.prism.v1.DatasetStatusR\x06status\x12#\n" +
	"\rstatus_detail\x18\x02 \x01(\tR\fstatusDetail\"=\n" +
	")OfflineFeaturesServiceFetchDatasetRequest\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\"\x81\x01\n" +
	"*OfflineFeaturesServiceFetchDatasetResponse\x12\x10\n" +
	"\x03ref\x18\x01 \x01(\tR\x03ref\x12A\n" +
	"\x0epresigned_urls\x18\x02 \x03(\v2\x1a.mlp.prism.v1.PresignedUrlR\rpresignedUrls*\xb8\x01\n" +
	"\x13AggregationFunction\x12$\n" +
	" AGGREGATION_FUNCTION_UNSPECIFIED\x10\x00\x12\x1e\n" +
	"\x1aAGGREGATION_FUNCTION_COUNT\x10\x01\x12\x1c\n" +
	"\x18AGGREGATION_FUNCTION_AVG\x10\x02\x12\x1c\n" +
	"\x18AGGREGATION_FUNCTION_SUM\x10\x03\x12\x1f\n" +
	"\x1bAGGREGATION_FUNCTION_STDDEV\x10\x04*\xb5\x02\n" +
	"\fFamilyStatus\x12\x1d\n" +
	"\x19FAMILY_STATUS_UNSPECIFIED\x10\x00\x12\x1e\n" +
	"\x1aFAMILY_STATUS_INITIALIZING\x10\x01\x12%\n" +
	"!FAMILY_STATUS_INITIALIZING_FAILED\x10\x02\x12\x1d\n" +
	"\x19FAMILY_STATUS_BACKFILLING\x10\x03\x12$\n" +
	" FAMILY_STATUS_BACKFILLING_FAILED\x10\x04\x12\x19\n" +
	"\x15FAMILY_STATUS_RUNNING\x10\x05\x12 \n" +
	"\x1cFAMILY_STATUS_RUNNING_FAILED\x10\x06\x12\x1a\n" +
	"\x16FAMILY_STATUS_DELETING\x10\a\x12!\n" +
	"\x1dFAMILY_STATUS_DELETING_FAILED\x10\b*\xdd\x01\n" +
	"\rDatasetStatus\x12\x1e\n" +
	"\x1aDATASET_STATUS_UNSPECIFIED\x10\x00\x12\"\n" +
	"\x1eDATASET_STATUS_SPINE_UPLOADING\x10\x01\x12)\n" +
	"%DATASET_STATUS_SPINE_UPLOADING_FAILED\x10\x02\x12\x1d\n" +
	"\x19DATASET_STATUS_GENERATING\x10\x03\x12$\n" +
	" DATASET_STATUS_GENERATING_FAILED\x10\x04\x12\x18\n" +
	"\x14DATASET_STATUS_READY\x10\x052\xa5\x03\n" +
	"\x0fFamiliesService\x12c\n" +
	"\x06Create\x12*.mlp.prism.v1.FamiliesServiceCreateRequest\x1a+.mlp.prism.v1.FamiliesServiceCreateResponse\"\x00\x12c\n" +
	"\x06Delete\x12*.mlp.prism.v1.FamiliesServiceDeleteRequest\x1a+.mlp.prism.v1.FamiliesServiceDeleteResponse\"\x00\x12i\n" +
	"\bDescribe\x12,.mlp.prism.v1.FamiliesServiceDescribeRequest\x1a-.mlp.prism.v1.FamiliesServiceDescribeResponse\"\x00\x12]\n" +
	"\x04List\x12(.mlp.prism.v1.FamiliesServiceListRequest\x1a).mlp.prism.v1.FamiliesServiceListResponse\"\x002\x85\x01\n" +
	"\x15OnlineFeaturesService\x12l\n" +
	"\x05Fetch\x12/.mlp.prism.v1.OnlineFeaturesServiceFetchRequest\x1a0.mlp.prism.v1.OnlineFeaturesServiceFetchResponse\"\x002\xb6\x03\n" +
	"\x16OfflineFeaturesService\x12\x86\x01\n" +
	"\rCreateDataset\x128.mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest\x1a9.mlp.prism.v1.OfflineFeaturesServiceCreateDatasetResponse\"\x00\x12\x8c\x01\n" +
	"\x0fDescribeDataset\x12:.mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetRequest\x1a;.mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetResponse\"\x00\x12\x83\x01\n" +
	"\fFetchDataset\x127.mlp.prism.v1.OfflineFeaturesServiceFetchDatasetRequest\x1a8.mlp.prism.v1.OfflineFeaturesServiceFetchDatasetResponse\"\x00B\x87\x01\n" +
	"\x10com.mlp.prism.v1B\fServiceProtoP\x01Z\x13mlp/prism/v1;protos\xa2\x02\x03MPX\xaa\x02\fMlp.Prism.V1\xca\x02\fMlp\\Prism\\V1\xe2\x02\x18Mlp\\Prism\\V1\\GPBMetadata\xea\x02\x0eMlp::Prism::V1b\x06proto3"

var (
	file_gametime_protos_mlp_prism_v1_service_proto_rawDescOnce sync.Once
	file_gametime_protos_mlp_prism_v1_service_proto_rawDescData []byte
)

func file_gametime_protos_mlp_prism_v1_service_proto_rawDescGZIP() []byte {
	file_gametime_protos_mlp_prism_v1_service_proto_rawDescOnce.Do(func() {
		file_gametime_protos_mlp_prism_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gametime_protos_mlp_prism_v1_service_proto_rawDesc), len(file_gametime_protos_mlp_prism_v1_service_proto_rawDesc)))
	})
	return file_gametime_protos_mlp_prism_v1_service_proto_rawDescData
}

var file_gametime_protos_mlp_prism_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_gametime_protos_mlp_prism_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_gametime_protos_mlp_prism_v1_service_proto_goTypes = []any{
	(AggregationFunction)(0),                              // 0: mlp.prism.v1.AggregationFunction
	(FamilyStatus)(0),                                     // 1: mlp.prism.v1.FamilyStatus
	(DatasetStatus)(0),                                    // 2: mlp.prism.v1.DatasetStatus
	(*BatchSourceConfig)(nil),                             // 3: mlp.prism.v1.BatchSourceConfig
	(*StreamingSourceConfig)(nil),                         // 4: mlp.prism.v1.StreamingSourceConfig
	(*SourceConfig)(nil),                                  // 5: mlp.prism.v1.SourceConfig
	(*FeatureConfig)(nil),                                 // 6: mlp.prism.v1.FeatureConfig
	(*FamilyConfig)(nil),                                  // 7: mlp.prism.v1.FamilyConfig
	(*FamiliesServiceCreateRequest)(nil),                  // 8: mlp.prism.v1.FamiliesServiceCreateRequest
	(*FamiliesServiceCreateResponse)(nil),                 // 9: mlp.prism.v1.FamiliesServiceCreateResponse
	(*FamiliesServiceDescribeRequest)(nil),                // 10: mlp.prism.v1.FamiliesServiceDescribeRequest
	(*Family)(nil),                                        // 11: mlp.prism.v1.Family
	(*FamiliesServiceDescribeResponse)(nil),               // 12: mlp.prism.v1.FamiliesServiceDescribeResponse
	(*FamiliesServiceListRequest)(nil),                    // 13: mlp.prism.v1.FamiliesServiceListRequest
	(*FamiliesServiceListResponse)(nil),                   // 14: mlp.prism.v1.FamiliesServiceListResponse
	(*FamiliesServiceDeleteRequest)(nil),                  // 15: mlp.prism.v1.FamiliesServiceDeleteRequest
	(*FamiliesServiceDeleteResponse)(nil),                 // 16: mlp.prism.v1.FamiliesServiceDeleteResponse
	(*Window)(nil),                                        // 17: mlp.prism.v1.Window
	(*Aggregation)(nil),                                   // 18: mlp.prism.v1.Aggregation
	(*FeatureRequest)(nil),                                // 19: mlp.prism.v1.FeatureRequest
	(*OnlineFeaturesServiceFetchRequest)(nil),             // 20: mlp.prism.v1.OnlineFeaturesServiceFetchRequest
	(*OnlineFeaturesServiceFetchResponse)(nil),            // 21: mlp.prism.v1.OnlineFeaturesServiceFetchResponse
	(*OfflineFeaturesServiceCreateDatasetRequest)(nil),    // 22: mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest
	(*PresignedUrl)(nil),                                  // 23: mlp.prism.v1.PresignedUrl
	(*OfflineFeaturesServiceCreateDatasetResponse)(nil),   // 24: mlp.prism.v1.OfflineFeaturesServiceCreateDatasetResponse
	(*OfflineFeaturesServiceDescribeDatasetRequest)(nil),  // 25: mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetRequest
	(*OfflineFeaturesServiceDescribeDatasetResponse)(nil), // 26: mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetResponse
	(*OfflineFeaturesServiceFetchDatasetRequest)(nil),     // 27: mlp.prism.v1.OfflineFeaturesServiceFetchDatasetRequest
	(*OfflineFeaturesServiceFetchDatasetResponse)(nil),    // 28: mlp.prism.v1.OfflineFeaturesServiceFetchDatasetResponse
	nil,                            // 29: mlp.prism.v1.FeatureRequest.IdentifierMappingEntry
	nil,                            // 30: mlp.prism.v1.OnlineFeaturesServiceFetchRequest.FeatureRequestsEntry
	nil,                            // 31: mlp.prism.v1.OnlineFeaturesServiceFetchRequest.IdentifiersEntry
	nil,                            // 32: mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest.FeatureRequestsEntry
	(*wrapperspb.StringValue)(nil), // 33: google.protobuf.StringValue
	(*timestamppb.Timestamp)(nil),  // 34: google.protobuf.Timestamp
}
var file_gametime_protos_mlp_prism_v1_service_proto_depIdxs = []int32{
	3,  // 0: mlp.prism.v1.SourceConfig.batch:type_name -> mlp.prism.v1.BatchSourceConfig
	4,  // 1: mlp.prism.v1.SourceConfig.streaming:type_name -> mlp.prism.v1.StreamingSourceConfig
	33, // 2: mlp.prism.v1.SourceConfig.query:type_name -> google.protobuf.StringValue
	0,  // 3: mlp.prism.v1.FeatureConfig.aggregations:type_name -> mlp.prism.v1.AggregationFunction
	5,  // 4: mlp.prism.v1.FamilyConfig.source:type_name -> mlp.prism.v1.SourceConfig
	6,  // 5: mlp.prism.v1.FamilyConfig.features:type_name -> mlp.prism.v1.FeatureConfig
	7,  // 6: mlp.prism.v1.FamiliesServiceCreateRequest.config:type_name -> mlp.prism.v1.FamilyConfig
	7,  // 7: mlp.prism.v1.Family.config:type_name -> mlp.prism.v1.FamilyConfig
	34, // 8: mlp.prism.v1.Family.inserted_at:type_name -> google.protobuf.Timestamp
	1,  // 9: mlp.prism.v1.Family.status:type_name -> mlp.prism.v1.FamilyStatus
	34, // 10: mlp.prism.v1.Family.last_fetched_at:type_name -> google.protobuf.Timestamp
	34, // 11: mlp.prism.v1.Family.frontier:type_name -> google.protobuf.Timestamp
	11, // 12: mlp.prism.v1.FamiliesServiceDescribeResponse.family:type_name -> mlp.prism.v1.Family
	11, // 13: mlp.prism.v1.FamiliesServiceListResponse.families:type_name -> mlp.prism.v1.Family
	0,  // 14: mlp.prism.v1.Aggregation.function:type_name -> mlp.prism.v1.AggregationFunction
	17, // 15: mlp.prism.v1.Aggregation.window:type_name -> mlp.prism.v1.Window
	18, // 16: mlp.prism.v1.FeatureRequest.aggregation:type_name -> mlp.prism.v1.Aggregation
	29, // 17: mlp.prism.v1.FeatureRequest.identifier_mapping:type_name -> mlp.prism.v1.FeatureRequest.IdentifierMappingEntry
	30, // 18: mlp.prism.v1.OnlineFeaturesServiceFetchRequest.feature_requests:type_name -> mlp.prism.v1.OnlineFeaturesServiceFetchRequest.FeatureRequestsEntry
	31, // 19: mlp.prism.v1.OnlineFeaturesServiceFetchRequest.identifiers:type_name -> mlp.prism.v1.OnlineFeaturesServiceFetchRequest.IdentifiersEntry
	34, // 20: mlp.prism.v1.OnlineFeaturesServiceFetchResponse.frontier:type_name -> google.protobuf.Timestamp
	32, // 21: mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest.feature_requests:type_name -> mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest.FeatureRequestsEntry
	23, // 22: mlp.prism.v1.OfflineFeaturesServiceCreateDatasetResponse.presigned_urls:type_name -> mlp.prism.v1.PresignedUrl
	2,  // 23: mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetResponse.status:type_name -> mlp.prism.v1.DatasetStatus
	23, // 24: mlp.prism.v1.OfflineFeaturesServiceFetchDatasetResponse.presigned_urls:type_name -> mlp.prism.v1.PresignedUrl
	19, // 25: mlp.prism.v1.OnlineFeaturesServiceFetchRequest.FeatureRequestsEntry.value:type_name -> mlp.prism.v1.FeatureRequest
	19, // 26: mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest.FeatureRequestsEntry.value:type_name -> mlp.prism.v1.FeatureRequest
	8,  // 27: mlp.prism.v1.FamiliesService.Create:input_type -> mlp.prism.v1.FamiliesServiceCreateRequest
	15, // 28: mlp.prism.v1.FamiliesService.Delete:input_type -> mlp.prism.v1.FamiliesServiceDeleteRequest
	10, // 29: mlp.prism.v1.FamiliesService.Describe:input_type -> mlp.prism.v1.FamiliesServiceDescribeRequest
	13, // 30: mlp.prism.v1.FamiliesService.List:input_type -> mlp.prism.v1.FamiliesServiceListRequest
	20, // 31: mlp.prism.v1.OnlineFeaturesService.Fetch:input_type -> mlp.prism.v1.OnlineFeaturesServiceFetchRequest
	22, // 32: mlp.prism.v1.OfflineFeaturesService.CreateDataset:input_type -> mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest
	25, // 33: mlp.prism.v1.OfflineFeaturesService.DescribeDataset:input_type -> mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetRequest
	27, // 34: mlp.prism.v1.OfflineFeaturesService.FetchDataset:input_type -> mlp.prism.v1.OfflineFeaturesServiceFetchDatasetRequest
	9,  // 35: mlp.prism.v1.FamiliesService.Create:output_type -> mlp.prism.v1.FamiliesServiceCreateResponse
	16, // 36: mlp.prism.v1.FamiliesService.Delete:output_type -> mlp.prism.v1.FamiliesServiceDeleteResponse
	12, // 37: mlp.prism.v1.FamiliesService.Describe:output_type -> mlp.prism.v1.FamiliesServiceDescribeResponse
	14, // 38: mlp.prism.v1.FamiliesService.List:output_type -> mlp.prism.v1.FamiliesServiceListResponse
	21, // 39: mlp.prism.v1.OnlineFeaturesService.Fetch:output_type -> mlp.prism.v1.OnlineFeaturesServiceFetchResponse
	24, // 40: mlp.prism.v1.OfflineFeaturesService.CreateDataset:output_type -> mlp.prism.v1.OfflineFeaturesServiceCreateDatasetResponse
	26, // 41: mlp.prism.v1.OfflineFeaturesService.DescribeDataset:output_type -> mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetResponse
	28, // 42: mlp.prism.v1.OfflineFeaturesService.FetchDataset:output_type -> mlp.prism.v1.OfflineFeaturesServiceFetchDatasetResponse
	35, // [35:43] is the sub-list for method output_type
	27, // [27:35] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_gametime_protos_mlp_prism_v1_service_proto_init() }
func file_gametime_protos_mlp_prism_v1_service_proto_init() {
	if File_gametime_protos_mlp_prism_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gametime_protos_mlp_prism_v1_service_proto_rawDesc), len(file_gametime_protos_mlp_prism_v1_service_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_gametime_protos_mlp_prism_v1_service_proto_goTypes,
		DependencyIndexes: file_gametime_protos_mlp_prism_v1_service_proto_depIdxs,
		EnumInfos:         file_gametime_protos_mlp_prism_v1_service_proto_enumTypes,
		MessageInfos:      file_gametime_protos_mlp_prism_v1_service_proto_msgTypes,
	}.Build()
	File_gametime_protos_mlp_prism_v1_service_proto = out.File
	file_gametime_protos_mlp_prism_v1_service_proto_goTypes = nil
	file_gametime_protos_mlp_prism_v1_service_proto_depIdxs = nil
}
