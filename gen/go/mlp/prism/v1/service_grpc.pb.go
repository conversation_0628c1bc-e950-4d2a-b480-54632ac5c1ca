// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: gametime_protos/mlp/prism/v1/service.proto

package protos

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FamiliesService_Create_FullMethodName   = "/mlp.prism.v1.FamiliesService/Create"
	FamiliesService_Delete_FullMethodName   = "/mlp.prism.v1.FamiliesService/Delete"
	FamiliesService_Describe_FullMethodName = "/mlp.prism.v1.FamiliesService/Describe"
	FamiliesService_List_FullMethodName     = "/mlp.prism.v1.FamiliesService/List"
)

// FamiliesServiceClient is the client API for FamiliesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FamiliesServiceClient interface {
	Create(ctx context.Context, in *FamiliesServiceCreateRequest, opts ...grpc.CallOption) (*FamiliesServiceCreateResponse, error)
	Delete(ctx context.Context, in *FamiliesServiceDeleteRequest, opts ...grpc.CallOption) (*FamiliesServiceDeleteResponse, error)
	Describe(ctx context.Context, in *FamiliesServiceDescribeRequest, opts ...grpc.CallOption) (*FamiliesServiceDescribeResponse, error)
	List(ctx context.Context, in *FamiliesServiceListRequest, opts ...grpc.CallOption) (*FamiliesServiceListResponse, error)
}

type familiesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFamiliesServiceClient(cc grpc.ClientConnInterface) FamiliesServiceClient {
	return &familiesServiceClient{cc}
}

func (c *familiesServiceClient) Create(ctx context.Context, in *FamiliesServiceCreateRequest, opts ...grpc.CallOption) (*FamiliesServiceCreateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FamiliesServiceCreateResponse)
	err := c.cc.Invoke(ctx, FamiliesService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *familiesServiceClient) Delete(ctx context.Context, in *FamiliesServiceDeleteRequest, opts ...grpc.CallOption) (*FamiliesServiceDeleteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FamiliesServiceDeleteResponse)
	err := c.cc.Invoke(ctx, FamiliesService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *familiesServiceClient) Describe(ctx context.Context, in *FamiliesServiceDescribeRequest, opts ...grpc.CallOption) (*FamiliesServiceDescribeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FamiliesServiceDescribeResponse)
	err := c.cc.Invoke(ctx, FamiliesService_Describe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *familiesServiceClient) List(ctx context.Context, in *FamiliesServiceListRequest, opts ...grpc.CallOption) (*FamiliesServiceListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FamiliesServiceListResponse)
	err := c.cc.Invoke(ctx, FamiliesService_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FamiliesServiceServer is the server API for FamiliesService service.
// All implementations must embed UnimplementedFamiliesServiceServer
// for forward compatibility.
type FamiliesServiceServer interface {
	Create(context.Context, *FamiliesServiceCreateRequest) (*FamiliesServiceCreateResponse, error)
	Delete(context.Context, *FamiliesServiceDeleteRequest) (*FamiliesServiceDeleteResponse, error)
	Describe(context.Context, *FamiliesServiceDescribeRequest) (*FamiliesServiceDescribeResponse, error)
	List(context.Context, *FamiliesServiceListRequest) (*FamiliesServiceListResponse, error)
	mustEmbedUnimplementedFamiliesServiceServer()
}

// UnimplementedFamiliesServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFamiliesServiceServer struct{}

func (UnimplementedFamiliesServiceServer) Create(context.Context, *FamiliesServiceCreateRequest) (*FamiliesServiceCreateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedFamiliesServiceServer) Delete(context.Context, *FamiliesServiceDeleteRequest) (*FamiliesServiceDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedFamiliesServiceServer) Describe(context.Context, *FamiliesServiceDescribeRequest) (*FamiliesServiceDescribeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Describe not implemented")
}
func (UnimplementedFamiliesServiceServer) List(context.Context, *FamiliesServiceListRequest) (*FamiliesServiceListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedFamiliesServiceServer) mustEmbedUnimplementedFamiliesServiceServer() {}
func (UnimplementedFamiliesServiceServer) testEmbeddedByValue()                         {}

// UnsafeFamiliesServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FamiliesServiceServer will
// result in compilation errors.
type UnsafeFamiliesServiceServer interface {
	mustEmbedUnimplementedFamiliesServiceServer()
}

func RegisterFamiliesServiceServer(s grpc.ServiceRegistrar, srv FamiliesServiceServer) {
	// If the following call pancis, it indicates UnimplementedFamiliesServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FamiliesService_ServiceDesc, srv)
}

func _FamiliesService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FamiliesServiceCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FamiliesServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FamiliesService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FamiliesServiceServer).Create(ctx, req.(*FamiliesServiceCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FamiliesService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FamiliesServiceDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FamiliesServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FamiliesService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FamiliesServiceServer).Delete(ctx, req.(*FamiliesServiceDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FamiliesService_Describe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FamiliesServiceDescribeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FamiliesServiceServer).Describe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FamiliesService_Describe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FamiliesServiceServer).Describe(ctx, req.(*FamiliesServiceDescribeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FamiliesService_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FamiliesServiceListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FamiliesServiceServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FamiliesService_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FamiliesServiceServer).List(ctx, req.(*FamiliesServiceListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FamiliesService_ServiceDesc is the grpc.ServiceDesc for FamiliesService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FamiliesService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mlp.prism.v1.FamiliesService",
	HandlerType: (*FamiliesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _FamiliesService_Create_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _FamiliesService_Delete_Handler,
		},
		{
			MethodName: "Describe",
			Handler:    _FamiliesService_Describe_Handler,
		},
		{
			MethodName: "List",
			Handler:    _FamiliesService_List_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mlp/prism/v1/service.proto",
}

const (
	OnlineFeaturesService_Fetch_FullMethodName = "/mlp.prism.v1.OnlineFeaturesService/Fetch"
)

// OnlineFeaturesServiceClient is the client API for OnlineFeaturesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OnlineFeaturesServiceClient interface {
	Fetch(ctx context.Context, in *OnlineFeaturesServiceFetchRequest, opts ...grpc.CallOption) (*OnlineFeaturesServiceFetchResponse, error)
}

type onlineFeaturesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOnlineFeaturesServiceClient(cc grpc.ClientConnInterface) OnlineFeaturesServiceClient {
	return &onlineFeaturesServiceClient{cc}
}

func (c *onlineFeaturesServiceClient) Fetch(ctx context.Context, in *OnlineFeaturesServiceFetchRequest, opts ...grpc.CallOption) (*OnlineFeaturesServiceFetchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OnlineFeaturesServiceFetchResponse)
	err := c.cc.Invoke(ctx, OnlineFeaturesService_Fetch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnlineFeaturesServiceServer is the server API for OnlineFeaturesService service.
// All implementations must embed UnimplementedOnlineFeaturesServiceServer
// for forward compatibility.
type OnlineFeaturesServiceServer interface {
	Fetch(context.Context, *OnlineFeaturesServiceFetchRequest) (*OnlineFeaturesServiceFetchResponse, error)
	mustEmbedUnimplementedOnlineFeaturesServiceServer()
}

// UnimplementedOnlineFeaturesServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOnlineFeaturesServiceServer struct{}

func (UnimplementedOnlineFeaturesServiceServer) Fetch(context.Context, *OnlineFeaturesServiceFetchRequest) (*OnlineFeaturesServiceFetchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Fetch not implemented")
}
func (UnimplementedOnlineFeaturesServiceServer) mustEmbedUnimplementedOnlineFeaturesServiceServer() {}
func (UnimplementedOnlineFeaturesServiceServer) testEmbeddedByValue()                               {}

// UnsafeOnlineFeaturesServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OnlineFeaturesServiceServer will
// result in compilation errors.
type UnsafeOnlineFeaturesServiceServer interface {
	mustEmbedUnimplementedOnlineFeaturesServiceServer()
}

func RegisterOnlineFeaturesServiceServer(s grpc.ServiceRegistrar, srv OnlineFeaturesServiceServer) {
	// If the following call pancis, it indicates UnimplementedOnlineFeaturesServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OnlineFeaturesService_ServiceDesc, srv)
}

func _OnlineFeaturesService_Fetch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnlineFeaturesServiceFetchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnlineFeaturesServiceServer).Fetch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OnlineFeaturesService_Fetch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnlineFeaturesServiceServer).Fetch(ctx, req.(*OnlineFeaturesServiceFetchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OnlineFeaturesService_ServiceDesc is the grpc.ServiceDesc for OnlineFeaturesService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OnlineFeaturesService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mlp.prism.v1.OnlineFeaturesService",
	HandlerType: (*OnlineFeaturesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Fetch",
			Handler:    _OnlineFeaturesService_Fetch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mlp/prism/v1/service.proto",
}

const (
	OfflineFeaturesService_CreateDataset_FullMethodName   = "/mlp.prism.v1.OfflineFeaturesService/CreateDataset"
	OfflineFeaturesService_DescribeDataset_FullMethodName = "/mlp.prism.v1.OfflineFeaturesService/DescribeDataset"
	OfflineFeaturesService_FetchDataset_FullMethodName    = "/mlp.prism.v1.OfflineFeaturesService/FetchDataset"
)

// OfflineFeaturesServiceClient is the client API for OfflineFeaturesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OfflineFeaturesServiceClient interface {
	CreateDataset(ctx context.Context, in *OfflineFeaturesServiceCreateDatasetRequest, opts ...grpc.CallOption) (*OfflineFeaturesServiceCreateDatasetResponse, error)
	DescribeDataset(ctx context.Context, in *OfflineFeaturesServiceDescribeDatasetRequest, opts ...grpc.CallOption) (*OfflineFeaturesServiceDescribeDatasetResponse, error)
	FetchDataset(ctx context.Context, in *OfflineFeaturesServiceFetchDatasetRequest, opts ...grpc.CallOption) (*OfflineFeaturesServiceFetchDatasetResponse, error)
}

type offlineFeaturesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOfflineFeaturesServiceClient(cc grpc.ClientConnInterface) OfflineFeaturesServiceClient {
	return &offlineFeaturesServiceClient{cc}
}

func (c *offlineFeaturesServiceClient) CreateDataset(ctx context.Context, in *OfflineFeaturesServiceCreateDatasetRequest, opts ...grpc.CallOption) (*OfflineFeaturesServiceCreateDatasetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OfflineFeaturesServiceCreateDatasetResponse)
	err := c.cc.Invoke(ctx, OfflineFeaturesService_CreateDataset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offlineFeaturesServiceClient) DescribeDataset(ctx context.Context, in *OfflineFeaturesServiceDescribeDatasetRequest, opts ...grpc.CallOption) (*OfflineFeaturesServiceDescribeDatasetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OfflineFeaturesServiceDescribeDatasetResponse)
	err := c.cc.Invoke(ctx, OfflineFeaturesService_DescribeDataset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offlineFeaturesServiceClient) FetchDataset(ctx context.Context, in *OfflineFeaturesServiceFetchDatasetRequest, opts ...grpc.CallOption) (*OfflineFeaturesServiceFetchDatasetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OfflineFeaturesServiceFetchDatasetResponse)
	err := c.cc.Invoke(ctx, OfflineFeaturesService_FetchDataset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OfflineFeaturesServiceServer is the server API for OfflineFeaturesService service.
// All implementations must embed UnimplementedOfflineFeaturesServiceServer
// for forward compatibility.
type OfflineFeaturesServiceServer interface {
	CreateDataset(context.Context, *OfflineFeaturesServiceCreateDatasetRequest) (*OfflineFeaturesServiceCreateDatasetResponse, error)
	DescribeDataset(context.Context, *OfflineFeaturesServiceDescribeDatasetRequest) (*OfflineFeaturesServiceDescribeDatasetResponse, error)
	FetchDataset(context.Context, *OfflineFeaturesServiceFetchDatasetRequest) (*OfflineFeaturesServiceFetchDatasetResponse, error)
	mustEmbedUnimplementedOfflineFeaturesServiceServer()
}

// UnimplementedOfflineFeaturesServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOfflineFeaturesServiceServer struct{}

func (UnimplementedOfflineFeaturesServiceServer) CreateDataset(context.Context, *OfflineFeaturesServiceCreateDatasetRequest) (*OfflineFeaturesServiceCreateDatasetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataset not implemented")
}
func (UnimplementedOfflineFeaturesServiceServer) DescribeDataset(context.Context, *OfflineFeaturesServiceDescribeDatasetRequest) (*OfflineFeaturesServiceDescribeDatasetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeDataset not implemented")
}
func (UnimplementedOfflineFeaturesServiceServer) FetchDataset(context.Context, *OfflineFeaturesServiceFetchDatasetRequest) (*OfflineFeaturesServiceFetchDatasetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchDataset not implemented")
}
func (UnimplementedOfflineFeaturesServiceServer) mustEmbedUnimplementedOfflineFeaturesServiceServer() {
}
func (UnimplementedOfflineFeaturesServiceServer) testEmbeddedByValue() {}

// UnsafeOfflineFeaturesServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OfflineFeaturesServiceServer will
// result in compilation errors.
type UnsafeOfflineFeaturesServiceServer interface {
	mustEmbedUnimplementedOfflineFeaturesServiceServer()
}

func RegisterOfflineFeaturesServiceServer(s grpc.ServiceRegistrar, srv OfflineFeaturesServiceServer) {
	// If the following call pancis, it indicates UnimplementedOfflineFeaturesServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OfflineFeaturesService_ServiceDesc, srv)
}

func _OfflineFeaturesService_CreateDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflineFeaturesServiceCreateDatasetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfflineFeaturesServiceServer).CreateDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OfflineFeaturesService_CreateDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfflineFeaturesServiceServer).CreateDataset(ctx, req.(*OfflineFeaturesServiceCreateDatasetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfflineFeaturesService_DescribeDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflineFeaturesServiceDescribeDatasetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfflineFeaturesServiceServer).DescribeDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OfflineFeaturesService_DescribeDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfflineFeaturesServiceServer).DescribeDataset(ctx, req.(*OfflineFeaturesServiceDescribeDatasetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfflineFeaturesService_FetchDataset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflineFeaturesServiceFetchDatasetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfflineFeaturesServiceServer).FetchDataset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OfflineFeaturesService_FetchDataset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfflineFeaturesServiceServer).FetchDataset(ctx, req.(*OfflineFeaturesServiceFetchDatasetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// OfflineFeaturesService_ServiceDesc is the grpc.ServiceDesc for OfflineFeaturesService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OfflineFeaturesService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mlp.prism.v1.OfflineFeaturesService",
	HandlerType: (*OfflineFeaturesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDataset",
			Handler:    _OfflineFeaturesService_CreateDataset_Handler,
		},
		{
			MethodName: "DescribeDataset",
			Handler:    _OfflineFeaturesService_DescribeDataset_Handler,
		},
		{
			MethodName: "FetchDataset",
			Handler:    _OfflineFeaturesService_FetchDataset_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/mlp/prism/v1/service.proto",
}
