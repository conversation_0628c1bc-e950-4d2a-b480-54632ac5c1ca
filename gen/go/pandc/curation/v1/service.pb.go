// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: gametime_protos/pandc/curation/v1/service.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ZoomLevel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tag           int32                  `protobuf:"varint,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ZoomLevel) Reset() {
	*x = ZoomLevel{}
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ZoomLevel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZoomLevel) ProtoMessage() {}

func (x *ZoomLevel) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZoomLevel.ProtoReflect.Descriptor instead.
func (*ZoomLevel) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *ZoomLevel) GetTag() int32 {
	if x != nil {
		return x.Tag
	}
	return 0
}

func (x *ZoomLevel) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type CurationOptions struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	IncludeOverflowListings bool                   `protobuf:"varint,1,opt,name=include_overflow_listings,json=includeOverflowListings,proto3" json:"include_overflow_listings,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *CurationOptions) Reset() {
	*x = CurationOptions{}
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CurationOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurationOptions) ProtoMessage() {}

func (x *CurationOptions) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurationOptions.ProtoReflect.Descriptor instead.
func (*CurationOptions) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *CurationOptions) GetIncludeOverflowListings() bool {
	if x != nil {
		return x.IncludeOverflowListings
	}
	return false
}

type CurationServiceCurateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventId       string                 `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	Venue         string                 `protobuf:"bytes,2,opt,name=venue,proto3" json:"venue,omitempty"`
	VenueConfig   string                 `protobuf:"bytes,3,opt,name=venue_config,json=venueConfig,proto3" json:"venue_config,omitempty"`
	Listings      []byte                 `protobuf:"bytes,4,opt,name=listings,proto3" json:"listings,omitempty"`
	Quantity      int32                  `protobuf:"varint,5,opt,name=quantity,proto3" json:"quantity,omitempty"`
	ZoomLevels    []*ZoomLevel           `protobuf:"bytes,6,rep,name=zoom_levels,json=zoomLevels,proto3" json:"zoom_levels,omitempty"`
	EventTime     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=event_time,json=eventTime,proto3" json:"event_time,omitempty"`
	League        string                 `protobuf:"bytes,8,opt,name=league,proto3" json:"league,omitempty"`
	Options       *CurationOptions       `protobuf:"bytes,9,opt,name=options,proto3" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CurationServiceCurateRequest) Reset() {
	*x = CurationServiceCurateRequest{}
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CurationServiceCurateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurationServiceCurateRequest) ProtoMessage() {}

func (x *CurationServiceCurateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurationServiceCurateRequest.ProtoReflect.Descriptor instead.
func (*CurationServiceCurateRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *CurationServiceCurateRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *CurationServiceCurateRequest) GetVenue() string {
	if x != nil {
		return x.Venue
	}
	return ""
}

func (x *CurationServiceCurateRequest) GetVenueConfig() string {
	if x != nil {
		return x.VenueConfig
	}
	return ""
}

func (x *CurationServiceCurateRequest) GetListings() []byte {
	if x != nil {
		return x.Listings
	}
	return nil
}

func (x *CurationServiceCurateRequest) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CurationServiceCurateRequest) GetZoomLevels() []*ZoomLevel {
	if x != nil {
		return x.ZoomLevels
	}
	return nil
}

func (x *CurationServiceCurateRequest) GetEventTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EventTime
	}
	return nil
}

func (x *CurationServiceCurateRequest) GetLeague() string {
	if x != nil {
		return x.League
	}
	return ""
}

func (x *CurationServiceCurateRequest) GetOptions() *CurationOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type CurationServiceCurateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Results       []byte                 `protobuf:"bytes,1,opt,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CurationServiceCurateResponse) Reset() {
	*x = CurationServiceCurateResponse{}
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CurationServiceCurateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurationServiceCurateResponse) ProtoMessage() {}

func (x *CurationServiceCurateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurationServiceCurateResponse.ProtoReflect.Descriptor instead.
func (*CurationServiceCurateResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *CurationServiceCurateResponse) GetResults() []byte {
	if x != nil {
		return x.Results
	}
	return nil
}

type TableServiceUpsertEssentialsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Venue         string                 `protobuf:"bytes,1,opt,name=venue,proto3" json:"venue,omitempty"`
	VenueConfig   string                 `protobuf:"bytes,2,opt,name=venue_config,json=venueConfig,proto3" json:"venue_config,omitempty"`
	Body          []byte                 `protobuf:"bytes,3,opt,name=body,proto3" json:"body,omitempty"`
	Version       string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableServiceUpsertEssentialsRequest) Reset() {
	*x = TableServiceUpsertEssentialsRequest{}
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableServiceUpsertEssentialsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableServiceUpsertEssentialsRequest) ProtoMessage() {}

func (x *TableServiceUpsertEssentialsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableServiceUpsertEssentialsRequest.ProtoReflect.Descriptor instead.
func (*TableServiceUpsertEssentialsRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *TableServiceUpsertEssentialsRequest) GetVenue() string {
	if x != nil {
		return x.Venue
	}
	return ""
}

func (x *TableServiceUpsertEssentialsRequest) GetVenueConfig() string {
	if x != nil {
		return x.VenueConfig
	}
	return ""
}

func (x *TableServiceUpsertEssentialsRequest) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *TableServiceUpsertEssentialsRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type TableServiceUpsertEssentialsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableServiceUpsertEssentialsResponse) Reset() {
	*x = TableServiceUpsertEssentialsResponse{}
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableServiceUpsertEssentialsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableServiceUpsertEssentialsResponse) ProtoMessage() {}

func (x *TableServiceUpsertEssentialsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableServiceUpsertEssentialsResponse.ProtoReflect.Descriptor instead.
func (*TableServiceUpsertEssentialsResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP(), []int{5}
}

type TableServiceDescribeEssentialsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Venue         string                 `protobuf:"bytes,1,opt,name=venue,proto3" json:"venue,omitempty"`
	VenueConfig   string                 `protobuf:"bytes,2,opt,name=venue_config,json=venueConfig,proto3" json:"venue_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableServiceDescribeEssentialsRequest) Reset() {
	*x = TableServiceDescribeEssentialsRequest{}
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableServiceDescribeEssentialsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableServiceDescribeEssentialsRequest) ProtoMessage() {}

func (x *TableServiceDescribeEssentialsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableServiceDescribeEssentialsRequest.ProtoReflect.Descriptor instead.
func (*TableServiceDescribeEssentialsRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *TableServiceDescribeEssentialsRequest) GetVenue() string {
	if x != nil {
		return x.Venue
	}
	return ""
}

func (x *TableServiceDescribeEssentialsRequest) GetVenueConfig() string {
	if x != nil {
		return x.VenueConfig
	}
	return ""
}

type TableServiceDescribeEssentialsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       string                 `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableServiceDescribeEssentialsResponse) Reset() {
	*x = TableServiceDescribeEssentialsResponse{}
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableServiceDescribeEssentialsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableServiceDescribeEssentialsResponse) ProtoMessage() {}

func (x *TableServiceDescribeEssentialsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_curation_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableServiceDescribeEssentialsResponse.ProtoReflect.Descriptor instead.
func (*TableServiceDescribeEssentialsResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *TableServiceDescribeEssentialsResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

var File_gametime_protos_pandc_curation_v1_service_proto protoreflect.FileDescriptor

const file_gametime_protos_pandc_curation_v1_service_proto_rawDesc = "" +
	"\n" +
	"/gametime_protos/pandc/curation/v1/service.proto\x12\x11pandc.curation.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"3\n" +
	"\tZoomLevel\x12\x10\n" +
	"\x03tag\x18\x01 \x01(\x05R\x03tag\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"M\n" +
	"\x0fCurationOptions\x12:\n" +
	"\x19include_overflow_listings\x18\x01 \x01(\bR\x17includeOverflowListings\"\xfa\x02\n" +
	"\x1cCurationServiceCurateRequest\x12\x19\n" +
	"\bevent_id\x18\x01 \x01(\tR\aeventId\x12\x14\n" +
	"\x05venue\x18\x02 \x01(\tR\x05venue\x12!\n" +
	"\fvenue_config\x18\x03 \x01(\tR\vvenueConfig\x12\x1a\n" +
	"\blistings\x18\x04 \x01(\fR\blistings\x12\x1a\n" +
	"\bquantity\x18\x05 \x01(\x05R\bquantity\x12=\n" +
	"\vzoom_levels\x18\x06 \x03(\v2\x1c.pandc.curation.v1.ZoomLevelR\n" +
	"zoomLevels\x129\n" +
	"\n" +
	"event_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\teventTime\x12\x16\n" +
	"\x06league\x18\b \x01(\tR\x06league\x12<\n" +
	"\aoptions\x18\t \x01(\v2\".pandc.curation.v1.CurationOptionsR\aoptions\"9\n" +
	"\x1dCurationServiceCurateResponse\x12\x18\n" +
	"\aresults\x18\x01 \x01(\fR\aresults\"\x8c\x01\n" +
	"#TableServiceUpsertEssentialsRequest\x12\x14\n" +
	"\x05venue\x18\x01 \x01(\tR\x05venue\x12!\n" +
	"\fvenue_config\x18\x02 \x01(\tR\vvenueConfig\x12\x12\n" +
	"\x04body\x18\x03 \x01(\fR\x04body\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\"&\n" +
	"$TableServiceUpsertEssentialsResponse\"`\n" +
	"%TableServiceDescribeEssentialsRequest\x12\x14\n" +
	"\x05venue\x18\x01 \x01(\tR\x05venue\x12!\n" +
	"\fvenue_config\x18\x02 \x01(\tR\vvenueConfig\"B\n" +
	"&TableServiceDescribeEssentialsResponse\x12\x18\n" +
	"\aversion\x18\x01 \x01(\tR\aversion2\x80\x01\n" +
	"\x0fCurationService\x12m\n" +
	"\x06Curate\x12/.pandc.curation.v1.CurationServiceCurateRequest\x1a0.pandc.curation.v1.CurationServiceCurateResponse\"\x002\xa4\x02\n" +
	"\fTableService\x12\x85\x01\n" +
	"\x10UpsertEssentials\x126.pandc.curation.v1.TableServiceUpsertEssentialsRequest\x1a7.pandc.curation.v1.TableServiceUpsertEssentialsResponse\"\x00\x12\x8b\x01\n" +
	"\x12DescribeEssentials\x128.pandc.curation.v1.TableServiceDescribeEssentialsRequest\x1a9.pandc.curation.v1.TableServiceDescribeEssentialsResponse\"\x00B\xa5\x01\n" +
	"\x15com.pandc.curation.v1B\fServiceProtoP\x01Z\x18pandc/curation/v1;protos\xa2\x02\x03PCX\xaa\x02\x11Pandc.Curation.V1\xca\x02\x11Pandc\\Curation\\V1\xe2\x02\x1dPandc\\Curation\\V1\\GPBMetadata\xea\x02\x13Pandc::Curation::V1b\x06proto3"

var (
	file_gametime_protos_pandc_curation_v1_service_proto_rawDescOnce sync.Once
	file_gametime_protos_pandc_curation_v1_service_proto_rawDescData []byte
)

func file_gametime_protos_pandc_curation_v1_service_proto_rawDescGZIP() []byte {
	file_gametime_protos_pandc_curation_v1_service_proto_rawDescOnce.Do(func() {
		file_gametime_protos_pandc_curation_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gametime_protos_pandc_curation_v1_service_proto_rawDesc), len(file_gametime_protos_pandc_curation_v1_service_proto_rawDesc)))
	})
	return file_gametime_protos_pandc_curation_v1_service_proto_rawDescData
}

var file_gametime_protos_pandc_curation_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_gametime_protos_pandc_curation_v1_service_proto_goTypes = []any{
	(*ZoomLevel)(nil),                              // 0: pandc.curation.v1.ZoomLevel
	(*CurationOptions)(nil),                        // 1: pandc.curation.v1.CurationOptions
	(*CurationServiceCurateRequest)(nil),           // 2: pandc.curation.v1.CurationServiceCurateRequest
	(*CurationServiceCurateResponse)(nil),          // 3: pandc.curation.v1.CurationServiceCurateResponse
	(*TableServiceUpsertEssentialsRequest)(nil),    // 4: pandc.curation.v1.TableServiceUpsertEssentialsRequest
	(*TableServiceUpsertEssentialsResponse)(nil),   // 5: pandc.curation.v1.TableServiceUpsertEssentialsResponse
	(*TableServiceDescribeEssentialsRequest)(nil),  // 6: pandc.curation.v1.TableServiceDescribeEssentialsRequest
	(*TableServiceDescribeEssentialsResponse)(nil), // 7: pandc.curation.v1.TableServiceDescribeEssentialsResponse
	(*timestamppb.Timestamp)(nil),                  // 8: google.protobuf.Timestamp
}
var file_gametime_protos_pandc_curation_v1_service_proto_depIdxs = []int32{
	0, // 0: pandc.curation.v1.CurationServiceCurateRequest.zoom_levels:type_name -> pandc.curation.v1.ZoomLevel
	8, // 1: pandc.curation.v1.CurationServiceCurateRequest.event_time:type_name -> google.protobuf.Timestamp
	1, // 2: pandc.curation.v1.CurationServiceCurateRequest.options:type_name -> pandc.curation.v1.CurationOptions
	2, // 3: pandc.curation.v1.CurationService.Curate:input_type -> pandc.curation.v1.CurationServiceCurateRequest
	4, // 4: pandc.curation.v1.TableService.UpsertEssentials:input_type -> pandc.curation.v1.TableServiceUpsertEssentialsRequest
	6, // 5: pandc.curation.v1.TableService.DescribeEssentials:input_type -> pandc.curation.v1.TableServiceDescribeEssentialsRequest
	3, // 6: pandc.curation.v1.CurationService.Curate:output_type -> pandc.curation.v1.CurationServiceCurateResponse
	5, // 7: pandc.curation.v1.TableService.UpsertEssentials:output_type -> pandc.curation.v1.TableServiceUpsertEssentialsResponse
	7, // 8: pandc.curation.v1.TableService.DescribeEssentials:output_type -> pandc.curation.v1.TableServiceDescribeEssentialsResponse
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_gametime_protos_pandc_curation_v1_service_proto_init() }
func file_gametime_protos_pandc_curation_v1_service_proto_init() {
	if File_gametime_protos_pandc_curation_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gametime_protos_pandc_curation_v1_service_proto_rawDesc), len(file_gametime_protos_pandc_curation_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_gametime_protos_pandc_curation_v1_service_proto_goTypes,
		DependencyIndexes: file_gametime_protos_pandc_curation_v1_service_proto_depIdxs,
		MessageInfos:      file_gametime_protos_pandc_curation_v1_service_proto_msgTypes,
	}.Build()
	File_gametime_protos_pandc_curation_v1_service_proto = out.File
	file_gametime_protos_pandc_curation_v1_service_proto_goTypes = nil
	file_gametime_protos_pandc_curation_v1_service_proto_depIdxs = nil
}
