// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: gametime_protos/pandc/curation/v1/service.proto

package protos

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CurationService_Curate_FullMethodName = "/pandc.curation.v1.CurationService/Curate"
)

// CurationServiceClient is the client API for CurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CurationServiceClient interface {
	Curate(ctx context.Context, in *CurationServiceCurateRequest, opts ...grpc.CallOption) (*CurationServiceCurateResponse, error)
}

type curationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCurationServiceClient(cc grpc.ClientConnInterface) CurationServiceClient {
	return &curationServiceClient{cc}
}

func (c *curationServiceClient) Curate(ctx context.Context, in *CurationServiceCurateRequest, opts ...grpc.CallOption) (*CurationServiceCurateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CurationServiceCurateResponse)
	err := c.cc.Invoke(ctx, CurationService_Curate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CurationServiceServer is the server API for CurationService service.
// All implementations must embed UnimplementedCurationServiceServer
// for forward compatibility.
type CurationServiceServer interface {
	Curate(context.Context, *CurationServiceCurateRequest) (*CurationServiceCurateResponse, error)
	mustEmbedUnimplementedCurationServiceServer()
}

// UnimplementedCurationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCurationServiceServer struct{}

func (UnimplementedCurationServiceServer) Curate(context.Context, *CurationServiceCurateRequest) (*CurationServiceCurateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Curate not implemented")
}
func (UnimplementedCurationServiceServer) mustEmbedUnimplementedCurationServiceServer() {}
func (UnimplementedCurationServiceServer) testEmbeddedByValue()                         {}

// UnsafeCurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CurationServiceServer will
// result in compilation errors.
type UnsafeCurationServiceServer interface {
	mustEmbedUnimplementedCurationServiceServer()
}

func RegisterCurationServiceServer(s grpc.ServiceRegistrar, srv CurationServiceServer) {
	// If the following call pancis, it indicates UnimplementedCurationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CurationService_ServiceDesc, srv)
}

func _CurationService_Curate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CurationServiceCurateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CurationServiceServer).Curate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CurationService_Curate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CurationServiceServer).Curate(ctx, req.(*CurationServiceCurateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CurationService_ServiceDesc is the grpc.ServiceDesc for CurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pandc.curation.v1.CurationService",
	HandlerType: (*CurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Curate",
			Handler:    _CurationService_Curate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/pandc/curation/v1/service.proto",
}

const (
	TableService_UpsertEssentials_FullMethodName   = "/pandc.curation.v1.TableService/UpsertEssentials"
	TableService_DescribeEssentials_FullMethodName = "/pandc.curation.v1.TableService/DescribeEssentials"
)

// TableServiceClient is the client API for TableService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TableServiceClient interface {
	UpsertEssentials(ctx context.Context, in *TableServiceUpsertEssentialsRequest, opts ...grpc.CallOption) (*TableServiceUpsertEssentialsResponse, error)
	DescribeEssentials(ctx context.Context, in *TableServiceDescribeEssentialsRequest, opts ...grpc.CallOption) (*TableServiceDescribeEssentialsResponse, error)
}

type tableServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTableServiceClient(cc grpc.ClientConnInterface) TableServiceClient {
	return &tableServiceClient{cc}
}

func (c *tableServiceClient) UpsertEssentials(ctx context.Context, in *TableServiceUpsertEssentialsRequest, opts ...grpc.CallOption) (*TableServiceUpsertEssentialsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TableServiceUpsertEssentialsResponse)
	err := c.cc.Invoke(ctx, TableService_UpsertEssentials_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tableServiceClient) DescribeEssentials(ctx context.Context, in *TableServiceDescribeEssentialsRequest, opts ...grpc.CallOption) (*TableServiceDescribeEssentialsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TableServiceDescribeEssentialsResponse)
	err := c.cc.Invoke(ctx, TableService_DescribeEssentials_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TableServiceServer is the server API for TableService service.
// All implementations must embed UnimplementedTableServiceServer
// for forward compatibility.
type TableServiceServer interface {
	UpsertEssentials(context.Context, *TableServiceUpsertEssentialsRequest) (*TableServiceUpsertEssentialsResponse, error)
	DescribeEssentials(context.Context, *TableServiceDescribeEssentialsRequest) (*TableServiceDescribeEssentialsResponse, error)
	mustEmbedUnimplementedTableServiceServer()
}

// UnimplementedTableServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTableServiceServer struct{}

func (UnimplementedTableServiceServer) UpsertEssentials(context.Context, *TableServiceUpsertEssentialsRequest) (*TableServiceUpsertEssentialsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertEssentials not implemented")
}
func (UnimplementedTableServiceServer) DescribeEssentials(context.Context, *TableServiceDescribeEssentialsRequest) (*TableServiceDescribeEssentialsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeEssentials not implemented")
}
func (UnimplementedTableServiceServer) mustEmbedUnimplementedTableServiceServer() {}
func (UnimplementedTableServiceServer) testEmbeddedByValue()                      {}

// UnsafeTableServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TableServiceServer will
// result in compilation errors.
type UnsafeTableServiceServer interface {
	mustEmbedUnimplementedTableServiceServer()
}

func RegisterTableServiceServer(s grpc.ServiceRegistrar, srv TableServiceServer) {
	// If the following call pancis, it indicates UnimplementedTableServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TableService_ServiceDesc, srv)
}

func _TableService_UpsertEssentials_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TableServiceUpsertEssentialsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TableServiceServer).UpsertEssentials(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TableService_UpsertEssentials_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TableServiceServer).UpsertEssentials(ctx, req.(*TableServiceUpsertEssentialsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TableService_DescribeEssentials_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TableServiceDescribeEssentialsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TableServiceServer).DescribeEssentials(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TableService_DescribeEssentials_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TableServiceServer).DescribeEssentials(ctx, req.(*TableServiceDescribeEssentialsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TableService_ServiceDesc is the grpc.ServiceDesc for TableService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TableService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pandc.curation.v1.TableService",
	HandlerType: (*TableServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertEssentials",
			Handler:    _TableService_UpsertEssentials_Handler,
		},
		{
			MethodName: "DescribeEssentials",
			Handler:    _TableService_DescribeEssentials_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/pandc/curation/v1/service.proto",
}
