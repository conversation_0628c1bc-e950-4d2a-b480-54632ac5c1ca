// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: gametime_protos/pandc/pricing/v1/service.proto

package protos

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PricingServicePriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	League        string                 `protobuf:"bytes,1,opt,name=league,proto3" json:"league,omitempty"`
	EventId       string                 `protobuf:"bytes,2,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	Listings      []byte                 `protobuf:"bytes,3,opt,name=listings,proto3" json:"listings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PricingServicePriceRequest) Reset() {
	*x = PricingServicePriceRequest{}
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PricingServicePriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingServicePriceRequest) ProtoMessage() {}

func (x *PricingServicePriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingServicePriceRequest.ProtoReflect.Descriptor instead.
func (*PricingServicePriceRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *PricingServicePriceRequest) GetLeague() string {
	if x != nil {
		return x.League
	}
	return ""
}

func (x *PricingServicePriceRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *PricingServicePriceRequest) GetListings() []byte {
	if x != nil {
		return x.Listings
	}
	return nil
}

type PricingServicePriceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Prices        []byte                 `protobuf:"bytes,1,opt,name=prices,proto3" json:"prices,omitempty"`
	Results       []byte                 `protobuf:"bytes,2,opt,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PricingServicePriceResponse) Reset() {
	*x = PricingServicePriceResponse{}
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PricingServicePriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingServicePriceResponse) ProtoMessage() {}

func (x *PricingServicePriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingServicePriceResponse.ProtoReflect.Descriptor instead.
func (*PricingServicePriceResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *PricingServicePriceResponse) GetPrices() []byte {
	if x != nil {
		return x.Prices
	}
	return nil
}

func (x *PricingServicePriceResponse) GetResults() []byte {
	if x != nil {
		return x.Results
	}
	return nil
}

type PricingServiceInvertPriceRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	League           string                 `protobuf:"bytes,1,opt,name=league,proto3" json:"league,omitempty"`
	EventId          string                 `protobuf:"bytes,2,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	TargetPriceCents int32                  `protobuf:"varint,3,opt,name=target_price_cents,json=targetPriceCents,proto3" json:"target_price_cents,omitempty"`
	UnitCostCents    int32                  `protobuf:"varint,4,opt,name=unit_cost_cents,json=unitCostCents,proto3" json:"unit_cost_cents,omitempty"`
	SellFee          float64                `protobuf:"fixed64,5,opt,name=sell_fee,json=sellFee,proto3" json:"sell_fee,omitempty"`
	// Deprecated: Marked as deprecated in gametime_protos/pandc/pricing/v1/service.proto.
	IsSpec        bool `protobuf:"varint,6,opt,name=is_spec,json=isSpec,proto3" json:"is_spec,omitempty"`
	IsHighOpsCost bool `protobuf:"varint,7,opt,name=is_high_ops_cost,json=isHighOpsCost,proto3" json:"is_high_ops_cost,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PricingServiceInvertPriceRequest) Reset() {
	*x = PricingServiceInvertPriceRequest{}
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PricingServiceInvertPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingServiceInvertPriceRequest) ProtoMessage() {}

func (x *PricingServiceInvertPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingServiceInvertPriceRequest.ProtoReflect.Descriptor instead.
func (*PricingServiceInvertPriceRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *PricingServiceInvertPriceRequest) GetLeague() string {
	if x != nil {
		return x.League
	}
	return ""
}

func (x *PricingServiceInvertPriceRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *PricingServiceInvertPriceRequest) GetTargetPriceCents() int32 {
	if x != nil {
		return x.TargetPriceCents
	}
	return 0
}

func (x *PricingServiceInvertPriceRequest) GetUnitCostCents() int32 {
	if x != nil {
		return x.UnitCostCents
	}
	return 0
}

func (x *PricingServiceInvertPriceRequest) GetSellFee() float64 {
	if x != nil {
		return x.SellFee
	}
	return 0
}

// Deprecated: Marked as deprecated in gametime_protos/pandc/pricing/v1/service.proto.
func (x *PricingServiceInvertPriceRequest) GetIsSpec() bool {
	if x != nil {
		return x.IsSpec
	}
	return false
}

func (x *PricingServiceInvertPriceRequest) GetIsHighOpsCost() bool {
	if x != nil {
		return x.IsHighOpsCost
	}
	return false
}

type PricingServiceInvertPriceResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TargetPromoFee float64                `protobuf:"fixed64,1,opt,name=target_promo_fee,json=targetPromoFee,proto3" json:"target_promo_fee,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PricingServiceInvertPriceResponse) Reset() {
	*x = PricingServiceInvertPriceResponse{}
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PricingServiceInvertPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingServiceInvertPriceResponse) ProtoMessage() {}

func (x *PricingServiceInvertPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingServiceInvertPriceResponse.ProtoReflect.Descriptor instead.
func (*PricingServiceInvertPriceResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *PricingServiceInvertPriceResponse) GetTargetPromoFee() float64 {
	if x != nil {
		return x.TargetPromoFee
	}
	return 0
}

type PricingServiceUpdateMarkupsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Markups       map[string]float32     `protobuf:"bytes,1,rep,name=markups,proto3" json:"markups,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PricingServiceUpdateMarkupsRequest) Reset() {
	*x = PricingServiceUpdateMarkupsRequest{}
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PricingServiceUpdateMarkupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingServiceUpdateMarkupsRequest) ProtoMessage() {}

func (x *PricingServiceUpdateMarkupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingServiceUpdateMarkupsRequest.ProtoReflect.Descriptor instead.
func (*PricingServiceUpdateMarkupsRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *PricingServiceUpdateMarkupsRequest) GetMarkups() map[string]float32 {
	if x != nil {
		return x.Markups
	}
	return nil
}

type PricingServiceUpdateMarkupsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PricingServiceUpdateMarkupsResponse) Reset() {
	*x = PricingServiceUpdateMarkupsResponse{}
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PricingServiceUpdateMarkupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingServiceUpdateMarkupsResponse) ProtoMessage() {}

func (x *PricingServiceUpdateMarkupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingServiceUpdateMarkupsResponse.ProtoReflect.Descriptor instead.
func (*PricingServiceUpdateMarkupsResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP(), []int{5}
}

type PricingServiceFetchSettingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PricingServiceFetchSettingsRequest) Reset() {
	*x = PricingServiceFetchSettingsRequest{}
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PricingServiceFetchSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingServiceFetchSettingsRequest) ProtoMessage() {}

func (x *PricingServiceFetchSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingServiceFetchSettingsRequest.ProtoReflect.Descriptor instead.
func (*PricingServiceFetchSettingsRequest) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP(), []int{6}
}

type PricingServiceFetchSettingsResponse struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	DefaultMarkup        float64                `protobuf:"fixed64,1,opt,name=default_markup,json=defaultMarkup,proto3" json:"default_markup,omitempty"`
	DefaultLeagueMarkups map[string]float64     `protobuf:"bytes,2,rep,name=default_league_markups,json=defaultLeagueMarkups,proto3" json:"default_league_markups,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MinMarkup            float64                `protobuf:"fixed64,3,opt,name=min_markup,json=minMarkup,proto3" json:"min_markup,omitempty"`
	MaxMarkup            float64                `protobuf:"fixed64,4,opt,name=max_markup,json=maxMarkup,proto3" json:"max_markup,omitempty"`
	// Deprecated: Marked as deprecated in gametime_protos/pandc/pricing/v1/service.proto.
	SpecMarkup        float64 `protobuf:"fixed64,5,opt,name=spec_markup,json=specMarkup,proto3" json:"spec_markup,omitempty"`
	InterceptCents    int32   `protobuf:"varint,6,opt,name=intercept_cents,json=interceptCents,proto3" json:"intercept_cents,omitempty"`
	HighOpsCostMarkup float64 `protobuf:"fixed64,7,opt,name=high_ops_cost_markup,json=highOpsCostMarkup,proto3" json:"high_ops_cost_markup,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PricingServiceFetchSettingsResponse) Reset() {
	*x = PricingServiceFetchSettingsResponse{}
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PricingServiceFetchSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PricingServiceFetchSettingsResponse) ProtoMessage() {}

func (x *PricingServiceFetchSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PricingServiceFetchSettingsResponse.ProtoReflect.Descriptor instead.
func (*PricingServiceFetchSettingsResponse) Descriptor() ([]byte, []int) {
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *PricingServiceFetchSettingsResponse) GetDefaultMarkup() float64 {
	if x != nil {
		return x.DefaultMarkup
	}
	return 0
}

func (x *PricingServiceFetchSettingsResponse) GetDefaultLeagueMarkups() map[string]float64 {
	if x != nil {
		return x.DefaultLeagueMarkups
	}
	return nil
}

func (x *PricingServiceFetchSettingsResponse) GetMinMarkup() float64 {
	if x != nil {
		return x.MinMarkup
	}
	return 0
}

func (x *PricingServiceFetchSettingsResponse) GetMaxMarkup() float64 {
	if x != nil {
		return x.MaxMarkup
	}
	return 0
}

// Deprecated: Marked as deprecated in gametime_protos/pandc/pricing/v1/service.proto.
func (x *PricingServiceFetchSettingsResponse) GetSpecMarkup() float64 {
	if x != nil {
		return x.SpecMarkup
	}
	return 0
}

func (x *PricingServiceFetchSettingsResponse) GetInterceptCents() int32 {
	if x != nil {
		return x.InterceptCents
	}
	return 0
}

func (x *PricingServiceFetchSettingsResponse) GetHighOpsCostMarkup() float64 {
	if x != nil {
		return x.HighOpsCostMarkup
	}
	return 0
}

var File_gametime_protos_pandc_pricing_v1_service_proto protoreflect.FileDescriptor

const file_gametime_protos_pandc_pricing_v1_service_proto_rawDesc = "" +
	"\n" +
	".gametime_protos/pandc/pricing/v1/service.proto\x12\x10pandc.pricing.v1\"k\n" +
	"\x1aPricingServicePriceRequest\x12\x16\n" +
	"\x06league\x18\x01 \x01(\tR\x06league\x12\x19\n" +
	"\bevent_id\x18\x02 \x01(\tR\aeventId\x12\x1a\n" +
	"\blistings\x18\x03 \x01(\fR\blistings\"O\n" +
	"\x1bPricingServicePriceResponse\x12\x16\n" +
	"\x06prices\x18\x01 \x01(\fR\x06prices\x12\x18\n" +
	"\aresults\x18\x02 \x01(\fR\aresults\"\x8c\x02\n" +
	" PricingServiceInvertPriceRequest\x12\x16\n" +
	"\x06league\x18\x01 \x01(\tR\x06league\x12\x19\n" +
	"\bevent_id\x18\x02 \x01(\tR\aeventId\x12,\n" +
	"\x12target_price_cents\x18\x03 \x01(\x05R\x10targetPriceCents\x12&\n" +
	"\x0funit_cost_cents\x18\x04 \x01(\x05R\runitCostCents\x12\x19\n" +
	"\bsell_fee\x18\x05 \x01(\x01R\asellFee\x12\x1b\n" +
	"\ais_spec\x18\x06 \x01(\bB\x02\x18\x01R\x06isSpec\x12'\n" +
	"\x10is_high_ops_cost\x18\a \x01(\bR\risHighOpsCost\"M\n" +
	"!PricingServiceInvertPriceResponse\x12(\n" +
	"\x10target_promo_fee\x18\x01 \x01(\x01R\x0etargetPromoFee\"\xbd\x01\n" +
	"\"PricingServiceUpdateMarkupsRequest\x12[\n" +
	"\amarkups\x18\x01 \x03(\v2A.pandc.pricing.v1.PricingServiceUpdateMarkupsRequest.MarkupsEntryR\amarkups\x1a:\n" +
	"\fMarkupsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x02R\x05value:\x028\x01\"%\n" +
	"#PricingServiceUpdateMarkupsResponse\"$\n" +
	"\"PricingServiceFetchSettingsRequest\"\xda\x03\n" +
	"#PricingServiceFetchSettingsResponse\x12%\n" +
	"\x0edefault_markup\x18\x01 \x01(\x01R\rdefaultMarkup\x12\x85\x01\n" +
	"\x16default_league_markups\x18\x02 \x03(\v2O.pandc.pricing.v1.PricingServiceFetchSettingsResponse.DefaultLeagueMarkupsEntryR\x14defaultLeagueMarkups\x12\x1d\n" +
	"\n" +
	"min_markup\x18\x03 \x01(\x01R\tminMarkup\x12\x1d\n" +
	"\n" +
	"max_markup\x18\x04 \x01(\x01R\tmaxMarkup\x12#\n" +
	"\vspec_markup\x18\x05 \x01(\x01B\x02\x18\x01R\n" +
	"specMarkup\x12'\n" +
	"\x0fintercept_cents\x18\x06 \x01(\x05R\x0einterceptCents\x12/\n" +
	"\x14high_ops_cost_markup\x18\a \x01(\x01R\x11highOpsCostMarkup\x1aG\n" +
	"\x19DefaultLeagueMarkupsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value:\x028\x012\xf2\x03\n" +
	"\x0ePricingService\x12f\n" +
	"\x05Price\x12,.pandc.pricing.v1.PricingServicePriceRequest\x1a-.pandc.pricing.v1.PricingServicePriceResponse\"\x00\x12x\n" +
	"\vInvertPrice\x122.pandc.pricing.v1.PricingServiceInvertPriceRequest\x1a3.pandc.pricing.v1.PricingServiceInvertPriceResponse\"\x00\x12~\n" +
	"\rFetchSettings\x124.pandc.pricing.v1.PricingServiceFetchSettingsRequest\x1a5.pandc.pricing.v1.PricingServiceFetchSettingsResponse\"\x00\x12~\n" +
	"\rUpdateMarkups\x124.pandc.pricing.v1.PricingServiceUpdateMarkupsRequest\x1a5.pandc.pricing.v1.PricingServiceUpdateMarkupsResponse\"\x00B\x9f\x01\n" +
	"\x14com.pandc.pricing.v1B\fServiceProtoP\x01Z\x17pandc/pricing/v1;protos\xa2\x02\x03PPX\xaa\x02\x10Pandc.Pricing.V1\xca\x02\x10Pandc\\Pricing\\V1\xe2\x02\x1cPandc\\Pricing\\V1\\GPBMetadata\xea\x02\x12Pandc::Pricing::V1b\x06proto3"

var (
	file_gametime_protos_pandc_pricing_v1_service_proto_rawDescOnce sync.Once
	file_gametime_protos_pandc_pricing_v1_service_proto_rawDescData []byte
)

func file_gametime_protos_pandc_pricing_v1_service_proto_rawDescGZIP() []byte {
	file_gametime_protos_pandc_pricing_v1_service_proto_rawDescOnce.Do(func() {
		file_gametime_protos_pandc_pricing_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gametime_protos_pandc_pricing_v1_service_proto_rawDesc), len(file_gametime_protos_pandc_pricing_v1_service_proto_rawDesc)))
	})
	return file_gametime_protos_pandc_pricing_v1_service_proto_rawDescData
}

var file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_gametime_protos_pandc_pricing_v1_service_proto_goTypes = []any{
	(*PricingServicePriceRequest)(nil),          // 0: pandc.pricing.v1.PricingServicePriceRequest
	(*PricingServicePriceResponse)(nil),         // 1: pandc.pricing.v1.PricingServicePriceResponse
	(*PricingServiceInvertPriceRequest)(nil),    // 2: pandc.pricing.v1.PricingServiceInvertPriceRequest
	(*PricingServiceInvertPriceResponse)(nil),   // 3: pandc.pricing.v1.PricingServiceInvertPriceResponse
	(*PricingServiceUpdateMarkupsRequest)(nil),  // 4: pandc.pricing.v1.PricingServiceUpdateMarkupsRequest
	(*PricingServiceUpdateMarkupsResponse)(nil), // 5: pandc.pricing.v1.PricingServiceUpdateMarkupsResponse
	(*PricingServiceFetchSettingsRequest)(nil),  // 6: pandc.pricing.v1.PricingServiceFetchSettingsRequest
	(*PricingServiceFetchSettingsResponse)(nil), // 7: pandc.pricing.v1.PricingServiceFetchSettingsResponse
	nil, // 8: pandc.pricing.v1.PricingServiceUpdateMarkupsRequest.MarkupsEntry
	nil, // 9: pandc.pricing.v1.PricingServiceFetchSettingsResponse.DefaultLeagueMarkupsEntry
}
var file_gametime_protos_pandc_pricing_v1_service_proto_depIdxs = []int32{
	8, // 0: pandc.pricing.v1.PricingServiceUpdateMarkupsRequest.markups:type_name -> pandc.pricing.v1.PricingServiceUpdateMarkupsRequest.MarkupsEntry
	9, // 1: pandc.pricing.v1.PricingServiceFetchSettingsResponse.default_league_markups:type_name -> pandc.pricing.v1.PricingServiceFetchSettingsResponse.DefaultLeagueMarkupsEntry
	0, // 2: pandc.pricing.v1.PricingService.Price:input_type -> pandc.pricing.v1.PricingServicePriceRequest
	2, // 3: pandc.pricing.v1.PricingService.InvertPrice:input_type -> pandc.pricing.v1.PricingServiceInvertPriceRequest
	6, // 4: pandc.pricing.v1.PricingService.FetchSettings:input_type -> pandc.pricing.v1.PricingServiceFetchSettingsRequest
	4, // 5: pandc.pricing.v1.PricingService.UpdateMarkups:input_type -> pandc.pricing.v1.PricingServiceUpdateMarkupsRequest
	1, // 6: pandc.pricing.v1.PricingService.Price:output_type -> pandc.pricing.v1.PricingServicePriceResponse
	3, // 7: pandc.pricing.v1.PricingService.InvertPrice:output_type -> pandc.pricing.v1.PricingServiceInvertPriceResponse
	7, // 8: pandc.pricing.v1.PricingService.FetchSettings:output_type -> pandc.pricing.v1.PricingServiceFetchSettingsResponse
	5, // 9: pandc.pricing.v1.PricingService.UpdateMarkups:output_type -> pandc.pricing.v1.PricingServiceUpdateMarkupsResponse
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_gametime_protos_pandc_pricing_v1_service_proto_init() }
func file_gametime_protos_pandc_pricing_v1_service_proto_init() {
	if File_gametime_protos_pandc_pricing_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gametime_protos_pandc_pricing_v1_service_proto_rawDesc), len(file_gametime_protos_pandc_pricing_v1_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gametime_protos_pandc_pricing_v1_service_proto_goTypes,
		DependencyIndexes: file_gametime_protos_pandc_pricing_v1_service_proto_depIdxs,
		MessageInfos:      file_gametime_protos_pandc_pricing_v1_service_proto_msgTypes,
	}.Build()
	File_gametime_protos_pandc_pricing_v1_service_proto = out.File
	file_gametime_protos_pandc_pricing_v1_service_proto_goTypes = nil
	file_gametime_protos_pandc_pricing_v1_service_proto_depIdxs = nil
}
