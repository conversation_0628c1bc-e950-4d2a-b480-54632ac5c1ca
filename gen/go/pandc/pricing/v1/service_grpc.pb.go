// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: gametime_protos/pandc/pricing/v1/service.proto

package protos

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PricingService_Price_FullMethodName         = "/pandc.pricing.v1.PricingService/Price"
	PricingService_InvertPrice_FullMethodName   = "/pandc.pricing.v1.PricingService/InvertPrice"
	PricingService_FetchSettings_FullMethodName = "/pandc.pricing.v1.PricingService/FetchSettings"
	PricingService_UpdateMarkups_FullMethodName = "/pandc.pricing.v1.PricingService/UpdateMarkups"
)

// PricingServiceClient is the client API for PricingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PricingServiceClient interface {
	Price(ctx context.Context, in *PricingServicePriceRequest, opts ...grpc.CallOption) (*PricingServicePriceResponse, error)
	InvertPrice(ctx context.Context, in *PricingServiceInvertPriceRequest, opts ...grpc.CallOption) (*PricingServiceInvertPriceResponse, error)
	FetchSettings(ctx context.Context, in *PricingServiceFetchSettingsRequest, opts ...grpc.CallOption) (*PricingServiceFetchSettingsResponse, error)
	UpdateMarkups(ctx context.Context, in *PricingServiceUpdateMarkupsRequest, opts ...grpc.CallOption) (*PricingServiceUpdateMarkupsResponse, error)
}

type pricingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingServiceClient(cc grpc.ClientConnInterface) PricingServiceClient {
	return &pricingServiceClient{cc}
}

func (c *pricingServiceClient) Price(ctx context.Context, in *PricingServicePriceRequest, opts ...grpc.CallOption) (*PricingServicePriceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PricingServicePriceResponse)
	err := c.cc.Invoke(ctx, PricingService_Price_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingServiceClient) InvertPrice(ctx context.Context, in *PricingServiceInvertPriceRequest, opts ...grpc.CallOption) (*PricingServiceInvertPriceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PricingServiceInvertPriceResponse)
	err := c.cc.Invoke(ctx, PricingService_InvertPrice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingServiceClient) FetchSettings(ctx context.Context, in *PricingServiceFetchSettingsRequest, opts ...grpc.CallOption) (*PricingServiceFetchSettingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PricingServiceFetchSettingsResponse)
	err := c.cc.Invoke(ctx, PricingService_FetchSettings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingServiceClient) UpdateMarkups(ctx context.Context, in *PricingServiceUpdateMarkupsRequest, opts ...grpc.CallOption) (*PricingServiceUpdateMarkupsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PricingServiceUpdateMarkupsResponse)
	err := c.cc.Invoke(ctx, PricingService_UpdateMarkups_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingServiceServer is the server API for PricingService service.
// All implementations must embed UnimplementedPricingServiceServer
// for forward compatibility.
type PricingServiceServer interface {
	Price(context.Context, *PricingServicePriceRequest) (*PricingServicePriceResponse, error)
	InvertPrice(context.Context, *PricingServiceInvertPriceRequest) (*PricingServiceInvertPriceResponse, error)
	FetchSettings(context.Context, *PricingServiceFetchSettingsRequest) (*PricingServiceFetchSettingsResponse, error)
	UpdateMarkups(context.Context, *PricingServiceUpdateMarkupsRequest) (*PricingServiceUpdateMarkupsResponse, error)
	mustEmbedUnimplementedPricingServiceServer()
}

// UnimplementedPricingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPricingServiceServer struct{}

func (UnimplementedPricingServiceServer) Price(context.Context, *PricingServicePriceRequest) (*PricingServicePriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Price not implemented")
}
func (UnimplementedPricingServiceServer) InvertPrice(context.Context, *PricingServiceInvertPriceRequest) (*PricingServiceInvertPriceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InvertPrice not implemented")
}
func (UnimplementedPricingServiceServer) FetchSettings(context.Context, *PricingServiceFetchSettingsRequest) (*PricingServiceFetchSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchSettings not implemented")
}
func (UnimplementedPricingServiceServer) UpdateMarkups(context.Context, *PricingServiceUpdateMarkupsRequest) (*PricingServiceUpdateMarkupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarkups not implemented")
}
func (UnimplementedPricingServiceServer) mustEmbedUnimplementedPricingServiceServer() {}
func (UnimplementedPricingServiceServer) testEmbeddedByValue()                        {}

// UnsafePricingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PricingServiceServer will
// result in compilation errors.
type UnsafePricingServiceServer interface {
	mustEmbedUnimplementedPricingServiceServer()
}

func RegisterPricingServiceServer(s grpc.ServiceRegistrar, srv PricingServiceServer) {
	// If the following call pancis, it indicates UnimplementedPricingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PricingService_ServiceDesc, srv)
}

func _PricingService_Price_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PricingServicePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServiceServer).Price(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PricingService_Price_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServiceServer).Price(ctx, req.(*PricingServicePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingService_InvertPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PricingServiceInvertPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServiceServer).InvertPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PricingService_InvertPrice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServiceServer).InvertPrice(ctx, req.(*PricingServiceInvertPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingService_FetchSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PricingServiceFetchSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServiceServer).FetchSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PricingService_FetchSettings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServiceServer).FetchSettings(ctx, req.(*PricingServiceFetchSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingService_UpdateMarkups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PricingServiceUpdateMarkupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServiceServer).UpdateMarkups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PricingService_UpdateMarkups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServiceServer).UpdateMarkups(ctx, req.(*PricingServiceUpdateMarkupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PricingService_ServiceDesc is the grpc.ServiceDesc for PricingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PricingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pandc.pricing.v1.PricingService",
	HandlerType: (*PricingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Price",
			Handler:    _PricingService_Price_Handler,
		},
		{
			MethodName: "InvertPrice",
			Handler:    _PricingService_InvertPrice_Handler,
		},
		{
			MethodName: "FetchSettings",
			Handler:    _PricingService_FetchSettings_Handler,
		},
		{
			MethodName: "UpdateMarkups",
			Handler:    _PricingService_UpdateMarkups_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gametime_protos/pandc/pricing/v1/service.proto",
}
