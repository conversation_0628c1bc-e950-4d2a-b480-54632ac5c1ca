# CHANGELOG


## v1.5.0 (2025-05-07)

### Chores

- **protos**: Generate code from proto definitions
  ([`5bee260`](https://github.com/gametimesf/protos/commit/5bee260aae4556348b6e6219250283e0cffd90b4))

### Features

- Adding curation options to curation request
  ([`51f794a`](https://github.com/gametimesf/protos/commit/51f794aca4d5a69d672c3b3edd4de83a69f87d02))


## v1.4.0 (2025-05-07)

### Chores

- **protos**: Generate code from proto definitions
  ([`eed48a7`](https://github.com/gametimesf/protos/commit/eed48a704f8d4ec66f16108ab6b1a103f13685a0))

### Features

- Add spine_s3_uri field to OfflineFeaturesServiceCreateDatasetRequest
  ([`bb41354`](https://github.com/gametimesf/protos/commit/bb41354e5d70484fd12bce0a698b339290379156))


## v1.3.0 (2025-04-24)

### Bug Fixes

- Update set response
  ([`a8a3e0e`](https://github.com/gametimesf/protos/commit/a8a3e0e006029ca2db1909475251e5e0a66ec3dd))

return code, message, & details on set response

- Use grpc status in set response
  ([`28e3619`](https://github.com/gametimesf/protos/commit/28e36195a135e0cf39c0fc7199bee76b90afb05f))

updated to use grpc status instead of bool success + string error in set response

### Chores

- **protos**: Generate code from proto definitions
  ([`85ed5a2`](https://github.com/gametimesf/protos/commit/85ed5a23db867c57eba37d46a56ac0dd254d79f2))

### Features

- Add initial protos for the new data-api service
  ([`fdf7f2b`](https://github.com/gametimesf/protos/commit/fdf7f2b80ce94aee38214304434f9333a00f60c5))

added GenericService proto definitions for the new data-api service


## v1.2.1 (2025-04-23)

### Bug Fixes

- Update version to 1.2.0
  ([`a22bff7`](https://github.com/gametimesf/protos/commit/a22bff79ca3fdb283cb2286a7d68a43ee531c489))


## v0.9.0 (2025-04-23)

### Bug Fixes

- Downgrade protobuf dependencies for ecosystem compatibility
  ([#45](https://github.com/gametimesf/protos/pull/45),
  [`f1df0dc`](https://github.com/gametimesf/protos/commit/f1df0dc0610bc965d2709690ecfafc622c82b202))

- Gametime_protos versioning to 1.2.0 ([#44](https://github.com/gametimesf/protos/pull/44),
  [`8b1a1f9`](https://github.com/gametimesf/protos/commit/8b1a1f9dfce5c661946ae41f277c7de11b7d4bcd))

### Chores

- **protos**: Generate code from proto definitions
  ([`c5151e1`](https://github.com/gametimesf/protos/commit/c5151e16c6857d447cb70c92667dd6de7b3194fe))

### Features

- Add manual release workflow ([#46](https://github.com/gametimesf/protos/pull/46),
  [`8ff95d5`](https://github.com/gametimesf/protos/commit/8ff95d54d3df3028cf97d4d03a24b6a9c4c2ad33))

* feat: add manual release workflow


## v0.8.0 (2025-04-22)

### Chores

- **protos**: Generate code from proto definitions
  ([`c773023`](https://github.com/gametimesf/protos/commit/c773023c2107618cfb048f24cfb8350d0f136f33))

### Features

- Fetchconnectioninfo for jupyter service ([#43](https://github.com/gametimesf/protos/pull/43),
  [`a86c36a`](https://github.com/gametimesf/protos/commit/a86c36a33c94c8cd685c6c141ae1b06d83756352))


## v0.7.0 (2025-04-16)

### Chores

- **protos**: Generate code from proto definitions
  ([`7e2304d`](https://github.com/gametimesf/protos/commit/7e2304dd298ef1f987cd531d92e601c828cf49df))

### Features

- Manually invoke code artifact ci
  ([`23c8c47`](https://github.com/gametimesf/protos/commit/23c8c47193c472352a11ed6138cef0c9b74f3057))


## v0.6.0 (2025-04-16)

### Chores

- **protos**: Generate code from proto definitions
  ([`4c0db9a`](https://github.com/gametimesf/protos/commit/4c0db9a020f3770d961d150d2dcd00b0df2a21d0))

- **protos**: Generate code from proto definitions
  ([`f692e88`](https://github.com/gametimesf/protos/commit/f692e88706e427be9933c79fcd8b365c79026ee5))

### Features

- Manually invoke code artifact ci
  ([`ee6e10e`](https://github.com/gametimesf/protos/commit/ee6e10ea1db9be5f501361be34a9f0439608c3f0))

- Manually invoke code artifact ci
  ([`3ce13bd`](https://github.com/gametimesf/protos/commit/3ce13bdaabc698c4ab6f126457dd71a28f5e762c))

### Refactoring

- Simplify log fetching to use unary RPC instead of streaming
  ([#42](https://github.com/gametimesf/protos/pull/42),
  [`40563a7`](https://github.com/gametimesf/protos/commit/40563a7a6243ba030ed05531f58f87315e10eefe))


## v0.5.0 (2025-04-09)

### Features

- Change data to results
  ([`7486f08`](https://github.com/gametimesf/protos/commit/7486f0847321bef82e5cbdaaf859aed6f367f054))


## v0.4.0 (2025-04-09)

### Bug Fixes

- **ci**: Prevent workflow failure when no generated files changed
  ([`1c83094`](https://github.com/gametimesf/protos/commit/1c830943439b7a424b8868c95100086125c34b11))

### Chores

- **protos**: Generate code from proto definitions
  ([`ff71ab1`](https://github.com/gametimesf/protos/commit/ff71ab19d72c75272254244876a2d58b26312c50))

### Features

- Add prices without promofees
  ([`faa8c03`](https://github.com/gametimesf/protos/commit/faa8c036d8e9e0954261bb5756695bb07b20b53f))

- Bump sem ver
  ([`125700c`](https://github.com/gametimesf/protos/commit/125700cf1297706070f8f408e96b81bc6d9683f5))

### Refactoring

- Rename statsheet to prism
  ([`be6810c`](https://github.com/gametimesf/protos/commit/be6810cbc374ae1ce68756cd849261a7a65b487a))


## v0.3.0 (2025-04-02)

### Chores

- **protos**: Generate code from proto definitions
  ([`6e13bc0`](https://github.com/gametimesf/protos/commit/6e13bc036b8e0c8410abb06e368de4d9a02e0998))

### Features

- Correct github tf role
  ([`f7e199d`](https://github.com/gametimesf/protos/commit/f7e199dd1a4f59ef2a57cf3d3e746fd555749f86))

### Testing

- Correct role for codeartifact
  ([`c5b004d`](https://github.com/gametimesf/protos/commit/c5b004d9f2b0452a6fb312dfe4d1815c5ed06cc9))


## v0.2.0 (2025-04-02)

### Bug Fixes

- Grpio dependency skew between protos and curation
  ([`e8d268a`](https://github.com/gametimesf/protos/commit/e8d268a7d79e41df76fcc82b087581434613064d))

### Chores

- **protos**: Generate code from proto definitions
  ([`c63937c`](https://github.com/gametimesf/protos/commit/c63937caedfd6afbf6eec312e185fa0dd6472309))

### Features

- Correct buf yaml ([#36](https://github.com/gametimesf/protos/pull/36),
  [`eb9525a`](https://github.com/gametimesf/protos/commit/eb9525acc27fd0f2e0e01dfd8dce17b7b6196035))

- Correct buf yaml name ([#37](https://github.com/gametimesf/protos/pull/37),
  [`019375c`](https://github.com/gametimesf/protos/commit/019375ca35b1c60e2941177c8d9d5f97d8a9859e))

* feat: correct buf yaml name

* feat: test update

- Correct code artifact role
  ([`ed20bd8`](https://github.com/gametimesf/protos/commit/ed20bd8eb4ca3e1cc2515e92048452633a51abb1))

- Test semver release
  ([`7132c0c`](https://github.com/gametimesf/protos/commit/7132c0ccd2d4a19d80882a0dc129f75e14775d2d))


## v0.1.0 (2025-03-26)

### Bug Fixes

- Attempt to resolve ModuleNotFoundError: No module named 'gametime_protos' by renaming
  gametime-protos to gametime_protos
  ([`efa9b55`](https://github.com/gametimesf/protos/commit/efa9b55d1abd006017c48b28c0cbb5af86f49595))

- Correct pyproject version
  ([`bead2bb`](https://github.com/gametimesf/protos/commit/bead2bbd8fc043a7fa7dd24329400e2ad9e6def0))

### Chores

- **protos**: Generate code from proto definitions
  ([`81fd315`](https://github.com/gametimesf/protos/commit/81fd315070b29b19451c018065e81fa3738fad1d))

- **protos**: Generate code from proto definitions
  ([`d6e9657`](https://github.com/gametimesf/protos/commit/d6e9657b103d2fe451a608af4a79e0a777266d16))

- **protos**: Generate code from proto definitions
  ([`8754b1c`](https://github.com/gametimesf/protos/commit/8754b1c428af53d855e2d5b79b007bfb8cdb3272))

- **protos**: Generate code from proto definitions
  ([`8999a51`](https://github.com/gametimesf/protos/commit/8999a51bc0f386c29dec1fbda205e6f53683ff0f))

- **protos**: Generate code from proto definitions
  ([`97ed6aa`](https://github.com/gametimesf/protos/commit/97ed6aa4fde8c57208337aa7895480fbf826f913))

- **protos**: Generate code from proto definitions
  ([`451e11d`](https://github.com/gametimesf/protos/commit/451e11d056015a19267a2aba39a18555241d082e))

- **protos**: Generate code from proto definitions
  ([`225a4f8`](https://github.com/gametimesf/protos/commit/225a4f8ca1b88ad011156696e90ba19724b3128f))

- **protos**: Generate code from proto definitions
  ([`ca6d80c`](https://github.com/gametimesf/protos/commit/ca6d80cf57be46ffd2e0a39383d756aee4137635))

- **protos**: Generate code from proto definitions
  ([`07b48b5`](https://github.com/gametimesf/protos/commit/07b48b5367e34d5bf9bfd9c5db93d297b50ad1cc))

- **protos**: Generate code from proto definitions
  ([`59b9951`](https://github.com/gametimesf/protos/commit/59b99516f7f4879307581a29039234f71b847c18))

- **protos**: Generate code from proto definitions
  ([`cc3cde0`](https://github.com/gametimesf/protos/commit/cc3cde0b4d2c20e645da5b853489cc1f624b1a57))

- **protos**: Generate code from proto definitions
  ([`b806e5e`](https://github.com/gametimesf/protos/commit/b806e5e2ce52636ca9157ee1a2ed7d62b9cbd450))

- **protos**: Generate code from proto definitions
  ([`30d6dc6`](https://github.com/gametimesf/protos/commit/30d6dc625f5065c04e3b90ecc6b13a0eb3787625))

- **protos**: Generate code from proto definitions
  ([`ea55691`](https://github.com/gametimesf/protos/commit/ea556914e57cd3957a498a61b59be5bedd051dc2))

- **protos**: Generate code from proto definitions
  ([`347b2ca`](https://github.com/gametimesf/protos/commit/347b2ca1832794edc2ab67ac0bce1aa331743482))

- **protos**: Generate code from proto definitions
  ([`e10a6e4`](https://github.com/gametimesf/protos/commit/e10a6e4feb2e558e9a8f2219331cd784d317657e))

- **protos**: Generate code from proto definitions
  ([`dfec730`](https://github.com/gametimesf/protos/commit/dfec73069e10a82301c05c68cf8caa70e622915e))

### Features

- Add newline to force ci
  ([`e9b54c5`](https://github.com/gametimesf/protos/commit/e9b54c5dd8a018087d6d24bdf80610d5e6883aa6))

- Add Proto Definitions for Job Logs Feature
  ([`e1d7a34`](https://github.com/gametimesf/protos/commit/e1d7a345a37614638cea49697e7f8858e8bca8df))

- Add semantic release GitHub action for python package
  ([`2fff474`](https://github.com/gametimesf/protos/commit/2fff474b2cf183dd2093ff1cd5f49c846aa8d6c8))

- Change pricing data types
  ([`b514279`](https://github.com/gametimesf/protos/commit/b5142799386977023ae586174b65b9b2edd16361))

- Correct sem ver ci tag ref
  ([`3c733a4`](https://github.com/gametimesf/protos/commit/3c733a49fa1c9ff3398aa626329f88713f3cf3b4))

- Enforce conventional commits
  ([`ec2bdd9`](https://github.com/gametimesf/protos/commit/ec2bdd9b89167bc3daeace528eae61a41b16537e))

- Manual increment gametime_protos to invoke ci
  ([`c79c232`](https://github.com/gametimesf/protos/commit/c79c2324b48b69906b0bbe05ddf793e1a7118f1d))

- Pr feedback
  ([`74bb74e`](https://github.com/gametimesf/protos/commit/74bb74e64d291f0931107bbd13673a8e0873ec03))

- Remove value score fro relevanc to invoke ci
  ([`8e25e38`](https://github.com/gametimesf/protos/commit/8e25e3834552856eac2f0c79e23be76021c0633e))

### Testing

- Add this branch to test workflow
  ([`41ac89a`](https://github.com/gametimesf/protos/commit/41ac89a0f2f30e4f218abd058c22ec052ebc44cd))

- Cleanup
  ([`29518ed`](https://github.com/gametimesf/protos/commit/29518ede1d223f99fde718fccfbe3b60acc986a6))

- Disable step conditional
  ([`8a0bf6e`](https://github.com/gametimesf/protos/commit/8a0bf6e239ca7af5b2c8f9d9a19058f32a38001f))

- Reference version tag for reusable workflow
  ([`8cefd61`](https://github.com/gametimesf/protos/commit/8cefd619eac6c597c6733efcfc1f2adf8c54660b))

- Temp remove other test conditionals
  ([`160debe`](https://github.com/gametimesf/protos/commit/160debea03e3947338bdbca6020dc2f4e36d000f))

- Trigger semantic release on branch push
  ([`c8df483`](https://github.com/gametimesf/protos/commit/c8df48396a115fafe38b7880123fc142a4a40bd8))
