# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: gametime_protos/dataapi/entities/v1/service.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'gametime_protos/dataapi/entities/v1/service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1gametime_protos/dataapi/entities/v1/service.proto\x12\x13\x64\x61taapi.entities.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x19google/protobuf/any.proto\"9\n\x06\x45ntity\x12/\n\x06\x66ields\x18\x01 \x01(\x0b\x32\x17.google.protobuf.StructR\x06\x66ields\"-\n\x19GetGenericEntitiesRequest\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\"U\n\x1aGetGenericEntitiesResponse\x12\x37\n\x08\x65ntities\x18\x01 \x03(\x0b\x32\x1b.dataapi.entities.v1.EntityR\x08\x65ntities\"f\n\x19SetGenericEntitiesRequest\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x37\n\x08\x65ntities\x18\x02 \x03(\x0b\x32\x1b.dataapi.entities.v1.EntityR\x08\x65ntities\"z\n\x1aSetGenericEntitiesResponse\x12\x12\n\x04\x63ode\x18\x01 \x01(\x05R\x04\x63ode\x12\x18\n\x07message\x18\x02 \x01(\tR\x07message\x12.\n\x07\x64\x65tails\x18\x03 \x03(\x0b\x32\x14.google.protobuf.AnyR\x07\x64\x65tails2\xfe\x01\n\x0eGenericService\x12u\n\x12GetGenericEntities\x12..dataapi.entities.v1.GetGenericEntitiesRequest\x1a/.dataapi.entities.v1.GetGenericEntitiesResponse\x12u\n\x12SetGenericEntities\x12..dataapi.entities.v1.SetGenericEntitiesRequest\x1a/.dataapi.entities.v1.SetGenericEntitiesResponseB\xb1\x01\n\x17\x63om.dataapi.entities.v1B\x0cServiceProtoP\x01Z\x1a\x64\x61taapi/entities/v1;protos\xa2\x02\x03\x44\x45X\xaa\x02\x13\x44\x61taapi.Entities.V1\xca\x02\x13\x44\x61taapi\\Entities\\V1\xe2\x02\x1f\x44\x61taapi\\Entities\\V1\\GPBMetadata\xea\x02\x15\x44\x61taapi::Entities::V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gametime_protos.dataapi.entities.v1.service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\027com.dataapi.entities.v1B\014ServiceProtoP\001Z\032dataapi/entities/v1;protos\242\002\003DEX\252\002\023Dataapi.Entities.V1\312\002\023Dataapi\\Entities\\V1\342\002\037Dataapi\\Entities\\V1\\GPBMetadata\352\002\025Dataapi::Entities::V1'
  _globals['_ENTITY']._serialized_start=131
  _globals['_ENTITY']._serialized_end=188
  _globals['_GETGENERICENTITIESREQUEST']._serialized_start=190
  _globals['_GETGENERICENTITIESREQUEST']._serialized_end=235
  _globals['_GETGENERICENTITIESRESPONSE']._serialized_start=237
  _globals['_GETGENERICENTITIESRESPONSE']._serialized_end=322
  _globals['_SETGENERICENTITIESREQUEST']._serialized_start=324
  _globals['_SETGENERICENTITIESREQUEST']._serialized_end=426
  _globals['_SETGENERICENTITIESRESPONSE']._serialized_start=428
  _globals['_SETGENERICENTITIESRESPONSE']._serialized_end=550
  _globals['_GENERICSERVICE']._serialized_start=553
  _globals['_GENERICSERVICE']._serialized_end=807
# @@protoc_insertion_point(module_scope)
