# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from gametime_protos.dataapi.entities.v1 import service_pb2 as gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2


class GenericServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetGenericEntities = channel.unary_unary(
                '/dataapi.entities.v1.GenericService/GetGenericEntities',
                request_serializer=gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.GetGenericEntitiesRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.GetGenericEntitiesResponse.FromString,
                _registered_method=True)
        self.SetGenericEntities = channel.unary_unary(
                '/dataapi.entities.v1.GenericService/SetGenericEntities',
                request_serializer=gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.SetGenericEntitiesRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.SetGenericEntitiesResponse.FromString,
                _registered_method=True)


class GenericServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetGenericEntities(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetGenericEntities(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GenericServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetGenericEntities': grpc.unary_unary_rpc_method_handler(
                    servicer.GetGenericEntities,
                    request_deserializer=gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.GetGenericEntitiesRequest.FromString,
                    response_serializer=gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.GetGenericEntitiesResponse.SerializeToString,
            ),
            'SetGenericEntities': grpc.unary_unary_rpc_method_handler(
                    servicer.SetGenericEntities,
                    request_deserializer=gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.SetGenericEntitiesRequest.FromString,
                    response_serializer=gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.SetGenericEntitiesResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'dataapi.entities.v1.GenericService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('dataapi.entities.v1.GenericService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class GenericService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetGenericEntities(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/dataapi.entities.v1.GenericService/GetGenericEntities',
            gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.GetGenericEntitiesRequest.SerializeToString,
            gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.GetGenericEntitiesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetGenericEntities(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/dataapi.entities.v1.GenericService/SetGenericEntities',
            gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.SetGenericEntitiesRequest.SerializeToString,
            gametime__protos_dot_dataapi_dot_entities_dot_v1_dot_service__pb2.SetGenericEntitiesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
