# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from gametime_protos.mle.fraud.v1 import service_pb2 as gametime__protos_dot_mle_dot_fraud_dot_v1_dot_service__pb2


class FraudServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ScoreOrder = channel.unary_unary(
                '/mle.fraud.v1.FraudService/ScoreOrder',
                request_serializer=gametime__protos_dot_mle_dot_fraud_dot_v1_dot_service__pb2.FraudServiceScoreOrderRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mle_dot_fraud_dot_v1_dot_service__pb2.FraudServiceScoreOrderResponse.FromString,
                _registered_method=True)


class FraudServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def ScoreOrder(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FraudServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ScoreOrder': grpc.unary_unary_rpc_method_handler(
                    servicer.ScoreOrder,
                    request_deserializer=gametime__protos_dot_mle_dot_fraud_dot_v1_dot_service__pb2.FraudServiceScoreOrderRequest.FromString,
                    response_serializer=gametime__protos_dot_mle_dot_fraud_dot_v1_dot_service__pb2.FraudServiceScoreOrderResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mle.fraud.v1.FraudService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mle.fraud.v1.FraudService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class FraudService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def ScoreOrder(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mle.fraud.v1.FraudService/ScoreOrder',
            gametime__protos_dot_mle_dot_fraud_dot_v1_dot_service__pb2.FraudServiceScoreOrderRequest.SerializeToString,
            gametime__protos_dot_mle_dot_fraud_dot_v1_dot_service__pb2.FraudServiceScoreOrderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
