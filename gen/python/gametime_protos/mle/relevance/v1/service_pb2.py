# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: gametime_protos/mle/relevance/v1/service.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'gametime_protos/mle/relevance/v1/service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.gametime_protos/mle/relevance/v1/service.proto\x12\x10mle.relevance.v1\"\xf1\x03\n\x07Listing\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\x12\x36\n\x17\x63ompetition_price_cents\x18\x02 \x01(\x05R\x15\x63ompetitionPriceCents\x12\x1d\n\ncost_cents\x18\x03 \x01(\x05R\tcostCents\x12$\n\x0eour_cost_cents\x18\x04 \x01(\x05R\x0courCostCents\x12,\n\x12prefee_price_cents\x18\x05 \x01(\x05R\x10prefeePriceCents\x12\x1f\n\x0bprice_cents\x18\x06 \x01(\x05R\npriceCents\x12\x32\n\x15\x64isplay_savings_cents\x18\x07 \x01(\x05R\x13\x64isplaySavingsCents\x12\x36\n\x17\x64isplay_savings_percent\x18\x08 \x01(\x05R\x15\x64isplaySavingsPercent\x12#\n\rsection_group\x18\t \x01(\tR\x0csectionGroup\x12\x18\n\x07section\x18\n \x01(\tR\x07section\x12\x10\n\x03row\x18\x0b \x01(\tR\x03row\x12,\n\x12seat_quality_score\x18\x0c \x01(\x02R\x10seatQualityScore\x12\x1f\n\x0bvalue_score\x18\r \x01(\x02R\nvalueScore\"}\n\x0cScoreRequest\x12\x36\n\x08platform\x18\x01 \x01(\x0e\x32\x1a.mle.relevance.v1.PlatformR\x08platform\x12\x35\n\x08listings\x18\x02 \x03(\x0b\x32\x19.mle.relevance.v1.ListingR\x08listings\"\'\n\rScoreResponse\x12\x16\n\x06scores\x18\x01 \x03(\x02R\x06scores*\x95\x01\n\x08Platform\x12\x18\n\x14PLATFORM_UNSPECIFIED\x10\x00\x12\x1b\n\x17PLATFORM_MOBILE_APP_IOS\x10\x01\x12\x1f\n\x1bPLATFORM_MOBILE_APP_ANDROID\x10\x02\x12\x17\n\x13PLATFORM_MOBILE_WEB\x10\x03\x12\x18\n\x14PLATFORM_DESKTOP_WEB\x10\x04*G\n\x0c\x44\x65liveryType\x12\x1d\n\x19\x44\x45LIVERY_TYPE_UNSPECIFIED\x10\x00\x12\x18\n\x14\x44\x45LIVERY_TYPE_MOBILE\x10\x01\x32^\n\x10RelevanceService\x12J\n\x05Score\x12\x1e.mle.relevance.v1.ScoreRequest\x1a\x1f.mle.relevance.v1.ScoreResponse\"\x00\x42\x9f\x01\n\x14\x63om.mle.relevance.v1B\x0cServiceProtoP\x01Z\x17mle/relevance/v1;protos\xa2\x02\x03MRX\xaa\x02\x10Mle.Relevance.V1\xca\x02\x10Mle\\Relevance\\V1\xe2\x02\x1cMle\\Relevance\\V1\\GPBMetadata\xea\x02\x12Mle::Relevance::V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gametime_protos.mle.relevance.v1.service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\024com.mle.relevance.v1B\014ServiceProtoP\001Z\027mle/relevance/v1;protos\242\002\003MRX\252\002\020Mle.Relevance.V1\312\002\020Mle\\Relevance\\V1\342\002\034Mle\\Relevance\\V1\\GPBMetadata\352\002\022Mle::Relevance::V1'
  _globals['_PLATFORM']._serialized_start=737
  _globals['_PLATFORM']._serialized_end=886
  _globals['_DELIVERYTYPE']._serialized_start=888
  _globals['_DELIVERYTYPE']._serialized_end=959
  _globals['_LISTING']._serialized_start=69
  _globals['_LISTING']._serialized_end=566
  _globals['_SCOREREQUEST']._serialized_start=568
  _globals['_SCOREREQUEST']._serialized_end=693
  _globals['_SCORERESPONSE']._serialized_start=695
  _globals['_SCORERESPONSE']._serialized_end=734
  _globals['_RELEVANCESERVICE']._serialized_start=961
  _globals['_RELEVANCESERVICE']._serialized_end=1055
# @@protoc_insertion_point(module_scope)
