from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Platform(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PLATFORM_UNSPECIFIED: _ClassVar[Platform]
    PLATFORM_MOBILE_APP_IOS: _ClassVar[Platform]
    PLATFORM_MOBILE_APP_ANDROID: _ClassVar[Platform]
    PLATFORM_MOBILE_WEB: _ClassVar[Platform]
    PLATFORM_DESKTOP_WEB: _ClassVar[Platform]

class DeliveryType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    DELIVERY_TYPE_UNSPECIFIED: _ClassVar[DeliveryType]
    DELIVERY_TYPE_MOBILE: _ClassVar[DeliveryType]
PLATFORM_UNSPECIFIED: Platform
PLATFORM_MOBILE_APP_IOS: Platform
PLATFORM_MOBILE_APP_ANDROID: Platform
PLATFORM_MOBILE_WEB: Platform
PLATFORM_DESKTOP_WEB: Platform
DELIVERY_TYPE_UNSPECIFIED: DeliveryType
DELIVERY_TYPE_MOBILE: DeliveryType

class Listing(_message.Message):
    __slots__ = ("id", "competition_price_cents", "cost_cents", "our_cost_cents", "prefee_price_cents", "price_cents", "display_savings_cents", "display_savings_percent", "section_group", "section", "row", "seat_quality_score", "value_score")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPETITION_PRICE_CENTS_FIELD_NUMBER: _ClassVar[int]
    COST_CENTS_FIELD_NUMBER: _ClassVar[int]
    OUR_COST_CENTS_FIELD_NUMBER: _ClassVar[int]
    PREFEE_PRICE_CENTS_FIELD_NUMBER: _ClassVar[int]
    PRICE_CENTS_FIELD_NUMBER: _ClassVar[int]
    DISPLAY_SAVINGS_CENTS_FIELD_NUMBER: _ClassVar[int]
    DISPLAY_SAVINGS_PERCENT_FIELD_NUMBER: _ClassVar[int]
    SECTION_GROUP_FIELD_NUMBER: _ClassVar[int]
    SECTION_FIELD_NUMBER: _ClassVar[int]
    ROW_FIELD_NUMBER: _ClassVar[int]
    SEAT_QUALITY_SCORE_FIELD_NUMBER: _ClassVar[int]
    VALUE_SCORE_FIELD_NUMBER: _ClassVar[int]
    id: str
    competition_price_cents: int
    cost_cents: int
    our_cost_cents: int
    prefee_price_cents: int
    price_cents: int
    display_savings_cents: int
    display_savings_percent: int
    section_group: str
    section: str
    row: str
    seat_quality_score: float
    value_score: float
    def __init__(self, id: _Optional[str] = ..., competition_price_cents: _Optional[int] = ..., cost_cents: _Optional[int] = ..., our_cost_cents: _Optional[int] = ..., prefee_price_cents: _Optional[int] = ..., price_cents: _Optional[int] = ..., display_savings_cents: _Optional[int] = ..., display_savings_percent: _Optional[int] = ..., section_group: _Optional[str] = ..., section: _Optional[str] = ..., row: _Optional[str] = ..., seat_quality_score: _Optional[float] = ..., value_score: _Optional[float] = ...) -> None: ...

class ScoreRequest(_message.Message):
    __slots__ = ("platform", "listings")
    PLATFORM_FIELD_NUMBER: _ClassVar[int]
    LISTINGS_FIELD_NUMBER: _ClassVar[int]
    platform: Platform
    listings: _containers.RepeatedCompositeFieldContainer[Listing]
    def __init__(self, platform: _Optional[_Union[Platform, str]] = ..., listings: _Optional[_Iterable[_Union[Listing, _Mapping]]] = ...) -> None: ...

class ScoreResponse(_message.Message):
    __slots__ = ("scores",)
    SCORES_FIELD_NUMBER: _ClassVar[int]
    scores: _containers.RepeatedScalarFieldContainer[float]
    def __init__(self, scores: _Optional[_Iterable[float]] = ...) -> None: ...
