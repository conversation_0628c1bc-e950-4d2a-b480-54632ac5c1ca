# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from gametime_protos.mle.relevance.v1 import service_pb2 as gametime__protos_dot_mle_dot_relevance_dot_v1_dot_service__pb2


class RelevanceServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Score = channel.unary_unary(
                '/mle.relevance.v1.RelevanceService/Score',
                request_serializer=gametime__protos_dot_mle_dot_relevance_dot_v1_dot_service__pb2.ScoreRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mle_dot_relevance_dot_v1_dot_service__pb2.ScoreResponse.FromString,
                _registered_method=True)


class RelevanceServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Score(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RelevanceServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Score': grpc.unary_unary_rpc_method_handler(
                    servicer.Score,
                    request_deserializer=gametime__protos_dot_mle_dot_relevance_dot_v1_dot_service__pb2.ScoreRequest.FromString,
                    response_serializer=gametime__protos_dot_mle_dot_relevance_dot_v1_dot_service__pb2.ScoreResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mle.relevance.v1.RelevanceService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mle.relevance.v1.RelevanceService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class RelevanceService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Score(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mle.relevance.v1.RelevanceService/Score',
            gametime__protos_dot_mle_dot_relevance_dot_v1_dot_service__pb2.ScoreRequest.SerializeToString,
            gametime__protos_dot_mle_dot_relevance_dot_v1_dot_service__pb2.ScoreResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
