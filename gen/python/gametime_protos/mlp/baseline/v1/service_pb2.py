# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: gametime_protos/mlp/baseline/v1/service.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'gametime_protos/mlp/baseline/v1/service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-gametime_protos/mlp/baseline/v1/service.proto\x12\x0fmlp.baseline.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"`\n\x1aJupyterServiceStartRequest\x12\x42\n\rinstance_type\x18\x01 \x01(\x0e\x32\x1d.mlp.baseline.v1.InstanceTypeR\x0cinstanceType\"\x1d\n\x1bJupyterServiceStartResponse\"\x1b\n\x19JupyterServiceStopRequest\"\x1c\n\x1aJupyterServiceStopResponse\"\x1f\n\x1dJupyterServiceDescribeRequest\"\x9c\x01\n\x1eJupyterServiceDescribeResponse\x12\x42\n\rinstance_type\x18\x01 \x01(\x0e\x32\x1d.mlp.baseline.v1.InstanceTypeR\x0cinstanceType\x12\x36\n\x06status\x18\x02 \x01(\x0e\x32\x1e.mlp.baseline.v1.JupyterStatusR\x06status\"\x1f\n\x1dJupyterServiceFetchUrlRequest\"2\n\x1eJupyterServiceFetchUrlResponse\x12\x10\n\x03url\x18\x01 \x01(\tR\x03url\"*\n(JupyterServiceFetchConnectionInfoRequest\"\xda\x01\n)JupyterServiceFetchConnectionInfoResponse\x12\x1f\n\x0binstance_id\x18\x01 \x01(\tR\ninstanceId\x12\x1b\n\tdomain_id\x18\x02 \x01(\tR\x08\x64omainId\x12\x1d\n\nspace_name\x18\x03 \x01(\tR\tspaceName\x12\x19\n\x08\x61pp_name\x18\x04 \x01(\tR\x07\x61ppName\x12\x12\n\x04host\x18\x05 \x01(\tR\x04host\x12!\n\x0cjupyter_port\x18\x06 \x01(\x05R\x0bjupyterPort\"\xa3\x02\n\x15JobsServiceRunRequest\x12\x16\n\x06\x66\x61mily\x18\x01 \x01(\tR\x06\x66\x61mily\x12\x42\n\rinstance_type\x18\x02 \x01(\x0e\x32\x1d.mlp.baseline.v1.InstanceTypeR\x0cinstanceType\x12V\n\nparameters\x18\x03 \x03(\x0b\x32\x36.mlp.baseline.v1.JobsServiceRunRequest.ParametersEntryR\nparameters\x12\x17\n\x07git_ref\x18\x04 \x01(\tR\x06gitRef\x1a=\n\x0fParametersEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"*\n\x16JobsServiceRunResponse\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\",\n\x18JobsServiceCancelRequest\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\"\x1b\n\x19JobsServiceCancelResponse\".\n\x1aJobsServiceDescribeRequest\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\"\xea\x05\n\x1bJobsServiceDescribeResponse\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\x12\x16\n\x06\x66\x61mily\x18\x02 \x01(\tR\x06\x66\x61mily\x12\x42\n\rinstance_type\x18\x03 \x01(\x0e\x32\x1d.mlp.baseline.v1.InstanceTypeR\x0cinstanceType\x12\\\n\nparameters\x18\x04 \x03(\x0b\x32<.mlp.baseline.v1.JobsServiceDescribeResponse.ParametersEntryR\nparameters\x12\x17\n\x07git_sha\x18\x05 \x01(\tR\x06gitSha\x12=\n\x0crequested_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0brequestedAt\x12\x39\n\nstarted_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tstartedAt\x12\x35\n\x08\x65nded_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x07\x65ndedAt\x12\x32\n\x06status\x18\t \x01(\x0e\x32\x1a.mlp.baseline.v1.JobStatusR\x06status\x12#\n\rstatus_reason\x18\n \x01(\tR\x0cstatusReason\x12\x15\n\x06run_by\x18\x0b \x01(\tR\x05runBy\x12>\n\x0e\x61sset_manifest\x18\x0c \x01(\x0b\x32\x17.google.protobuf.StructR\rassetManifest\x12\x46\n\x12\x65valuation_metrics\x18\r \x01(\x0b\x32\x17.google.protobuf.StructR\x11\x65valuationMetrics\x1a=\n\x0fParametersEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"\xb3\x02\n\x0bJobListItem\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\x12\x16\n\x06\x66\x61mily\x18\x02 \x01(\tR\x06\x66\x61mily\x12=\n\x0crequested_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0brequestedAt\x12\x39\n\nstarted_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tstartedAt\x12\x35\n\x08\x65nded_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x07\x65ndedAt\x12\x32\n\x06status\x18\x06 \x01(\x0e\x32\x1a.mlp.baseline.v1.JobStatusR\x06status\x12\x15\n\x06run_by\x18\x07 \x01(\tR\x05runBy\"\xb4\x01\n\x16JobsServiceListRequest\x12\x16\n\x06\x66\x61mily\x18\x01 \x01(\tR\x06\x66\x61mily\x12\x30\n\x05\x61\x66ter\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x05\x61\x66ter\x12\x32\n\x06\x62\x65\x66ore\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x06\x62\x65\x66ore\x12\x1c\n\tascending\x18\x04 \x01(\x08R\tascending\"K\n\x17JobsServiceListResponse\x12\x30\n\x04jobs\x18\x01 \x03(\x0b\x32\x1c.mlp.baseline.v1.JobListItemR\x04jobs\"0\n\x1cJobsServiceInitializeRequest\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\"\x1f\n\x1dJobsServiceInitializeResponse\"\x8f\x02\n\x1aJobsServiceFinalizeRequest\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\x12\x32\n\x06status\x18\x02 \x01(\x0e\x32\x1a.mlp.baseline.v1.JobStatusR\x06status\x12#\n\rstatus_reason\x18\x03 \x01(\tR\x0cstatusReason\x12>\n\x0e\x61sset_manifest\x18\x04 \x01(\x0b\x32\x17.google.protobuf.StructR\rassetManifest\x12\x46\n\x12\x65valuation_metrics\x18\x05 \x01(\x0b\x32\x17.google.protobuf.StructR\x11\x65valuationMetrics\"\x1d\n\x1bJobsServiceFinalizeResponse\"4\n JobsServiceFetchOutputUrlRequest\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\"5\n!JobsServiceFetchOutputUrlResponse\x12\x10\n\x03url\x18\x01 \x01(\tR\x03url\"C\n\x1bJobsServiceFetchLogsRequest\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\x12\x12\n\x04tail\x18\x02 \x01(\x05R\x04tail\"8\n\x1cJobsServiceFetchLogsResponse\x12\x18\n\x07\x63ontent\x18\x01 \x01(\tR\x07\x63ontent\"f\n\x18SecretsServiceAddRequest\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x02 \x01(\tR\x0b\x64\x65scription\x12\x14\n\x05value\x18\x03 \x01(\tR\x05value\"\x1b\n\x19SecretsServiceAddResponse\"i\n\x1bSecretsServiceUpdateRequest\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x02 \x01(\tR\x0b\x64\x65scription\x12\x14\n\x05value\x18\x03 \x01(\tR\x05value\"\x1e\n\x1cSecretsServiceUpdateResponse\"1\n\x1bSecretsServiceRemoveRequest\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\"\x1e\n\x1cSecretsServiceRemoveResponse\"\xbc\x01\n\x0eSecretListItem\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x02 \x01(\tR\x0b\x64\x65scription\x12\x39\n\ncreated_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x39\n\nupdated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tupdatedAt\"\x1b\n\x19SecretsServiceListRequest\"W\n\x1aSecretsServiceListResponse\x12\x39\n\x07secrets\x18\x01 \x03(\x0b\x32\x1f.mlp.baseline.v1.SecretListItemR\x07secrets*\xc7\x01\n\x0cInstanceType\x12\x1d\n\x19INSTANCE_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n\x16INSTANCE_TYPE_M5_LARGE\x10\x01\x12\x1c\n\x18INSTANCE_TYPE_M5_4XLARGE\x10\x02\x12\x1d\n\x19INSTANCE_TYPE_M5_24XLARGE\x10\x03\x12\x1e\n\x1aINSTANCE_TYPE_G4DN_4XLARGE\x10\x04\x12\x1f\n\x1bINSTANCE_TYPE_G4DN_12XLARGE\x10\x05*\xbb\x01\n\rJupyterStatus\x12\x1e\n\x1aJUPYTER_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n\x16JUPYTER_STATUS_PENDING\x10\x01\x12\x1a\n\x16JUPYTER_STATUS_RUNNING\x10\x02\x12\x1b\n\x17JUPYTER_STATUS_STOPPING\x10\x03\x12\x1a\n\x16JUPYTER_STATUS_STOPPED\x10\x04\x12\x19\n\x15JUPYTER_STATUS_FAILED\x10\x05*\xa1\x01\n\tJobStatus\x12\x1a\n\x16JOB_STATUS_UNSPECIFIED\x10\x00\x12\x16\n\x12JOB_STATUS_PENDING\x10\x01\x12\x16\n\x12JOB_STATUS_RUNNING\x10\x02\x12\x18\n\x14JOB_STATUS_SUCCEEDED\x10\x03\x12\x15\n\x11JOB_STATUS_FAILED\x10\x04\x12\x17\n\x13JOB_STATUS_CANCELED\x10\x05\x32\xc8\x04\n\x0eJupyterService\x12\x64\n\x05Start\x12+.mlp.baseline.v1.JupyterServiceStartRequest\x1a,.mlp.baseline.v1.JupyterServiceStartResponse\"\x00\x12\x61\n\x04Stop\x12*.mlp.baseline.v1.JupyterServiceStopRequest\x1a+.mlp.baseline.v1.JupyterServiceStopResponse\"\x00\x12m\n\x08\x44\x65scribe\x12..mlp.baseline.v1.JupyterServiceDescribeRequest\x1a/.mlp.baseline.v1.JupyterServiceDescribeResponse\"\x00\x12m\n\x08\x46\x65tchUrl\x12..mlp.baseline.v1.JupyterServiceFetchUrlRequest\x1a/.mlp.baseline.v1.JupyterServiceFetchUrlResponse\"\x00\x12\x8e\x01\n\x13\x46\x65tchConnectionInfo\x12\x39.mlp.baseline.v1.JupyterServiceFetchConnectionInfoRequest\x1a:.mlp.baseline.v1.JupyterServiceFetchConnectionInfoResponse\"\x00\x32\xcf\x06\n\x0bJobsService\x12X\n\x03Run\x12&.mlp.baseline.v1.JobsServiceRunRequest\x1a\'.mlp.baseline.v1.JobsServiceRunResponse\"\x00\x12\x61\n\x06\x43\x61ncel\x12).mlp.baseline.v1.JobsServiceCancelRequest\x1a*.mlp.baseline.v1.JobsServiceCancelResponse\"\x00\x12g\n\x08\x44\x65scribe\x12+.mlp.baseline.v1.JobsServiceDescribeRequest\x1a,.mlp.baseline.v1.JobsServiceDescribeResponse\"\x00\x12[\n\x04List\x12\'.mlp.baseline.v1.JobsServiceListRequest\x1a(.mlp.baseline.v1.JobsServiceListResponse\"\x00\x12m\n\nInitialize\x12-.mlp.baseline.v1.JobsServiceInitializeRequest\x1a..mlp.baseline.v1.JobsServiceInitializeResponse\"\x00\x12g\n\x08\x46inalize\x12+.mlp.baseline.v1.JobsServiceFinalizeRequest\x1a,.mlp.baseline.v1.JobsServiceFinalizeResponse\"\x00\x12y\n\x0e\x46\x65tchOutputUrl\x12\x31.mlp.baseline.v1.JobsServiceFetchOutputUrlRequest\x1a\x32.mlp.baseline.v1.JobsServiceFetchOutputUrlResponse\"\x00\x12j\n\tFetchLogs\x12,.mlp.baseline.v1.JobsServiceFetchLogsRequest\x1a-.mlp.baseline.v1.JobsServiceFetchLogsResponse\"\x00\x32\xa5\x03\n\x0eSecretsService\x12^\n\x03\x41\x64\x64\x12).mlp.baseline.v1.SecretsServiceAddRequest\x1a*.mlp.baseline.v1.SecretsServiceAddResponse\"\x00\x12g\n\x06Update\x12,.mlp.baseline.v1.SecretsServiceUpdateRequest\x1a-.mlp.baseline.v1.SecretsServiceUpdateResponse\"\x00\x12g\n\x06Remove\x12,.mlp.baseline.v1.SecretsServiceRemoveRequest\x1a-.mlp.baseline.v1.SecretsServiceRemoveResponse\"\x00\x12\x61\n\x04List\x12*.mlp.baseline.v1.SecretsServiceListRequest\x1a+.mlp.baseline.v1.SecretsServiceListResponse\"\x00\x42\x99\x01\n\x13\x63om.mlp.baseline.v1B\x0cServiceProtoP\x01Z\x16mlp/baseline/v1;protos\xa2\x02\x03MBX\xaa\x02\x0fMlp.Baseline.V1\xca\x02\x0fMlp\\Baseline\\V1\xe2\x02\x1bMlp\\Baseline\\V1\\GPBMetadata\xea\x02\x11Mlp::Baseline::V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gametime_protos.mlp.baseline.v1.service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\023com.mlp.baseline.v1B\014ServiceProtoP\001Z\026mlp/baseline/v1;protos\242\002\003MBX\252\002\017Mlp.Baseline.V1\312\002\017Mlp\\Baseline\\V1\342\002\033Mlp\\Baseline\\V1\\GPBMetadata\352\002\021Mlp::Baseline::V1'
  _globals['_JOBSSERVICERUNREQUEST_PARAMETERSENTRY']._loaded_options = None
  _globals['_JOBSSERVICERUNREQUEST_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_JOBSSERVICEDESCRIBERESPONSE_PARAMETERSENTRY']._loaded_options = None
  _globals['_JOBSSERVICEDESCRIBERESPONSE_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_INSTANCETYPE']._serialized_start=3928
  _globals['_INSTANCETYPE']._serialized_end=4127
  _globals['_JUPYTERSTATUS']._serialized_start=4130
  _globals['_JUPYTERSTATUS']._serialized_end=4317
  _globals['_JOBSTATUS']._serialized_start=4320
  _globals['_JOBSTATUS']._serialized_end=4481
  _globals['_JUPYTERSERVICESTARTREQUEST']._serialized_start=129
  _globals['_JUPYTERSERVICESTARTREQUEST']._serialized_end=225
  _globals['_JUPYTERSERVICESTARTRESPONSE']._serialized_start=227
  _globals['_JUPYTERSERVICESTARTRESPONSE']._serialized_end=256
  _globals['_JUPYTERSERVICESTOPREQUEST']._serialized_start=258
  _globals['_JUPYTERSERVICESTOPREQUEST']._serialized_end=285
  _globals['_JUPYTERSERVICESTOPRESPONSE']._serialized_start=287
  _globals['_JUPYTERSERVICESTOPRESPONSE']._serialized_end=315
  _globals['_JUPYTERSERVICEDESCRIBEREQUEST']._serialized_start=317
  _globals['_JUPYTERSERVICEDESCRIBEREQUEST']._serialized_end=348
  _globals['_JUPYTERSERVICEDESCRIBERESPONSE']._serialized_start=351
  _globals['_JUPYTERSERVICEDESCRIBERESPONSE']._serialized_end=507
  _globals['_JUPYTERSERVICEFETCHURLREQUEST']._serialized_start=509
  _globals['_JUPYTERSERVICEFETCHURLREQUEST']._serialized_end=540
  _globals['_JUPYTERSERVICEFETCHURLRESPONSE']._serialized_start=542
  _globals['_JUPYTERSERVICEFETCHURLRESPONSE']._serialized_end=592
  _globals['_JUPYTERSERVICEFETCHCONNECTIONINFOREQUEST']._serialized_start=594
  _globals['_JUPYTERSERVICEFETCHCONNECTIONINFOREQUEST']._serialized_end=636
  _globals['_JUPYTERSERVICEFETCHCONNECTIONINFORESPONSE']._serialized_start=639
  _globals['_JUPYTERSERVICEFETCHCONNECTIONINFORESPONSE']._serialized_end=857
  _globals['_JOBSSERVICERUNREQUEST']._serialized_start=860
  _globals['_JOBSSERVICERUNREQUEST']._serialized_end=1151
  _globals['_JOBSSERVICERUNREQUEST_PARAMETERSENTRY']._serialized_start=1090
  _globals['_JOBSSERVICERUNREQUEST_PARAMETERSENTRY']._serialized_end=1151
  _globals['_JOBSSERVICERUNRESPONSE']._serialized_start=1153
  _globals['_JOBSSERVICERUNRESPONSE']._serialized_end=1195
  _globals['_JOBSSERVICECANCELREQUEST']._serialized_start=1197
  _globals['_JOBSSERVICECANCELREQUEST']._serialized_end=1241
  _globals['_JOBSSERVICECANCELRESPONSE']._serialized_start=1243
  _globals['_JOBSSERVICECANCELRESPONSE']._serialized_end=1270
  _globals['_JOBSSERVICEDESCRIBEREQUEST']._serialized_start=1272
  _globals['_JOBSSERVICEDESCRIBEREQUEST']._serialized_end=1318
  _globals['_JOBSSERVICEDESCRIBERESPONSE']._serialized_start=1321
  _globals['_JOBSSERVICEDESCRIBERESPONSE']._serialized_end=2067
  _globals['_JOBSSERVICEDESCRIBERESPONSE_PARAMETERSENTRY']._serialized_start=1090
  _globals['_JOBSSERVICEDESCRIBERESPONSE_PARAMETERSENTRY']._serialized_end=1151
  _globals['_JOBLISTITEM']._serialized_start=2070
  _globals['_JOBLISTITEM']._serialized_end=2377
  _globals['_JOBSSERVICELISTREQUEST']._serialized_start=2380
  _globals['_JOBSSERVICELISTREQUEST']._serialized_end=2560
  _globals['_JOBSSERVICELISTRESPONSE']._serialized_start=2562
  _globals['_JOBSSERVICELISTRESPONSE']._serialized_end=2637
  _globals['_JOBSSERVICEINITIALIZEREQUEST']._serialized_start=2639
  _globals['_JOBSSERVICEINITIALIZEREQUEST']._serialized_end=2687
  _globals['_JOBSSERVICEINITIALIZERESPONSE']._serialized_start=2689
  _globals['_JOBSSERVICEINITIALIZERESPONSE']._serialized_end=2720
  _globals['_JOBSSERVICEFINALIZEREQUEST']._serialized_start=2723
  _globals['_JOBSSERVICEFINALIZEREQUEST']._serialized_end=2994
  _globals['_JOBSSERVICEFINALIZERESPONSE']._serialized_start=2996
  _globals['_JOBSSERVICEFINALIZERESPONSE']._serialized_end=3025
  _globals['_JOBSSERVICEFETCHOUTPUTURLREQUEST']._serialized_start=3027
  _globals['_JOBSSERVICEFETCHOUTPUTURLREQUEST']._serialized_end=3079
  _globals['_JOBSSERVICEFETCHOUTPUTURLRESPONSE']._serialized_start=3081
  _globals['_JOBSSERVICEFETCHOUTPUTURLRESPONSE']._serialized_end=3134
  _globals['_JOBSSERVICEFETCHLOGSREQUEST']._serialized_start=3136
  _globals['_JOBSSERVICEFETCHLOGSREQUEST']._serialized_end=3203
  _globals['_JOBSSERVICEFETCHLOGSRESPONSE']._serialized_start=3205
  _globals['_JOBSSERVICEFETCHLOGSRESPONSE']._serialized_end=3261
  _globals['_SECRETSSERVICEADDREQUEST']._serialized_start=3263
  _globals['_SECRETSSERVICEADDREQUEST']._serialized_end=3365
  _globals['_SECRETSSERVICEADDRESPONSE']._serialized_start=3367
  _globals['_SECRETSSERVICEADDRESPONSE']._serialized_end=3394
  _globals['_SECRETSSERVICEUPDATEREQUEST']._serialized_start=3396
  _globals['_SECRETSSERVICEUPDATEREQUEST']._serialized_end=3501
  _globals['_SECRETSSERVICEUPDATERESPONSE']._serialized_start=3503
  _globals['_SECRETSSERVICEUPDATERESPONSE']._serialized_end=3533
  _globals['_SECRETSSERVICEREMOVEREQUEST']._serialized_start=3535
  _globals['_SECRETSSERVICEREMOVEREQUEST']._serialized_end=3584
  _globals['_SECRETSSERVICEREMOVERESPONSE']._serialized_start=3586
  _globals['_SECRETSSERVICEREMOVERESPONSE']._serialized_end=3616
  _globals['_SECRETLISTITEM']._serialized_start=3619
  _globals['_SECRETLISTITEM']._serialized_end=3807
  _globals['_SECRETSSERVICELISTREQUEST']._serialized_start=3809
  _globals['_SECRETSSERVICELISTREQUEST']._serialized_end=3836
  _globals['_SECRETSSERVICELISTRESPONSE']._serialized_start=3838
  _globals['_SECRETSSERVICELISTRESPONSE']._serialized_end=3925
  _globals['_JUPYTERSERVICE']._serialized_start=4484
  _globals['_JUPYTERSERVICE']._serialized_end=5068
  _globals['_JOBSSERVICE']._serialized_start=5071
  _globals['_JOBSSERVICE']._serialized_end=5918
  _globals['_SECRETSSERVICE']._serialized_start=5921
  _globals['_SECRETSSERVICE']._serialized_end=6342
# @@protoc_insertion_point(module_scope)
