from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf import struct_pb2 as _struct_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class InstanceType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    INSTANCE_TYPE_UNSPECIFIED: _ClassVar[InstanceType]
    INSTANCE_TYPE_M5_LARGE: _ClassVar[InstanceType]
    INSTANCE_TYPE_M5_4XLARGE: _ClassVar[InstanceType]
    INSTANCE_TYPE_M5_24XLARGE: _ClassVar[InstanceType]
    INSTANCE_TYPE_G4DN_4XLARGE: _ClassVar[InstanceType]
    INSTANCE_TYPE_G4DN_12XLARGE: _ClassVar[InstanceType]

class JupyterStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    JUPYTER_STATUS_UNSPECIFIED: _ClassVar[JupyterStatus]
    JUPYTER_STATUS_PENDING: _ClassVar[JupyterStatus]
    JUPYTER_STATUS_RUNNING: _ClassVar[JupyterStatus]
    JUPYTER_STATUS_STOPPING: _ClassVar[JupyterStatus]
    JUPYTER_STATUS_STOPPED: _ClassVar[JupyterStatus]
    JUPYTER_STATUS_FAILED: _ClassVar[JupyterStatus]

class JobStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    JOB_STATUS_UNSPECIFIED: _ClassVar[JobStatus]
    JOB_STATUS_PENDING: _ClassVar[JobStatus]
    JOB_STATUS_RUNNING: _ClassVar[JobStatus]
    JOB_STATUS_SUCCEEDED: _ClassVar[JobStatus]
    JOB_STATUS_FAILED: _ClassVar[JobStatus]
    JOB_STATUS_CANCELED: _ClassVar[JobStatus]
INSTANCE_TYPE_UNSPECIFIED: InstanceType
INSTANCE_TYPE_M5_LARGE: InstanceType
INSTANCE_TYPE_M5_4XLARGE: InstanceType
INSTANCE_TYPE_M5_24XLARGE: InstanceType
INSTANCE_TYPE_G4DN_4XLARGE: InstanceType
INSTANCE_TYPE_G4DN_12XLARGE: InstanceType
JUPYTER_STATUS_UNSPECIFIED: JupyterStatus
JUPYTER_STATUS_PENDING: JupyterStatus
JUPYTER_STATUS_RUNNING: JupyterStatus
JUPYTER_STATUS_STOPPING: JupyterStatus
JUPYTER_STATUS_STOPPED: JupyterStatus
JUPYTER_STATUS_FAILED: JupyterStatus
JOB_STATUS_UNSPECIFIED: JobStatus
JOB_STATUS_PENDING: JobStatus
JOB_STATUS_RUNNING: JobStatus
JOB_STATUS_SUCCEEDED: JobStatus
JOB_STATUS_FAILED: JobStatus
JOB_STATUS_CANCELED: JobStatus

class JupyterServiceStartRequest(_message.Message):
    __slots__ = ("instance_type",)
    INSTANCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    instance_type: InstanceType
    def __init__(self, instance_type: _Optional[_Union[InstanceType, str]] = ...) -> None: ...

class JupyterServiceStartResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JupyterServiceStopRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JupyterServiceStopResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JupyterServiceDescribeRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JupyterServiceDescribeResponse(_message.Message):
    __slots__ = ("instance_type", "status")
    INSTANCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    instance_type: InstanceType
    status: JupyterStatus
    def __init__(self, instance_type: _Optional[_Union[InstanceType, str]] = ..., status: _Optional[_Union[JupyterStatus, str]] = ...) -> None: ...

class JupyterServiceFetchUrlRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JupyterServiceFetchUrlResponse(_message.Message):
    __slots__ = ("url",)
    URL_FIELD_NUMBER: _ClassVar[int]
    url: str
    def __init__(self, url: _Optional[str] = ...) -> None: ...

class JupyterServiceFetchConnectionInfoRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JupyterServiceFetchConnectionInfoResponse(_message.Message):
    __slots__ = ("instance_id", "domain_id", "space_name", "app_name", "host", "jupyter_port")
    INSTANCE_ID_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_ID_FIELD_NUMBER: _ClassVar[int]
    SPACE_NAME_FIELD_NUMBER: _ClassVar[int]
    APP_NAME_FIELD_NUMBER: _ClassVar[int]
    HOST_FIELD_NUMBER: _ClassVar[int]
    JUPYTER_PORT_FIELD_NUMBER: _ClassVar[int]
    instance_id: str
    domain_id: str
    space_name: str
    app_name: str
    host: str
    jupyter_port: int
    def __init__(self, instance_id: _Optional[str] = ..., domain_id: _Optional[str] = ..., space_name: _Optional[str] = ..., app_name: _Optional[str] = ..., host: _Optional[str] = ..., jupyter_port: _Optional[int] = ...) -> None: ...

class JobsServiceRunRequest(_message.Message):
    __slots__ = ("family", "instance_type", "parameters", "git_ref")
    class ParametersEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    FAMILY_FIELD_NUMBER: _ClassVar[int]
    INSTANCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    PARAMETERS_FIELD_NUMBER: _ClassVar[int]
    GIT_REF_FIELD_NUMBER: _ClassVar[int]
    family: str
    instance_type: InstanceType
    parameters: _containers.ScalarMap[str, str]
    git_ref: str
    def __init__(self, family: _Optional[str] = ..., instance_type: _Optional[_Union[InstanceType, str]] = ..., parameters: _Optional[_Mapping[str, str]] = ..., git_ref: _Optional[str] = ...) -> None: ...

class JobsServiceRunResponse(_message.Message):
    __slots__ = ("ref",)
    REF_FIELD_NUMBER: _ClassVar[int]
    ref: str
    def __init__(self, ref: _Optional[str] = ...) -> None: ...

class JobsServiceCancelRequest(_message.Message):
    __slots__ = ("ref",)
    REF_FIELD_NUMBER: _ClassVar[int]
    ref: str
    def __init__(self, ref: _Optional[str] = ...) -> None: ...

class JobsServiceCancelResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JobsServiceDescribeRequest(_message.Message):
    __slots__ = ("ref",)
    REF_FIELD_NUMBER: _ClassVar[int]
    ref: str
    def __init__(self, ref: _Optional[str] = ...) -> None: ...

class JobsServiceDescribeResponse(_message.Message):
    __slots__ = ("ref", "family", "instance_type", "parameters", "git_sha", "requested_at", "started_at", "ended_at", "status", "status_reason", "run_by", "asset_manifest", "evaluation_metrics")
    class ParametersEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    REF_FIELD_NUMBER: _ClassVar[int]
    FAMILY_FIELD_NUMBER: _ClassVar[int]
    INSTANCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    PARAMETERS_FIELD_NUMBER: _ClassVar[int]
    GIT_SHA_FIELD_NUMBER: _ClassVar[int]
    REQUESTED_AT_FIELD_NUMBER: _ClassVar[int]
    STARTED_AT_FIELD_NUMBER: _ClassVar[int]
    ENDED_AT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_REASON_FIELD_NUMBER: _ClassVar[int]
    RUN_BY_FIELD_NUMBER: _ClassVar[int]
    ASSET_MANIFEST_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_METRICS_FIELD_NUMBER: _ClassVar[int]
    ref: str
    family: str
    instance_type: InstanceType
    parameters: _containers.ScalarMap[str, str]
    git_sha: str
    requested_at: _timestamp_pb2.Timestamp
    started_at: _timestamp_pb2.Timestamp
    ended_at: _timestamp_pb2.Timestamp
    status: JobStatus
    status_reason: str
    run_by: str
    asset_manifest: _struct_pb2.Struct
    evaluation_metrics: _struct_pb2.Struct
    def __init__(self, ref: _Optional[str] = ..., family: _Optional[str] = ..., instance_type: _Optional[_Union[InstanceType, str]] = ..., parameters: _Optional[_Mapping[str, str]] = ..., git_sha: _Optional[str] = ..., requested_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., started_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., ended_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., status: _Optional[_Union[JobStatus, str]] = ..., status_reason: _Optional[str] = ..., run_by: _Optional[str] = ..., asset_manifest: _Optional[_Union[_struct_pb2.Struct, _Mapping]] = ..., evaluation_metrics: _Optional[_Union[_struct_pb2.Struct, _Mapping]] = ...) -> None: ...

class JobListItem(_message.Message):
    __slots__ = ("ref", "family", "requested_at", "started_at", "ended_at", "status", "run_by")
    REF_FIELD_NUMBER: _ClassVar[int]
    FAMILY_FIELD_NUMBER: _ClassVar[int]
    REQUESTED_AT_FIELD_NUMBER: _ClassVar[int]
    STARTED_AT_FIELD_NUMBER: _ClassVar[int]
    ENDED_AT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    RUN_BY_FIELD_NUMBER: _ClassVar[int]
    ref: str
    family: str
    requested_at: _timestamp_pb2.Timestamp
    started_at: _timestamp_pb2.Timestamp
    ended_at: _timestamp_pb2.Timestamp
    status: JobStatus
    run_by: str
    def __init__(self, ref: _Optional[str] = ..., family: _Optional[str] = ..., requested_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., started_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., ended_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., status: _Optional[_Union[JobStatus, str]] = ..., run_by: _Optional[str] = ...) -> None: ...

class JobsServiceListRequest(_message.Message):
    __slots__ = ("family", "after", "before", "ascending")
    FAMILY_FIELD_NUMBER: _ClassVar[int]
    AFTER_FIELD_NUMBER: _ClassVar[int]
    BEFORE_FIELD_NUMBER: _ClassVar[int]
    ASCENDING_FIELD_NUMBER: _ClassVar[int]
    family: str
    after: _timestamp_pb2.Timestamp
    before: _timestamp_pb2.Timestamp
    ascending: bool
    def __init__(self, family: _Optional[str] = ..., after: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., before: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., ascending: bool = ...) -> None: ...

class JobsServiceListResponse(_message.Message):
    __slots__ = ("jobs",)
    JOBS_FIELD_NUMBER: _ClassVar[int]
    jobs: _containers.RepeatedCompositeFieldContainer[JobListItem]
    def __init__(self, jobs: _Optional[_Iterable[_Union[JobListItem, _Mapping]]] = ...) -> None: ...

class JobsServiceInitializeRequest(_message.Message):
    __slots__ = ("ref",)
    REF_FIELD_NUMBER: _ClassVar[int]
    ref: str
    def __init__(self, ref: _Optional[str] = ...) -> None: ...

class JobsServiceInitializeResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JobsServiceFinalizeRequest(_message.Message):
    __slots__ = ("ref", "status", "status_reason", "asset_manifest", "evaluation_metrics")
    REF_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_REASON_FIELD_NUMBER: _ClassVar[int]
    ASSET_MANIFEST_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_METRICS_FIELD_NUMBER: _ClassVar[int]
    ref: str
    status: JobStatus
    status_reason: str
    asset_manifest: _struct_pb2.Struct
    evaluation_metrics: _struct_pb2.Struct
    def __init__(self, ref: _Optional[str] = ..., status: _Optional[_Union[JobStatus, str]] = ..., status_reason: _Optional[str] = ..., asset_manifest: _Optional[_Union[_struct_pb2.Struct, _Mapping]] = ..., evaluation_metrics: _Optional[_Union[_struct_pb2.Struct, _Mapping]] = ...) -> None: ...

class JobsServiceFinalizeResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class JobsServiceFetchOutputUrlRequest(_message.Message):
    __slots__ = ("ref",)
    REF_FIELD_NUMBER: _ClassVar[int]
    ref: str
    def __init__(self, ref: _Optional[str] = ...) -> None: ...

class JobsServiceFetchOutputUrlResponse(_message.Message):
    __slots__ = ("url",)
    URL_FIELD_NUMBER: _ClassVar[int]
    url: str
    def __init__(self, url: _Optional[str] = ...) -> None: ...

class JobsServiceFetchLogsRequest(_message.Message):
    __slots__ = ("ref", "tail")
    REF_FIELD_NUMBER: _ClassVar[int]
    TAIL_FIELD_NUMBER: _ClassVar[int]
    ref: str
    tail: int
    def __init__(self, ref: _Optional[str] = ..., tail: _Optional[int] = ...) -> None: ...

class JobsServiceFetchLogsResponse(_message.Message):
    __slots__ = ("content",)
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    content: str
    def __init__(self, content: _Optional[str] = ...) -> None: ...

class SecretsServiceAddRequest(_message.Message):
    __slots__ = ("name", "description", "value")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    name: str
    description: str
    value: str
    def __init__(self, name: _Optional[str] = ..., description: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...

class SecretsServiceAddResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class SecretsServiceUpdateRequest(_message.Message):
    __slots__ = ("name", "description", "value")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    name: str
    description: str
    value: str
    def __init__(self, name: _Optional[str] = ..., description: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...

class SecretsServiceUpdateResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class SecretsServiceRemoveRequest(_message.Message):
    __slots__ = ("name",)
    NAME_FIELD_NUMBER: _ClassVar[int]
    name: str
    def __init__(self, name: _Optional[str] = ...) -> None: ...

class SecretsServiceRemoveResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class SecretListItem(_message.Message):
    __slots__ = ("name", "description", "created_at", "updated_at")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    name: str
    description: str
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    def __init__(self, name: _Optional[str] = ..., description: _Optional[str] = ..., created_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class SecretsServiceListRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class SecretsServiceListResponse(_message.Message):
    __slots__ = ("secrets",)
    SECRETS_FIELD_NUMBER: _ClassVar[int]
    secrets: _containers.RepeatedCompositeFieldContainer[SecretListItem]
    def __init__(self, secrets: _Optional[_Iterable[_Union[SecretListItem, _Mapping]]] = ...) -> None: ...
