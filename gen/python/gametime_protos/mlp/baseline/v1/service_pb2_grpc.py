# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from gametime_protos.mlp.baseline.v1 import service_pb2 as gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2


class JupyterServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Start = channel.unary_unary(
                '/mlp.baseline.v1.JupyterService/Start',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStartRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStartResponse.FromString,
                _registered_method=True)
        self.Stop = channel.unary_unary(
                '/mlp.baseline.v1.JupyterService/Stop',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStopRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStopResponse.FromString,
                _registered_method=True)
        self.Describe = channel.unary_unary(
                '/mlp.baseline.v1.JupyterService/Describe',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceDescribeRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceDescribeResponse.FromString,
                _registered_method=True)
        self.FetchUrl = channel.unary_unary(
                '/mlp.baseline.v1.JupyterService/FetchUrl',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchUrlRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchUrlResponse.FromString,
                _registered_method=True)
        self.FetchConnectionInfo = channel.unary_unary(
                '/mlp.baseline.v1.JupyterService/FetchConnectionInfo',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchConnectionInfoRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchConnectionInfoResponse.FromString,
                _registered_method=True)


class JupyterServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Start(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Stop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Describe(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchUrl(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchConnectionInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_JupyterServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Start': grpc.unary_unary_rpc_method_handler(
                    servicer.Start,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStartRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStartResponse.SerializeToString,
            ),
            'Stop': grpc.unary_unary_rpc_method_handler(
                    servicer.Stop,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStopRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStopResponse.SerializeToString,
            ),
            'Describe': grpc.unary_unary_rpc_method_handler(
                    servicer.Describe,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceDescribeRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceDescribeResponse.SerializeToString,
            ),
            'FetchUrl': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchUrl,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchUrlRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchUrlResponse.SerializeToString,
            ),
            'FetchConnectionInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchConnectionInfo,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchConnectionInfoRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchConnectionInfoResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mlp.baseline.v1.JupyterService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mlp.baseline.v1.JupyterService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class JupyterService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Start(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JupyterService/Start',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStartRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStartResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Stop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JupyterService/Stop',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStopRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceStopResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Describe(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JupyterService/Describe',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceDescribeRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceDescribeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchUrl(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JupyterService/FetchUrl',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchUrlRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchUrlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchConnectionInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JupyterService/FetchConnectionInfo',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchConnectionInfoRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JupyterServiceFetchConnectionInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class JobsServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Run = channel.unary_unary(
                '/mlp.baseline.v1.JobsService/Run',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceRunRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceRunResponse.FromString,
                _registered_method=True)
        self.Cancel = channel.unary_unary(
                '/mlp.baseline.v1.JobsService/Cancel',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceCancelRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceCancelResponse.FromString,
                _registered_method=True)
        self.Describe = channel.unary_unary(
                '/mlp.baseline.v1.JobsService/Describe',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceDescribeRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceDescribeResponse.FromString,
                _registered_method=True)
        self.List = channel.unary_unary(
                '/mlp.baseline.v1.JobsService/List',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceListRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceListResponse.FromString,
                _registered_method=True)
        self.Initialize = channel.unary_unary(
                '/mlp.baseline.v1.JobsService/Initialize',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceInitializeRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceInitializeResponse.FromString,
                _registered_method=True)
        self.Finalize = channel.unary_unary(
                '/mlp.baseline.v1.JobsService/Finalize',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFinalizeRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFinalizeResponse.FromString,
                _registered_method=True)
        self.FetchOutputUrl = channel.unary_unary(
                '/mlp.baseline.v1.JobsService/FetchOutputUrl',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchOutputUrlRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchOutputUrlResponse.FromString,
                _registered_method=True)
        self.FetchLogs = channel.unary_unary(
                '/mlp.baseline.v1.JobsService/FetchLogs',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchLogsRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchLogsResponse.FromString,
                _registered_method=True)


class JobsServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Run(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Cancel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Describe(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def List(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Initialize(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Finalize(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchOutputUrl(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchLogs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_JobsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Run': grpc.unary_unary_rpc_method_handler(
                    servicer.Run,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceRunRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceRunResponse.SerializeToString,
            ),
            'Cancel': grpc.unary_unary_rpc_method_handler(
                    servicer.Cancel,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceCancelRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceCancelResponse.SerializeToString,
            ),
            'Describe': grpc.unary_unary_rpc_method_handler(
                    servicer.Describe,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceDescribeRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceDescribeResponse.SerializeToString,
            ),
            'List': grpc.unary_unary_rpc_method_handler(
                    servicer.List,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceListRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceListResponse.SerializeToString,
            ),
            'Initialize': grpc.unary_unary_rpc_method_handler(
                    servicer.Initialize,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceInitializeRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceInitializeResponse.SerializeToString,
            ),
            'Finalize': grpc.unary_unary_rpc_method_handler(
                    servicer.Finalize,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFinalizeRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFinalizeResponse.SerializeToString,
            ),
            'FetchOutputUrl': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchOutputUrl,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchOutputUrlRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchOutputUrlResponse.SerializeToString,
            ),
            'FetchLogs': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchLogs,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchLogsRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchLogsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mlp.baseline.v1.JobsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mlp.baseline.v1.JobsService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class JobsService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Run(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JobsService/Run',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceRunRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceRunResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Cancel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JobsService/Cancel',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceCancelRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceCancelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Describe(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JobsService/Describe',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceDescribeRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceDescribeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def List(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JobsService/List',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceListRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Initialize(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JobsService/Initialize',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceInitializeRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceInitializeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Finalize(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JobsService/Finalize',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFinalizeRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFinalizeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchOutputUrl(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JobsService/FetchOutputUrl',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchOutputUrlRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchOutputUrlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchLogs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.JobsService/FetchLogs',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchLogsRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.JobsServiceFetchLogsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class SecretsServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Add = channel.unary_unary(
                '/mlp.baseline.v1.SecretsService/Add',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceAddRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceAddResponse.FromString,
                _registered_method=True)
        self.Update = channel.unary_unary(
                '/mlp.baseline.v1.SecretsService/Update',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceUpdateRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceUpdateResponse.FromString,
                _registered_method=True)
        self.Remove = channel.unary_unary(
                '/mlp.baseline.v1.SecretsService/Remove',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceRemoveRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceRemoveResponse.FromString,
                _registered_method=True)
        self.List = channel.unary_unary(
                '/mlp.baseline.v1.SecretsService/List',
                request_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceListRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceListResponse.FromString,
                _registered_method=True)


class SecretsServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Add(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Update(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Remove(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def List(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SecretsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Add': grpc.unary_unary_rpc_method_handler(
                    servicer.Add,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceAddRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceAddResponse.SerializeToString,
            ),
            'Update': grpc.unary_unary_rpc_method_handler(
                    servicer.Update,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceUpdateRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceUpdateResponse.SerializeToString,
            ),
            'Remove': grpc.unary_unary_rpc_method_handler(
                    servicer.Remove,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceRemoveRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceRemoveResponse.SerializeToString,
            ),
            'List': grpc.unary_unary_rpc_method_handler(
                    servicer.List,
                    request_deserializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceListRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceListResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mlp.baseline.v1.SecretsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mlp.baseline.v1.SecretsService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class SecretsService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Add(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.SecretsService/Add',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceAddRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceAddResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Update(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.SecretsService/Update',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceUpdateRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceUpdateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Remove(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.SecretsService/Remove',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceRemoveRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceRemoveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def List(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.baseline.v1.SecretsService/List',
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceListRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_baseline_dot_v1_dot_service__pb2.SecretsServiceListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
