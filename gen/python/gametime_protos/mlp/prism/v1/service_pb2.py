# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: gametime_protos/mlp/prism/v1/service.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'gametime_protos/mlp/prism/v1/service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*gametime_protos/mlp/prism/v1/service.proto\x12\x0cmlp.prism.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/wrappers.proto\"m\n\x11\x42\x61tchSourceConfig\x12\x14\n\x05table\x18\x01 \x01(\tR\x05table\x12\x42\n\x1elate_arriving_data_lag_seconds\x18\x02 \x01(\x05R\x1alateArrivingDataLagSeconds\"-\n\x15StreamingSourceConfig\x12\x14\n\x05topic\x18\x01 \x01(\tR\x05topic\"\xbc\x01\n\x0cSourceConfig\x12\x35\n\x05\x62\x61tch\x18\x01 \x01(\x0b\x32\x1f.mlp.prism.v1.BatchSourceConfigR\x05\x62\x61tch\x12\x41\n\tstreaming\x18\x02 \x01(\x0b\x32#.mlp.prism.v1.StreamingSourceConfigR\tstreaming\x12\x32\n\x05query\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x05query\"n\n\rFeatureConfig\x12\x16\n\x06\x63olumn\x18\x01 \x01(\tR\x06\x63olumn\x12\x45\n\x0c\x61ggregations\x18\x02 \x03(\x0e\x32!.mlp.prism.v1.AggregationFunctionR\x0c\x61ggregations\"\xf2\x01\n\x0c\x46\x61milyConfig\x12\x32\n\x06source\x18\x01 \x01(\x0b\x32\x1a.mlp.prism.v1.SourceConfigR\x06source\x12\x1b\n\tid_column\x18\x02 \x01(\tR\x08idColumn\x12)\n\x10timestamp_column\x18\x03 \x01(\tR\x0ftimestampColumn\x12-\n\x12identifier_columns\x18\x04 \x03(\tR\x11identifierColumns\x12\x37\n\x08\x66\x65\x61tures\x18\x05 \x03(\x0b\x32\x1b.mlp.prism.v1.FeatureConfigR\x08\x66\x65\x61tures\"|\n\x1c\x46\x61miliesServiceCreateRequest\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\x12\x32\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x1a.mlp.prism.v1.FamilyConfigR\x06\x63onfig\x12\x14\n\x05\x64raft\x18\x03 \x01(\x08R\x05\x64raft\"\x1f\n\x1d\x46\x61miliesServiceCreateResponse\"4\n\x1e\x46\x61miliesServiceDescribeRequest\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\"\xf8\x02\n\x06\x46\x61mily\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\x12\x32\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x1a.mlp.prism.v1.FamilyConfigR\x06\x63onfig\x12\x14\n\x05\x64raft\x18\x03 \x01(\x08R\x05\x64raft\x12;\n\x0binserted_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\ninsertedAt\x12\x32\n\x06status\x18\x05 \x01(\x0e\x32\x1a.mlp.prism.v1.FamilyStatusR\x06status\x12#\n\rstatus_detail\x18\x06 \x01(\tR\x0cstatusDetail\x12\x42\n\x0flast_fetched_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\rlastFetchedAt\x12\x36\n\x08\x66rontier\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x08\x66rontier\"O\n\x1f\x46\x61miliesServiceDescribeResponse\x12,\n\x06\x66\x61mily\x18\x01 \x01(\x0b\x32\x14.mlp.prism.v1.FamilyR\x06\x66\x61mily\"A\n\x1a\x46\x61miliesServiceListRequest\x12#\n\rexclude_draft\x18\x01 \x01(\x08R\x0c\x65xcludeDraft\"O\n\x1b\x46\x61miliesServiceListResponse\x12\x30\n\x08\x66\x61milies\x18\x01 \x03(\x0b\x32\x14.mlp.prism.v1.FamilyR\x08\x66\x61milies\"2\n\x1c\x46\x61miliesServiceDeleteRequest\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\"\x1f\n\x1d\x46\x61miliesServiceDeleteResponse\"\xc4\x01\n\x06Window\x12\x14\n\x05weeks\x18\x01 \x01(\x03R\x05weeks\x12\x12\n\x04\x64\x61ys\x18\x02 \x01(\x03R\x04\x64\x61ys\x12\x14\n\x05hours\x18\x03 \x01(\x03R\x05hours\x12\x18\n\x07minutes\x18\x04 \x01(\x03R\x07minutes\x12\x18\n\x07seconds\x18\x05 \x01(\x03R\x07seconds\x12\"\n\x0cmilliseconds\x18\x06 \x01(\x03R\x0cmilliseconds\x12\"\n\x0cmicroseconds\x18\x07 \x01(\x03R\x0cmicroseconds\"z\n\x0b\x41ggregation\x12=\n\x08\x66unction\x18\x01 \x01(\x0e\x32!.mlp.prism.v1.AggregationFunctionR\x08\x66unction\x12,\n\x06window\x18\x02 \x01(\x0b\x32\x14.mlp.prism.v1.WindowR\x06window\"\xa7\x02\n\x0e\x46\x65\x61tureRequest\x12\x16\n\x06\x66\x61mily\x18\x01 \x01(\tR\x06\x66\x61mily\x12\x16\n\x06\x63olumn\x18\x02 \x01(\tR\x06\x63olumn\x12;\n\x0b\x61ggregation\x18\x03 \x01(\x0b\x32\x19.mlp.prism.v1.AggregationR\x0b\x61ggregation\x12\x62\n\x12identifier_mapping\x18\x04 \x03(\x0b\x32\x33.mlp.prism.v1.FeatureRequest.IdentifierMappingEntryR\x11identifierMapping\x1a\x44\n\x16IdentifierMappingEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"\x9a\x03\n!OnlineFeaturesServiceFetchRequest\x12o\n\x10\x66\x65\x61ture_requests\x18\x01 \x03(\x0b\x32\x44.mlp.prism.v1.OnlineFeaturesServiceFetchRequest.FeatureRequestsEntryR\x0f\x66\x65\x61tureRequests\x12\x62\n\x0bidentifiers\x18\x02 \x03(\x0b\<EMAIL>\x0bidentifiers\x1a`\n\x14\x46\x65\x61tureRequestsEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x32\n\x05value\x18\x02 \x01(\x0b\x32\x1c.mlp.prism.v1.FeatureRequestR\x05value:\x02\x38\x01\x1a>\n\x10IdentifiersEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"x\n\"OnlineFeaturesServiceFetchResponse\x12\x1a\n\x08\x66\x65\x61tures\x18\x01 \x01(\x0cR\x08\x66\x65\x61tures\x12\x36\n\x08\x66rontier\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x08\x66rontier\"\xd4\x02\n*OfflineFeaturesServiceCreateDatasetRequest\x12(\n\x10num_spine_chunks\x18\x01 \x01(\x03R\x0enumSpineChunks\x12x\n\x10\x66\x65\x61ture_requests\x18\x02 \x03(\x0b\x32M.mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest.FeatureRequestsEntryR\x0f\x66\x65\x61tureRequests\x12 \n\x0cspine_s3_uri\x18\x03 \x01(\tR\nspineS3Uri\x1a`\n\x14\x46\x65\x61tureRequestsEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x32\n\x05value\x18\x02 \x01(\x0b\x32\x1c.mlp.prism.v1.FeatureRequestR\x05value:\x02\x38\x01\"8\n\x0cPresignedUrl\x12\x10\n\x03url\x18\x01 \x01(\tR\x03url\x12\x16\n\x06\x66ields\x18\x02 \x01(\tR\x06\x66ields\"\x82\x01\n+OfflineFeaturesServiceCreateDatasetResponse\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\x12\x41\n\x0epresigned_urls\x18\x02 \x03(\x0b\x32\x1a.mlp.prism.v1.PresignedUrlR\rpresignedUrls\"@\n,OfflineFeaturesServiceDescribeDatasetRequest\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\"\x89\x01\n-OfflineFeaturesServiceDescribeDatasetResponse\x12\x33\n\x06status\x18\x01 \x01(\x0e\x32\x1b.mlp.prism.v1.DatasetStatusR\x06status\x12#\n\rstatus_detail\x18\x02 \x01(\tR\x0cstatusDetail\"=\n)OfflineFeaturesServiceFetchDatasetRequest\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\"\x81\x01\n*OfflineFeaturesServiceFetchDatasetResponse\x12\x10\n\x03ref\x18\x01 \x01(\tR\x03ref\x12\x41\n\x0epresigned_urls\x18\x02 \x03(\x0b\x32\x1a.mlp.prism.v1.PresignedUrlR\rpresignedUrls*\xb8\x01\n\x13\x41ggregationFunction\x12$\n AGGREGATION_FUNCTION_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x41GGREGATION_FUNCTION_COUNT\x10\x01\x12\x1c\n\x18\x41GGREGATION_FUNCTION_AVG\x10\x02\x12\x1c\n\x18\x41GGREGATION_FUNCTION_SUM\x10\x03\x12\x1f\n\x1b\x41GGREGATION_FUNCTION_STDDEV\x10\x04*\xb5\x02\n\x0c\x46\x61milyStatus\x12\x1d\n\x19\x46\x41MILY_STATUS_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x46\x41MILY_STATUS_INITIALIZING\x10\x01\x12%\n!FAMILY_STATUS_INITIALIZING_FAILED\x10\x02\x12\x1d\n\x19\x46\x41MILY_STATUS_BACKFILLING\x10\x03\x12$\n FAMILY_STATUS_BACKFILLING_FAILED\x10\x04\x12\x19\n\x15\x46\x41MILY_STATUS_RUNNING\x10\x05\x12 \n\x1c\x46\x41MILY_STATUS_RUNNING_FAILED\x10\x06\x12\x1a\n\x16\x46\x41MILY_STATUS_DELETING\x10\x07\x12!\n\x1d\x46\x41MILY_STATUS_DELETING_FAILED\x10\x08*\xdd\x01\n\rDatasetStatus\x12\x1e\n\x1a\x44\x41TASET_STATUS_UNSPECIFIED\x10\x00\x12\"\n\x1e\x44\x41TASET_STATUS_SPINE_UPLOADING\x10\x01\x12)\n%DATASET_STATUS_SPINE_UPLOADING_FAILED\x10\x02\x12\x1d\n\x19\x44\x41TASET_STATUS_GENERATING\x10\x03\x12$\n DATASET_STATUS_GENERATING_FAILED\x10\x04\x12\x18\n\x14\x44\x41TASET_STATUS_READY\x10\x05\x32\xa5\x03\n\x0f\x46\x61miliesService\x12\x63\n\x06\x43reate\x12*.mlp.prism.v1.FamiliesServiceCreateRequest\x1a+.mlp.prism.v1.FamiliesServiceCreateResponse\"\x00\x12\x63\n\x06\x44\x65lete\x12*.mlp.prism.v1.FamiliesServiceDeleteRequest\x1a+.mlp.prism.v1.FamiliesServiceDeleteResponse\"\x00\x12i\n\x08\x44\x65scribe\x12,.mlp.prism.v1.FamiliesServiceDescribeRequest\x1a-.mlp.prism.v1.FamiliesServiceDescribeResponse\"\x00\x12]\n\x04List\x12(.mlp.prism.v1.FamiliesServiceListRequest\x1a).mlp.prism.v1.FamiliesServiceListResponse\"\x00\x32\x85\x01\n\x15OnlineFeaturesService\x12l\n\x05\x46\x65tch\x12/.mlp.prism.v1.OnlineFeaturesServiceFetchRequest\x1a\x30.mlp.prism.v1.OnlineFeaturesServiceFetchResponse\"\x00\x32\xb6\x03\n\x16OfflineFeaturesService\x12\x86\x01\n\rCreateDataset\x12\x38.mlp.prism.v1.OfflineFeaturesServiceCreateDatasetRequest\x1a\x39.mlp.prism.v1.OfflineFeaturesServiceCreateDatasetResponse\"\x00\x12\x8c\x01\n\x0f\x44\x65scribeDataset\x12:.mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetRequest\x1a;.mlp.prism.v1.OfflineFeaturesServiceDescribeDatasetResponse\"\x00\x12\x83\x01\n\x0c\x46\x65tchDataset\x12\x37.mlp.prism.v1.OfflineFeaturesServiceFetchDatasetRequest\x1a\x38.mlp.prism.v1.OfflineFeaturesServiceFetchDatasetResponse\"\x00\x42\x87\x01\n\x10\x63om.mlp.prism.v1B\x0cServiceProtoP\x01Z\x13mlp/prism/v1;protos\xa2\x02\x03MPX\xaa\x02\x0cMlp.Prism.V1\xca\x02\x0cMlp\\Prism\\V1\xe2\x02\x18Mlp\\Prism\\V1\\GPBMetadata\xea\x02\x0eMlp::Prism::V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gametime_protos.mlp.prism.v1.service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\020com.mlp.prism.v1B\014ServiceProtoP\001Z\023mlp/prism/v1;protos\242\002\003MPX\252\002\014Mlp.Prism.V1\312\002\014Mlp\\Prism\\V1\342\002\030Mlp\\Prism\\V1\\GPBMetadata\352\002\016Mlp::Prism::V1'
  _globals['_FEATUREREQUEST_IDENTIFIERMAPPINGENTRY']._loaded_options = None
  _globals['_FEATUREREQUEST_IDENTIFIERMAPPINGENTRY']._serialized_options = b'8\001'
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST_FEATUREREQUESTSENTRY']._loaded_options = None
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST_FEATUREREQUESTSENTRY']._serialized_options = b'8\001'
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST_IDENTIFIERSENTRY']._loaded_options = None
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST_IDENTIFIERSENTRY']._serialized_options = b'8\001'
  _globals['_OFFLINEFEATURESSERVICECREATEDATASETREQUEST_FEATUREREQUESTSENTRY']._loaded_options = None
  _globals['_OFFLINEFEATURESSERVICECREATEDATASETREQUEST_FEATUREREQUESTSENTRY']._serialized_options = b'8\001'
  _globals['_AGGREGATIONFUNCTION']._serialized_start=3829
  _globals['_AGGREGATIONFUNCTION']._serialized_end=4013
  _globals['_FAMILYSTATUS']._serialized_start=4016
  _globals['_FAMILYSTATUS']._serialized_end=4325
  _globals['_DATASETSTATUS']._serialized_start=4328
  _globals['_DATASETSTATUS']._serialized_end=4549
  _globals['_BATCHSOURCECONFIG']._serialized_start=125
  _globals['_BATCHSOURCECONFIG']._serialized_end=234
  _globals['_STREAMINGSOURCECONFIG']._serialized_start=236
  _globals['_STREAMINGSOURCECONFIG']._serialized_end=281
  _globals['_SOURCECONFIG']._serialized_start=284
  _globals['_SOURCECONFIG']._serialized_end=472
  _globals['_FEATURECONFIG']._serialized_start=474
  _globals['_FEATURECONFIG']._serialized_end=584
  _globals['_FAMILYCONFIG']._serialized_start=587
  _globals['_FAMILYCONFIG']._serialized_end=829
  _globals['_FAMILIESSERVICECREATEREQUEST']._serialized_start=831
  _globals['_FAMILIESSERVICECREATEREQUEST']._serialized_end=955
  _globals['_FAMILIESSERVICECREATERESPONSE']._serialized_start=957
  _globals['_FAMILIESSERVICECREATERESPONSE']._serialized_end=988
  _globals['_FAMILIESSERVICEDESCRIBEREQUEST']._serialized_start=990
  _globals['_FAMILIESSERVICEDESCRIBEREQUEST']._serialized_end=1042
  _globals['_FAMILY']._serialized_start=1045
  _globals['_FAMILY']._serialized_end=1421
  _globals['_FAMILIESSERVICEDESCRIBERESPONSE']._serialized_start=1423
  _globals['_FAMILIESSERVICEDESCRIBERESPONSE']._serialized_end=1502
  _globals['_FAMILIESSERVICELISTREQUEST']._serialized_start=1504
  _globals['_FAMILIESSERVICELISTREQUEST']._serialized_end=1569
  _globals['_FAMILIESSERVICELISTRESPONSE']._serialized_start=1571
  _globals['_FAMILIESSERVICELISTRESPONSE']._serialized_end=1650
  _globals['_FAMILIESSERVICEDELETEREQUEST']._serialized_start=1652
  _globals['_FAMILIESSERVICEDELETEREQUEST']._serialized_end=1702
  _globals['_FAMILIESSERVICEDELETERESPONSE']._serialized_start=1704
  _globals['_FAMILIESSERVICEDELETERESPONSE']._serialized_end=1735
  _globals['_WINDOW']._serialized_start=1738
  _globals['_WINDOW']._serialized_end=1934
  _globals['_AGGREGATION']._serialized_start=1936
  _globals['_AGGREGATION']._serialized_end=2058
  _globals['_FEATUREREQUEST']._serialized_start=2061
  _globals['_FEATUREREQUEST']._serialized_end=2356
  _globals['_FEATUREREQUEST_IDENTIFIERMAPPINGENTRY']._serialized_start=2288
  _globals['_FEATUREREQUEST_IDENTIFIERMAPPINGENTRY']._serialized_end=2356
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST']._serialized_start=2359
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST']._serialized_end=2769
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST_FEATUREREQUESTSENTRY']._serialized_start=2609
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST_FEATUREREQUESTSENTRY']._serialized_end=2705
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST_IDENTIFIERSENTRY']._serialized_start=2707
  _globals['_ONLINEFEATURESSERVICEFETCHREQUEST_IDENTIFIERSENTRY']._serialized_end=2769
  _globals['_ONLINEFEATURESSERVICEFETCHRESPONSE']._serialized_start=2771
  _globals['_ONLINEFEATURESSERVICEFETCHRESPONSE']._serialized_end=2891
  _globals['_OFFLINEFEATURESSERVICECREATEDATASETREQUEST']._serialized_start=2894
  _globals['_OFFLINEFEATURESSERVICECREATEDATASETREQUEST']._serialized_end=3234
  _globals['_OFFLINEFEATURESSERVICECREATEDATASETREQUEST_FEATUREREQUESTSENTRY']._serialized_start=2609
  _globals['_OFFLINEFEATURESSERVICECREATEDATASETREQUEST_FEATUREREQUESTSENTRY']._serialized_end=2705
  _globals['_PRESIGNEDURL']._serialized_start=3236
  _globals['_PRESIGNEDURL']._serialized_end=3292
  _globals['_OFFLINEFEATURESSERVICECREATEDATASETRESPONSE']._serialized_start=3295
  _globals['_OFFLINEFEATURESSERVICECREATEDATASETRESPONSE']._serialized_end=3425
  _globals['_OFFLINEFEATURESSERVICEDESCRIBEDATASETREQUEST']._serialized_start=3427
  _globals['_OFFLINEFEATURESSERVICEDESCRIBEDATASETREQUEST']._serialized_end=3491
  _globals['_OFFLINEFEATURESSERVICEDESCRIBEDATASETRESPONSE']._serialized_start=3494
  _globals['_OFFLINEFEATURESSERVICEDESCRIBEDATASETRESPONSE']._serialized_end=3631
  _globals['_OFFLINEFEATURESSERVICEFETCHDATASETREQUEST']._serialized_start=3633
  _globals['_OFFLINEFEATURESSERVICEFETCHDATASETREQUEST']._serialized_end=3694
  _globals['_OFFLINEFEATURESSERVICEFETCHDATASETRESPONSE']._serialized_start=3697
  _globals['_OFFLINEFEATURESSERVICEFETCHDATASETRESPONSE']._serialized_end=3826
  _globals['_FAMILIESSERVICE']._serialized_start=4552
  _globals['_FAMILIESSERVICE']._serialized_end=4973
  _globals['_ONLINEFEATURESSERVICE']._serialized_start=4976
  _globals['_ONLINEFEATURESSERVICE']._serialized_end=5109
  _globals['_OFFLINEFEATURESSERVICE']._serialized_start=5112
  _globals['_OFFLINEFEATURESSERVICE']._serialized_end=5550
# @@protoc_insertion_point(module_scope)
