from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf import wrappers_pb2 as _wrappers_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class AggregationFunction(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    AGGREGATION_FUNCTION_UNSPECIFIED: _ClassVar[AggregationFunction]
    AGGREGATION_FUNCTION_COUNT: _ClassVar[AggregationFunction]
    AGGREGATION_FUNCTION_AVG: _ClassVar[AggregationFunction]
    AGGREGATION_FUNCTION_SUM: _ClassVar[AggregationFunction]
    AGGREGATION_FUNCTION_STDDEV: _ClassVar[AggregationFunction]

class FamilyStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    FAMILY_STATUS_UNSPECIFIED: _ClassVar[FamilyStatus]
    FAMILY_STATUS_INITIALIZING: _ClassVar[FamilyStatus]
    FAMILY_STATUS_INITIALIZING_FAILED: _ClassVar[FamilyStatus]
    FAMILY_STATUS_BACKFILLING: _ClassVar[FamilyStatus]
    FAMILY_STATUS_BACKFILLING_FAILED: _ClassVar[FamilyStatus]
    FAMILY_STATUS_RUNNING: _ClassVar[FamilyStatus]
    FAMILY_STATUS_RUNNING_FAILED: _ClassVar[FamilyStatus]
    FAMILY_STATUS_DELETING: _ClassVar[FamilyStatus]
    FAMILY_STATUS_DELETING_FAILED: _ClassVar[FamilyStatus]

class DatasetStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    DATASET_STATUS_UNSPECIFIED: _ClassVar[DatasetStatus]
    DATASET_STATUS_SPINE_UPLOADING: _ClassVar[DatasetStatus]
    DATASET_STATUS_SPINE_UPLOADING_FAILED: _ClassVar[DatasetStatus]
    DATASET_STATUS_GENERATING: _ClassVar[DatasetStatus]
    DATASET_STATUS_GENERATING_FAILED: _ClassVar[DatasetStatus]
    DATASET_STATUS_READY: _ClassVar[DatasetStatus]
AGGREGATION_FUNCTION_UNSPECIFIED: AggregationFunction
AGGREGATION_FUNCTION_COUNT: AggregationFunction
AGGREGATION_FUNCTION_AVG: AggregationFunction
AGGREGATION_FUNCTION_SUM: AggregationFunction
AGGREGATION_FUNCTION_STDDEV: AggregationFunction
FAMILY_STATUS_UNSPECIFIED: FamilyStatus
FAMILY_STATUS_INITIALIZING: FamilyStatus
FAMILY_STATUS_INITIALIZING_FAILED: FamilyStatus
FAMILY_STATUS_BACKFILLING: FamilyStatus
FAMILY_STATUS_BACKFILLING_FAILED: FamilyStatus
FAMILY_STATUS_RUNNING: FamilyStatus
FAMILY_STATUS_RUNNING_FAILED: FamilyStatus
FAMILY_STATUS_DELETING: FamilyStatus
FAMILY_STATUS_DELETING_FAILED: FamilyStatus
DATASET_STATUS_UNSPECIFIED: DatasetStatus
DATASET_STATUS_SPINE_UPLOADING: DatasetStatus
DATASET_STATUS_SPINE_UPLOADING_FAILED: DatasetStatus
DATASET_STATUS_GENERATING: DatasetStatus
DATASET_STATUS_GENERATING_FAILED: DatasetStatus
DATASET_STATUS_READY: DatasetStatus

class BatchSourceConfig(_message.Message):
    __slots__ = ("table", "late_arriving_data_lag_seconds")
    TABLE_FIELD_NUMBER: _ClassVar[int]
    LATE_ARRIVING_DATA_LAG_SECONDS_FIELD_NUMBER: _ClassVar[int]
    table: str
    late_arriving_data_lag_seconds: int
    def __init__(self, table: _Optional[str] = ..., late_arriving_data_lag_seconds: _Optional[int] = ...) -> None: ...

class StreamingSourceConfig(_message.Message):
    __slots__ = ("topic",)
    TOPIC_FIELD_NUMBER: _ClassVar[int]
    topic: str
    def __init__(self, topic: _Optional[str] = ...) -> None: ...

class SourceConfig(_message.Message):
    __slots__ = ("batch", "streaming", "query")
    BATCH_FIELD_NUMBER: _ClassVar[int]
    STREAMING_FIELD_NUMBER: _ClassVar[int]
    QUERY_FIELD_NUMBER: _ClassVar[int]
    batch: BatchSourceConfig
    streaming: StreamingSourceConfig
    query: _wrappers_pb2.StringValue
    def __init__(self, batch: _Optional[_Union[BatchSourceConfig, _Mapping]] = ..., streaming: _Optional[_Union[StreamingSourceConfig, _Mapping]] = ..., query: _Optional[_Union[_wrappers_pb2.StringValue, _Mapping]] = ...) -> None: ...

class FeatureConfig(_message.Message):
    __slots__ = ("column", "aggregations")
    COLUMN_FIELD_NUMBER: _ClassVar[int]
    AGGREGATIONS_FIELD_NUMBER: _ClassVar[int]
    column: str
    aggregations: _containers.RepeatedScalarFieldContainer[AggregationFunction]
    def __init__(self, column: _Optional[str] = ..., aggregations: _Optional[_Iterable[_Union[AggregationFunction, str]]] = ...) -> None: ...

class FamilyConfig(_message.Message):
    __slots__ = ("source", "id_column", "timestamp_column", "identifier_columns", "features")
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    ID_COLUMN_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_COLUMN_FIELD_NUMBER: _ClassVar[int]
    IDENTIFIER_COLUMNS_FIELD_NUMBER: _ClassVar[int]
    FEATURES_FIELD_NUMBER: _ClassVar[int]
    source: SourceConfig
    id_column: str
    timestamp_column: str
    identifier_columns: _containers.RepeatedScalarFieldContainer[str]
    features: _containers.RepeatedCompositeFieldContainer[FeatureConfig]
    def __init__(self, source: _Optional[_Union[SourceConfig, _Mapping]] = ..., id_column: _Optional[str] = ..., timestamp_column: _Optional[str] = ..., identifier_columns: _Optional[_Iterable[str]] = ..., features: _Optional[_Iterable[_Union[FeatureConfig, _Mapping]]] = ...) -> None: ...

class FamiliesServiceCreateRequest(_message.Message):
    __slots__ = ("name", "config", "draft")
    NAME_FIELD_NUMBER: _ClassVar[int]
    CONFIG_FIELD_NUMBER: _ClassVar[int]
    DRAFT_FIELD_NUMBER: _ClassVar[int]
    name: str
    config: FamilyConfig
    draft: bool
    def __init__(self, name: _Optional[str] = ..., config: _Optional[_Union[FamilyConfig, _Mapping]] = ..., draft: bool = ...) -> None: ...

class FamiliesServiceCreateResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class FamiliesServiceDescribeRequest(_message.Message):
    __slots__ = ("name",)
    NAME_FIELD_NUMBER: _ClassVar[int]
    name: str
    def __init__(self, name: _Optional[str] = ...) -> None: ...

class Family(_message.Message):
    __slots__ = ("name", "config", "draft", "inserted_at", "status", "status_detail", "last_fetched_at", "frontier")
    NAME_FIELD_NUMBER: _ClassVar[int]
    CONFIG_FIELD_NUMBER: _ClassVar[int]
    DRAFT_FIELD_NUMBER: _ClassVar[int]
    INSERTED_AT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_DETAIL_FIELD_NUMBER: _ClassVar[int]
    LAST_FETCHED_AT_FIELD_NUMBER: _ClassVar[int]
    FRONTIER_FIELD_NUMBER: _ClassVar[int]
    name: str
    config: FamilyConfig
    draft: bool
    inserted_at: _timestamp_pb2.Timestamp
    status: FamilyStatus
    status_detail: str
    last_fetched_at: _timestamp_pb2.Timestamp
    frontier: _timestamp_pb2.Timestamp
    def __init__(self, name: _Optional[str] = ..., config: _Optional[_Union[FamilyConfig, _Mapping]] = ..., draft: bool = ..., inserted_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., status: _Optional[_Union[FamilyStatus, str]] = ..., status_detail: _Optional[str] = ..., last_fetched_at: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., frontier: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class FamiliesServiceDescribeResponse(_message.Message):
    __slots__ = ("family",)
    FAMILY_FIELD_NUMBER: _ClassVar[int]
    family: Family
    def __init__(self, family: _Optional[_Union[Family, _Mapping]] = ...) -> None: ...

class FamiliesServiceListRequest(_message.Message):
    __slots__ = ("exclude_draft",)
    EXCLUDE_DRAFT_FIELD_NUMBER: _ClassVar[int]
    exclude_draft: bool
    def __init__(self, exclude_draft: bool = ...) -> None: ...

class FamiliesServiceListResponse(_message.Message):
    __slots__ = ("families",)
    FAMILIES_FIELD_NUMBER: _ClassVar[int]
    families: _containers.RepeatedCompositeFieldContainer[Family]
    def __init__(self, families: _Optional[_Iterable[_Union[Family, _Mapping]]] = ...) -> None: ...

class FamiliesServiceDeleteRequest(_message.Message):
    __slots__ = ("name",)
    NAME_FIELD_NUMBER: _ClassVar[int]
    name: str
    def __init__(self, name: _Optional[str] = ...) -> None: ...

class FamiliesServiceDeleteResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class Window(_message.Message):
    __slots__ = ("weeks", "days", "hours", "minutes", "seconds", "milliseconds", "microseconds")
    WEEKS_FIELD_NUMBER: _ClassVar[int]
    DAYS_FIELD_NUMBER: _ClassVar[int]
    HOURS_FIELD_NUMBER: _ClassVar[int]
    MINUTES_FIELD_NUMBER: _ClassVar[int]
    SECONDS_FIELD_NUMBER: _ClassVar[int]
    MILLISECONDS_FIELD_NUMBER: _ClassVar[int]
    MICROSECONDS_FIELD_NUMBER: _ClassVar[int]
    weeks: int
    days: int
    hours: int
    minutes: int
    seconds: int
    milliseconds: int
    microseconds: int
    def __init__(self, weeks: _Optional[int] = ..., days: _Optional[int] = ..., hours: _Optional[int] = ..., minutes: _Optional[int] = ..., seconds: _Optional[int] = ..., milliseconds: _Optional[int] = ..., microseconds: _Optional[int] = ...) -> None: ...

class Aggregation(_message.Message):
    __slots__ = ("function", "window")
    FUNCTION_FIELD_NUMBER: _ClassVar[int]
    WINDOW_FIELD_NUMBER: _ClassVar[int]
    function: AggregationFunction
    window: Window
    def __init__(self, function: _Optional[_Union[AggregationFunction, str]] = ..., window: _Optional[_Union[Window, _Mapping]] = ...) -> None: ...

class FeatureRequest(_message.Message):
    __slots__ = ("family", "column", "aggregation", "identifier_mapping")
    class IdentifierMappingEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    FAMILY_FIELD_NUMBER: _ClassVar[int]
    COLUMN_FIELD_NUMBER: _ClassVar[int]
    AGGREGATION_FIELD_NUMBER: _ClassVar[int]
    IDENTIFIER_MAPPING_FIELD_NUMBER: _ClassVar[int]
    family: str
    column: str
    aggregation: Aggregation
    identifier_mapping: _containers.ScalarMap[str, str]
    def __init__(self, family: _Optional[str] = ..., column: _Optional[str] = ..., aggregation: _Optional[_Union[Aggregation, _Mapping]] = ..., identifier_mapping: _Optional[_Mapping[str, str]] = ...) -> None: ...

class OnlineFeaturesServiceFetchRequest(_message.Message):
    __slots__ = ("feature_requests", "identifiers")
    class FeatureRequestsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: FeatureRequest
        def __init__(self, key: _Optional[str] = ..., value: _Optional[_Union[FeatureRequest, _Mapping]] = ...) -> None: ...
    class IdentifiersEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    FEATURE_REQUESTS_FIELD_NUMBER: _ClassVar[int]
    IDENTIFIERS_FIELD_NUMBER: _ClassVar[int]
    feature_requests: _containers.MessageMap[str, FeatureRequest]
    identifiers: _containers.ScalarMap[str, str]
    def __init__(self, feature_requests: _Optional[_Mapping[str, FeatureRequest]] = ..., identifiers: _Optional[_Mapping[str, str]] = ...) -> None: ...

class OnlineFeaturesServiceFetchResponse(_message.Message):
    __slots__ = ("features", "frontier")
    FEATURES_FIELD_NUMBER: _ClassVar[int]
    FRONTIER_FIELD_NUMBER: _ClassVar[int]
    features: bytes
    frontier: _timestamp_pb2.Timestamp
    def __init__(self, features: _Optional[bytes] = ..., frontier: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class OfflineFeaturesServiceCreateDatasetRequest(_message.Message):
    __slots__ = ("num_spine_chunks", "feature_requests", "spine_s3_uri")
    class FeatureRequestsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: FeatureRequest
        def __init__(self, key: _Optional[str] = ..., value: _Optional[_Union[FeatureRequest, _Mapping]] = ...) -> None: ...
    NUM_SPINE_CHUNKS_FIELD_NUMBER: _ClassVar[int]
    FEATURE_REQUESTS_FIELD_NUMBER: _ClassVar[int]
    SPINE_S3_URI_FIELD_NUMBER: _ClassVar[int]
    num_spine_chunks: int
    feature_requests: _containers.MessageMap[str, FeatureRequest]
    spine_s3_uri: str
    def __init__(self, num_spine_chunks: _Optional[int] = ..., feature_requests: _Optional[_Mapping[str, FeatureRequest]] = ..., spine_s3_uri: _Optional[str] = ...) -> None: ...

class PresignedUrl(_message.Message):
    __slots__ = ("url", "fields")
    URL_FIELD_NUMBER: _ClassVar[int]
    FIELDS_FIELD_NUMBER: _ClassVar[int]
    url: str
    fields: str
    def __init__(self, url: _Optional[str] = ..., fields: _Optional[str] = ...) -> None: ...

class OfflineFeaturesServiceCreateDatasetResponse(_message.Message):
    __slots__ = ("ref", "presigned_urls")
    REF_FIELD_NUMBER: _ClassVar[int]
    PRESIGNED_URLS_FIELD_NUMBER: _ClassVar[int]
    ref: str
    presigned_urls: _containers.RepeatedCompositeFieldContainer[PresignedUrl]
    def __init__(self, ref: _Optional[str] = ..., presigned_urls: _Optional[_Iterable[_Union[PresignedUrl, _Mapping]]] = ...) -> None: ...

class OfflineFeaturesServiceDescribeDatasetRequest(_message.Message):
    __slots__ = ("ref",)
    REF_FIELD_NUMBER: _ClassVar[int]
    ref: str
    def __init__(self, ref: _Optional[str] = ...) -> None: ...

class OfflineFeaturesServiceDescribeDatasetResponse(_message.Message):
    __slots__ = ("status", "status_detail")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    STATUS_DETAIL_FIELD_NUMBER: _ClassVar[int]
    status: DatasetStatus
    status_detail: str
    def __init__(self, status: _Optional[_Union[DatasetStatus, str]] = ..., status_detail: _Optional[str] = ...) -> None: ...

class OfflineFeaturesServiceFetchDatasetRequest(_message.Message):
    __slots__ = ("ref",)
    REF_FIELD_NUMBER: _ClassVar[int]
    ref: str
    def __init__(self, ref: _Optional[str] = ...) -> None: ...

class OfflineFeaturesServiceFetchDatasetResponse(_message.Message):
    __slots__ = ("ref", "presigned_urls")
    REF_FIELD_NUMBER: _ClassVar[int]
    PRESIGNED_URLS_FIELD_NUMBER: _ClassVar[int]
    ref: str
    presigned_urls: _containers.RepeatedCompositeFieldContainer[PresignedUrl]
    def __init__(self, ref: _Optional[str] = ..., presigned_urls: _Optional[_Iterable[_Union[PresignedUrl, _Mapping]]] = ...) -> None: ...
