# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from gametime_protos.mlp.prism.v1 import service_pb2 as gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2


class FamiliesServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Create = channel.unary_unary(
                '/mlp.prism.v1.FamiliesService/Create',
                request_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceCreateRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceCreateResponse.FromString,
                _registered_method=True)
        self.Delete = channel.unary_unary(
                '/mlp.prism.v1.FamiliesService/Delete',
                request_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDeleteRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDeleteResponse.FromString,
                _registered_method=True)
        self.Describe = channel.unary_unary(
                '/mlp.prism.v1.FamiliesService/Describe',
                request_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDescribeRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDescribeResponse.FromString,
                _registered_method=True)
        self.List = channel.unary_unary(
                '/mlp.prism.v1.FamiliesService/List',
                request_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceListRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceListResponse.FromString,
                _registered_method=True)


class FamiliesServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Create(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Delete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Describe(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def List(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FamiliesServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Create': grpc.unary_unary_rpc_method_handler(
                    servicer.Create,
                    request_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceCreateRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceCreateResponse.SerializeToString,
            ),
            'Delete': grpc.unary_unary_rpc_method_handler(
                    servicer.Delete,
                    request_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDeleteRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDeleteResponse.SerializeToString,
            ),
            'Describe': grpc.unary_unary_rpc_method_handler(
                    servicer.Describe,
                    request_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDescribeRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDescribeResponse.SerializeToString,
            ),
            'List': grpc.unary_unary_rpc_method_handler(
                    servicer.List,
                    request_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceListRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceListResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mlp.prism.v1.FamiliesService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mlp.prism.v1.FamiliesService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class FamiliesService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Create(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.prism.v1.FamiliesService/Create',
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceCreateRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceCreateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Delete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.prism.v1.FamiliesService/Delete',
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDeleteRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDeleteResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Describe(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.prism.v1.FamiliesService/Describe',
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDescribeRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceDescribeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def List(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.prism.v1.FamiliesService/List',
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceListRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.FamiliesServiceListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class OnlineFeaturesServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Fetch = channel.unary_unary(
                '/mlp.prism.v1.OnlineFeaturesService/Fetch',
                request_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OnlineFeaturesServiceFetchRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OnlineFeaturesServiceFetchResponse.FromString,
                _registered_method=True)


class OnlineFeaturesServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Fetch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OnlineFeaturesServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Fetch': grpc.unary_unary_rpc_method_handler(
                    servicer.Fetch,
                    request_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OnlineFeaturesServiceFetchRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OnlineFeaturesServiceFetchResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mlp.prism.v1.OnlineFeaturesService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mlp.prism.v1.OnlineFeaturesService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OnlineFeaturesService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Fetch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.prism.v1.OnlineFeaturesService/Fetch',
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OnlineFeaturesServiceFetchRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OnlineFeaturesServiceFetchResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class OfflineFeaturesServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateDataset = channel.unary_unary(
                '/mlp.prism.v1.OfflineFeaturesService/CreateDataset',
                request_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceCreateDatasetRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceCreateDatasetResponse.FromString,
                _registered_method=True)
        self.DescribeDataset = channel.unary_unary(
                '/mlp.prism.v1.OfflineFeaturesService/DescribeDataset',
                request_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceDescribeDatasetRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceDescribeDatasetResponse.FromString,
                _registered_method=True)
        self.FetchDataset = channel.unary_unary(
                '/mlp.prism.v1.OfflineFeaturesService/FetchDataset',
                request_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceFetchDatasetRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceFetchDatasetResponse.FromString,
                _registered_method=True)


class OfflineFeaturesServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateDataset(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeDataset(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchDataset(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OfflineFeaturesServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateDataset': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDataset,
                    request_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceCreateDatasetRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceCreateDatasetResponse.SerializeToString,
            ),
            'DescribeDataset': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeDataset,
                    request_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceDescribeDatasetRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceDescribeDatasetResponse.SerializeToString,
            ),
            'FetchDataset': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchDataset,
                    request_deserializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceFetchDatasetRequest.FromString,
                    response_serializer=gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceFetchDatasetResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'mlp.prism.v1.OfflineFeaturesService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('mlp.prism.v1.OfflineFeaturesService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OfflineFeaturesService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateDataset(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.prism.v1.OfflineFeaturesService/CreateDataset',
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceCreateDatasetRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceCreateDatasetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeDataset(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.prism.v1.OfflineFeaturesService/DescribeDataset',
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceDescribeDatasetRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceDescribeDatasetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchDataset(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/mlp.prism.v1.OfflineFeaturesService/FetchDataset',
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceFetchDatasetRequest.SerializeToString,
            gametime__protos_dot_mlp_dot_prism_dot_v1_dot_service__pb2.OfflineFeaturesServiceFetchDatasetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
