# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: gametime_protos/pandc/curation/v1/service.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'gametime_protos/pandc/curation/v1/service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/gametime_protos/pandc/curation/v1/service.proto\x12\x11pandc.curation.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"3\n\tZoomLevel\x12\x10\n\x03tag\x18\x01 \x01(\x05R\x03tag\x12\x14\n\x05\x63ount\x18\x02 \x01(\x05R\x05\x63ount\"M\n\x0f\x43urationOptions\x12:\n\x19include_overflow_listings\x18\x01 \x01(\x08R\x17includeOverflowListings\"\xfa\x02\n\x1c\x43urationServiceCurateRequest\x12\x19\n\x08\x65vent_id\x18\x01 \x01(\tR\x07\x65ventId\x12\x14\n\x05venue\x18\x02 \x01(\tR\x05venue\x12!\n\x0cvenue_config\x18\x03 \x01(\tR\x0bvenueConfig\x12\x1a\n\x08listings\x18\x04 \x01(\x0cR\x08listings\x12\x1a\n\x08quantity\x18\x05 \x01(\x05R\x08quantity\x12=\n\x0bzoom_levels\x18\x06 \x03(\x0b\x32\x1c.pandc.curation.v1.ZoomLevelR\nzoomLevels\x12\x39\n\nevent_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\teventTime\x12\x16\n\x06league\x18\x08 \x01(\tR\x06league\x12<\n\x07options\x18\t \x01(\x0b\x32\".pandc.curation.v1.CurationOptionsR\x07options\"9\n\x1d\x43urationServiceCurateResponse\x12\x18\n\x07results\x18\x01 \x01(\x0cR\x07results\"\x8c\x01\n#TableServiceUpsertEssentialsRequest\x12\x14\n\x05venue\x18\x01 \x01(\tR\x05venue\x12!\n\x0cvenue_config\x18\x02 \x01(\tR\x0bvenueConfig\x12\x12\n\x04\x62ody\x18\x03 \x01(\x0cR\x04\x62ody\x12\x18\n\x07version\x18\x04 \x01(\tR\x07version\"&\n$TableServiceUpsertEssentialsResponse\"`\n%TableServiceDescribeEssentialsRequest\x12\x14\n\x05venue\x18\x01 \x01(\tR\x05venue\x12!\n\x0cvenue_config\x18\x02 \x01(\tR\x0bvenueConfig\"B\n&TableServiceDescribeEssentialsResponse\x12\x18\n\x07version\x18\x01 \x01(\tR\x07version2\x80\x01\n\x0f\x43urationService\x12m\n\x06\x43urate\x12/.pandc.curation.v1.CurationServiceCurateRequest\x1a\x30.pandc.curation.v1.CurationServiceCurateResponse\"\x00\x32\xa4\x02\n\x0cTableService\x12\x85\x01\n\x10UpsertEssentials\x12\x36.pandc.curation.v1.TableServiceUpsertEssentialsRequest\x1a\x37.pandc.curation.v1.TableServiceUpsertEssentialsResponse\"\x00\x12\x8b\x01\n\x12\x44\x65scribeEssentials\x12\x38.pandc.curation.v1.TableServiceDescribeEssentialsRequest\x1a\x39.pandc.curation.v1.TableServiceDescribeEssentialsResponse\"\x00\x42\xa5\x01\n\x15\x63om.pandc.curation.v1B\x0cServiceProtoP\x01Z\x18pandc/curation/v1;protos\xa2\x02\x03PCX\xaa\x02\x11Pandc.Curation.V1\xca\x02\x11Pandc\\Curation\\V1\xe2\x02\x1dPandc\\Curation\\V1\\GPBMetadata\xea\x02\x13Pandc::Curation::V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gametime_protos.pandc.curation.v1.service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\025com.pandc.curation.v1B\014ServiceProtoP\001Z\030pandc/curation/v1;protos\242\002\003PCX\252\002\021Pandc.Curation.V1\312\002\021Pandc\\Curation\\V1\342\002\035Pandc\\Curation\\V1\\GPBMetadata\352\002\023Pandc::Curation::V1'
  _globals['_ZOOMLEVEL']._serialized_start=103
  _globals['_ZOOMLEVEL']._serialized_end=154
  _globals['_CURATIONOPTIONS']._serialized_start=156
  _globals['_CURATIONOPTIONS']._serialized_end=233
  _globals['_CURATIONSERVICECURATEREQUEST']._serialized_start=236
  _globals['_CURATIONSERVICECURATEREQUEST']._serialized_end=614
  _globals['_CURATIONSERVICECURATERESPONSE']._serialized_start=616
  _globals['_CURATIONSERVICECURATERESPONSE']._serialized_end=673
  _globals['_TABLESERVICEUPSERTESSENTIALSREQUEST']._serialized_start=676
  _globals['_TABLESERVICEUPSERTESSENTIALSREQUEST']._serialized_end=816
  _globals['_TABLESERVICEUPSERTESSENTIALSRESPONSE']._serialized_start=818
  _globals['_TABLESERVICEUPSERTESSENTIALSRESPONSE']._serialized_end=856
  _globals['_TABLESERVICEDESCRIBEESSENTIALSREQUEST']._serialized_start=858
  _globals['_TABLESERVICEDESCRIBEESSENTIALSREQUEST']._serialized_end=954
  _globals['_TABLESERVICEDESCRIBEESSENTIALSRESPONSE']._serialized_start=956
  _globals['_TABLESERVICEDESCRIBEESSENTIALSRESPONSE']._serialized_end=1022
  _globals['_CURATIONSERVICE']._serialized_start=1025
  _globals['_CURATIONSERVICE']._serialized_end=1153
  _globals['_TABLESERVICE']._serialized_start=1156
  _globals['_TABLESERVICE']._serialized_end=1448
# @@protoc_insertion_point(module_scope)
