from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ZoomLevel(_message.Message):
    __slots__ = ("tag", "count")
    TAG_FIELD_NUMBER: _ClassVar[int]
    COUNT_FIELD_NUMBER: _ClassVar[int]
    tag: int
    count: int
    def __init__(self, tag: _Optional[int] = ..., count: _Optional[int] = ...) -> None: ...

class CurationOptions(_message.Message):
    __slots__ = ("include_overflow_listings",)
    INCLUDE_OVERFLOW_LISTINGS_FIELD_NUMBER: _ClassVar[int]
    include_overflow_listings: bool
    def __init__(self, include_overflow_listings: bool = ...) -> None: ...

class CurationServiceCurateRequest(_message.Message):
    __slots__ = ("event_id", "venue", "venue_config", "listings", "quantity", "zoom_levels", "event_time", "league", "options")
    EVENT_ID_FIELD_NUMBER: _ClassVar[int]
    VENUE_FIELD_NUMBER: _ClassVar[int]
    VENUE_CONFIG_FIELD_NUMBER: _ClassVar[int]
    LISTINGS_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_FIELD_NUMBER: _ClassVar[int]
    ZOOM_LEVELS_FIELD_NUMBER: _ClassVar[int]
    EVENT_TIME_FIELD_NUMBER: _ClassVar[int]
    LEAGUE_FIELD_NUMBER: _ClassVar[int]
    OPTIONS_FIELD_NUMBER: _ClassVar[int]
    event_id: str
    venue: str
    venue_config: str
    listings: bytes
    quantity: int
    zoom_levels: _containers.RepeatedCompositeFieldContainer[ZoomLevel]
    event_time: _timestamp_pb2.Timestamp
    league: str
    options: CurationOptions
    def __init__(self, event_id: _Optional[str] = ..., venue: _Optional[str] = ..., venue_config: _Optional[str] = ..., listings: _Optional[bytes] = ..., quantity: _Optional[int] = ..., zoom_levels: _Optional[_Iterable[_Union[ZoomLevel, _Mapping]]] = ..., event_time: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., league: _Optional[str] = ..., options: _Optional[_Union[CurationOptions, _Mapping]] = ...) -> None: ...

class CurationServiceCurateResponse(_message.Message):
    __slots__ = ("results",)
    RESULTS_FIELD_NUMBER: _ClassVar[int]
    results: bytes
    def __init__(self, results: _Optional[bytes] = ...) -> None: ...

class TableServiceUpsertEssentialsRequest(_message.Message):
    __slots__ = ("venue", "venue_config", "body", "version")
    VENUE_FIELD_NUMBER: _ClassVar[int]
    VENUE_CONFIG_FIELD_NUMBER: _ClassVar[int]
    BODY_FIELD_NUMBER: _ClassVar[int]
    VERSION_FIELD_NUMBER: _ClassVar[int]
    venue: str
    venue_config: str
    body: bytes
    version: str
    def __init__(self, venue: _Optional[str] = ..., venue_config: _Optional[str] = ..., body: _Optional[bytes] = ..., version: _Optional[str] = ...) -> None: ...

class TableServiceUpsertEssentialsResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class TableServiceDescribeEssentialsRequest(_message.Message):
    __slots__ = ("venue", "venue_config")
    VENUE_FIELD_NUMBER: _ClassVar[int]
    VENUE_CONFIG_FIELD_NUMBER: _ClassVar[int]
    venue: str
    venue_config: str
    def __init__(self, venue: _Optional[str] = ..., venue_config: _Optional[str] = ...) -> None: ...

class TableServiceDescribeEssentialsResponse(_message.Message):
    __slots__ = ("version",)
    VERSION_FIELD_NUMBER: _ClassVar[int]
    version: str
    def __init__(self, version: _Optional[str] = ...) -> None: ...
