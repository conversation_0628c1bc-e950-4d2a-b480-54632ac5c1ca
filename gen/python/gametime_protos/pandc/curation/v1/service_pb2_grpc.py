# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from gametime_protos.pandc.curation.v1 import service_pb2 as gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2


class CurationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Curate = channel.unary_unary(
                '/pandc.curation.v1.CurationService/Curate',
                request_serializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.CurationServiceCurateRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.CurationServiceCurateResponse.FromString,
                _registered_method=True)


class CurationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Curate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CurationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Curate': grpc.unary_unary_rpc_method_handler(
                    servicer.Curate,
                    request_deserializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.CurationServiceCurateRequest.FromString,
                    response_serializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.CurationServiceCurateResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'pandc.curation.v1.CurationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('pandc.curation.v1.CurationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class CurationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Curate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/pandc.curation.v1.CurationService/Curate',
            gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.CurationServiceCurateRequest.SerializeToString,
            gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.CurationServiceCurateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)


class TableServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.UpsertEssentials = channel.unary_unary(
                '/pandc.curation.v1.TableService/UpsertEssentials',
                request_serializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceUpsertEssentialsRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceUpsertEssentialsResponse.FromString,
                _registered_method=True)
        self.DescribeEssentials = channel.unary_unary(
                '/pandc.curation.v1.TableService/DescribeEssentials',
                request_serializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceDescribeEssentialsRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceDescribeEssentialsResponse.FromString,
                _registered_method=True)


class TableServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def UpsertEssentials(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DescribeEssentials(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TableServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'UpsertEssentials': grpc.unary_unary_rpc_method_handler(
                    servicer.UpsertEssentials,
                    request_deserializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceUpsertEssentialsRequest.FromString,
                    response_serializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceUpsertEssentialsResponse.SerializeToString,
            ),
            'DescribeEssentials': grpc.unary_unary_rpc_method_handler(
                    servicer.DescribeEssentials,
                    request_deserializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceDescribeEssentialsRequest.FromString,
                    response_serializer=gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceDescribeEssentialsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'pandc.curation.v1.TableService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('pandc.curation.v1.TableService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class TableService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def UpsertEssentials(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/pandc.curation.v1.TableService/UpsertEssentials',
            gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceUpsertEssentialsRequest.SerializeToString,
            gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceUpsertEssentialsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DescribeEssentials(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/pandc.curation.v1.TableService/DescribeEssentials',
            gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceDescribeEssentialsRequest.SerializeToString,
            gametime__protos_dot_pandc_dot_curation_dot_v1_dot_service__pb2.TableServiceDescribeEssentialsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
