# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: gametime_protos/pandc/pricing/v1/service.proto
# Protobuf Python Version: 5.29.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    1,
    '',
    'gametime_protos/pandc/pricing/v1/service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.gametime_protos/pandc/pricing/v1/service.proto\x12\x10pandc.pricing.v1\"k\n\x1aPricingServicePriceRequest\x12\x16\n\x06league\x18\x01 \x01(\tR\x06league\x12\x19\n\x08\x65vent_id\x18\x02 \x01(\tR\x07\x65ventId\x12\x1a\n\x08listings\x18\x03 \x01(\x0cR\x08listings\"O\n\x1bPricingServicePriceResponse\x12\x16\n\x06prices\x18\x01 \x01(\x0cR\x06prices\x12\x18\n\x07results\x18\x02 \x01(\x0cR\x07results\"\x8c\x02\n PricingServiceInvertPriceRequest\x12\x16\n\x06league\x18\x01 \x01(\tR\x06league\x12\x19\n\x08\x65vent_id\x18\x02 \x01(\tR\x07\x65ventId\x12,\n\x12target_price_cents\x18\x03 \x01(\x05R\x10targetPriceCents\x12&\n\x0funit_cost_cents\x18\x04 \x01(\x05R\runitCostCents\x12\x19\n\x08sell_fee\x18\x05 \x01(\x01R\x07sellFee\x12\x1b\n\x07is_spec\x18\x06 \x01(\x08\x42\x02\x18\x01R\x06isSpec\x12\'\n\x10is_high_ops_cost\x18\x07 \x01(\x08R\risHighOpsCost\"M\n!PricingServiceInvertPriceResponse\x12(\n\x10target_promo_fee\x18\x01 \x01(\x01R\x0etargetPromoFee\"\xbd\x01\n\"PricingServiceUpdateMarkupsRequest\x12[\n\x07markups\x18\x01 \x03(\x0b\x32\x41.pandc.pricing.v1.PricingServiceUpdateMarkupsRequest.MarkupsEntryR\x07markups\x1a:\n\x0cMarkupsEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\x02R\x05value:\x02\x38\x01\"%\n#PricingServiceUpdateMarkupsResponse\"$\n\"PricingServiceFetchSettingsRequest\"\xda\x03\n#PricingServiceFetchSettingsResponse\x12%\n\x0e\x64\x65\x66\x61ult_markup\x18\x01 \x01(\x01R\rdefaultMarkup\x12\x85\x01\n\x16\x64\x65\x66\x61ult_league_markups\x18\x02 \x03(\x0b\x32O.pandc.pricing.v1.PricingServiceFetchSettingsResponse.DefaultLeagueMarkupsEntryR\x14\x64\x65\x66\x61ultLeagueMarkups\x12\x1d\n\nmin_markup\x18\x03 \x01(\x01R\tminMarkup\x12\x1d\n\nmax_markup\x18\x04 \x01(\x01R\tmaxMarkup\x12#\n\x0bspec_markup\x18\x05 \x01(\x01\x42\x02\x18\x01R\nspecMarkup\x12\'\n\x0fintercept_cents\x18\x06 \x01(\x05R\x0einterceptCents\x12/\n\x14high_ops_cost_markup\x18\x07 \x01(\x01R\x11highOpsCostMarkup\x1aG\n\x19\x44\x65\x66\x61ultLeagueMarkupsEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\x01R\x05value:\x02\x38\x01\x32\xf2\x03\n\x0ePricingService\x12\x66\n\x05Price\x12,.pandc.pricing.v1.PricingServicePriceRequest\x1a-.pandc.pricing.v1.PricingServicePriceResponse\"\x00\x12x\n\x0bInvertPrice\x12\x32.pandc.pricing.v1.PricingServiceInvertPriceRequest\x1a\x33.pandc.pricing.v1.PricingServiceInvertPriceResponse\"\x00\x12~\n\rFetchSettings\x12\x34.pandc.pricing.v1.PricingServiceFetchSettingsRequest\x1a\x35.pandc.pricing.v1.PricingServiceFetchSettingsResponse\"\x00\x12~\n\rUpdateMarkups\x12\x34.pandc.pricing.v1.PricingServiceUpdateMarkupsRequest\x1a\x35.pandc.pricing.v1.PricingServiceUpdateMarkupsResponse\"\x00\x42\x9f\x01\n\x14\x63om.pandc.pricing.v1B\x0cServiceProtoP\x01Z\x17pandc/pricing/v1;protos\xa2\x02\x03PPX\xaa\x02\x10Pandc.Pricing.V1\xca\x02\x10Pandc\\Pricing\\V1\xe2\x02\x1cPandc\\Pricing\\V1\\GPBMetadata\xea\x02\x12Pandc::Pricing::V1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'gametime_protos.pandc.pricing.v1.service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\024com.pandc.pricing.v1B\014ServiceProtoP\001Z\027pandc/pricing/v1;protos\242\002\003PPX\252\002\020Pandc.Pricing.V1\312\002\020Pandc\\Pricing\\V1\342\002\034Pandc\\Pricing\\V1\\GPBMetadata\352\002\022Pandc::Pricing::V1'
  _globals['_PRICINGSERVICEINVERTPRICEREQUEST'].fields_by_name['is_spec']._loaded_options = None
  _globals['_PRICINGSERVICEINVERTPRICEREQUEST'].fields_by_name['is_spec']._serialized_options = b'\030\001'
  _globals['_PRICINGSERVICEUPDATEMARKUPSREQUEST_MARKUPSENTRY']._loaded_options = None
  _globals['_PRICINGSERVICEUPDATEMARKUPSREQUEST_MARKUPSENTRY']._serialized_options = b'8\001'
  _globals['_PRICINGSERVICEFETCHSETTINGSRESPONSE_DEFAULTLEAGUEMARKUPSENTRY']._loaded_options = None
  _globals['_PRICINGSERVICEFETCHSETTINGSRESPONSE_DEFAULTLEAGUEMARKUPSENTRY']._serialized_options = b'8\001'
  _globals['_PRICINGSERVICEFETCHSETTINGSRESPONSE'].fields_by_name['spec_markup']._loaded_options = None
  _globals['_PRICINGSERVICEFETCHSETTINGSRESPONSE'].fields_by_name['spec_markup']._serialized_options = b'\030\001'
  _globals['_PRICINGSERVICEPRICEREQUEST']._serialized_start=68
  _globals['_PRICINGSERVICEPRICEREQUEST']._serialized_end=175
  _globals['_PRICINGSERVICEPRICERESPONSE']._serialized_start=177
  _globals['_PRICINGSERVICEPRICERESPONSE']._serialized_end=256
  _globals['_PRICINGSERVICEINVERTPRICEREQUEST']._serialized_start=259
  _globals['_PRICINGSERVICEINVERTPRICEREQUEST']._serialized_end=527
  _globals['_PRICINGSERVICEINVERTPRICERESPONSE']._serialized_start=529
  _globals['_PRICINGSERVICEINVERTPRICERESPONSE']._serialized_end=606
  _globals['_PRICINGSERVICEUPDATEMARKUPSREQUEST']._serialized_start=609
  _globals['_PRICINGSERVICEUPDATEMARKUPSREQUEST']._serialized_end=798
  _globals['_PRICINGSERVICEUPDATEMARKUPSREQUEST_MARKUPSENTRY']._serialized_start=740
  _globals['_PRICINGSERVICEUPDATEMARKUPSREQUEST_MARKUPSENTRY']._serialized_end=798
  _globals['_PRICINGSERVICEUPDATEMARKUPSRESPONSE']._serialized_start=800
  _globals['_PRICINGSERVICEUPDATEMARKUPSRESPONSE']._serialized_end=837
  _globals['_PRICINGSERVICEFETCHSETTINGSREQUEST']._serialized_start=839
  _globals['_PRICINGSERVICEFETCHSETTINGSREQUEST']._serialized_end=875
  _globals['_PRICINGSERVICEFETCHSETTINGSRESPONSE']._serialized_start=878
  _globals['_PRICINGSERVICEFETCHSETTINGSRESPONSE']._serialized_end=1352
  _globals['_PRICINGSERVICEFETCHSETTINGSRESPONSE_DEFAULTLEAGUEMARKUPSENTRY']._serialized_start=1281
  _globals['_PRICINGSERVICEFETCHSETTINGSRESPONSE_DEFAULTLEAGUEMARKUPSENTRY']._serialized_end=1352
  _globals['_PRICINGSERVICE']._serialized_start=1355
  _globals['_PRICINGSERVICE']._serialized_end=1853
# @@protoc_insertion_point(module_scope)
