from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class PricingServicePriceRequest(_message.Message):
    __slots__ = ("league", "event_id", "listings")
    LEAGUE_FIELD_NUMBER: _ClassVar[int]
    EVENT_ID_FIELD_NUMBER: _ClassVar[int]
    LISTINGS_FIELD_NUMBER: _ClassVar[int]
    league: str
    event_id: str
    listings: bytes
    def __init__(self, league: _Optional[str] = ..., event_id: _Optional[str] = ..., listings: _Optional[bytes] = ...) -> None: ...

class PricingServicePriceResponse(_message.Message):
    __slots__ = ("prices", "results")
    PRICES_FIELD_NUMBER: _ClassVar[int]
    RESULTS_FIELD_NUMBER: _ClassVar[int]
    prices: bytes
    results: bytes
    def __init__(self, prices: _Optional[bytes] = ..., results: _Optional[bytes] = ...) -> None: ...

class PricingServiceInvertPriceRequest(_message.Message):
    __slots__ = ("league", "event_id", "target_price_cents", "unit_cost_cents", "sell_fee", "is_spec", "is_high_ops_cost")
    LEAGUE_FIELD_NUMBER: _ClassVar[int]
    EVENT_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_PRICE_CENTS_FIELD_NUMBER: _ClassVar[int]
    UNIT_COST_CENTS_FIELD_NUMBER: _ClassVar[int]
    SELL_FEE_FIELD_NUMBER: _ClassVar[int]
    IS_SPEC_FIELD_NUMBER: _ClassVar[int]
    IS_HIGH_OPS_COST_FIELD_NUMBER: _ClassVar[int]
    league: str
    event_id: str
    target_price_cents: int
    unit_cost_cents: int
    sell_fee: float
    is_spec: bool
    is_high_ops_cost: bool
    def __init__(self, league: _Optional[str] = ..., event_id: _Optional[str] = ..., target_price_cents: _Optional[int] = ..., unit_cost_cents: _Optional[int] = ..., sell_fee: _Optional[float] = ..., is_spec: bool = ..., is_high_ops_cost: bool = ...) -> None: ...

class PricingServiceInvertPriceResponse(_message.Message):
    __slots__ = ("target_promo_fee",)
    TARGET_PROMO_FEE_FIELD_NUMBER: _ClassVar[int]
    target_promo_fee: float
    def __init__(self, target_promo_fee: _Optional[float] = ...) -> None: ...

class PricingServiceUpdateMarkupsRequest(_message.Message):
    __slots__ = ("markups",)
    class MarkupsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: float
        def __init__(self, key: _Optional[str] = ..., value: _Optional[float] = ...) -> None: ...
    MARKUPS_FIELD_NUMBER: _ClassVar[int]
    markups: _containers.ScalarMap[str, float]
    def __init__(self, markups: _Optional[_Mapping[str, float]] = ...) -> None: ...

class PricingServiceUpdateMarkupsResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class PricingServiceFetchSettingsRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class PricingServiceFetchSettingsResponse(_message.Message):
    __slots__ = ("default_markup", "default_league_markups", "min_markup", "max_markup", "spec_markup", "intercept_cents", "high_ops_cost_markup")
    class DefaultLeagueMarkupsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: float
        def __init__(self, key: _Optional[str] = ..., value: _Optional[float] = ...) -> None: ...
    DEFAULT_MARKUP_FIELD_NUMBER: _ClassVar[int]
    DEFAULT_LEAGUE_MARKUPS_FIELD_NUMBER: _ClassVar[int]
    MIN_MARKUP_FIELD_NUMBER: _ClassVar[int]
    MAX_MARKUP_FIELD_NUMBER: _ClassVar[int]
    SPEC_MARKUP_FIELD_NUMBER: _ClassVar[int]
    INTERCEPT_CENTS_FIELD_NUMBER: _ClassVar[int]
    HIGH_OPS_COST_MARKUP_FIELD_NUMBER: _ClassVar[int]
    default_markup: float
    default_league_markups: _containers.ScalarMap[str, float]
    min_markup: float
    max_markup: float
    spec_markup: float
    intercept_cents: int
    high_ops_cost_markup: float
    def __init__(self, default_markup: _Optional[float] = ..., default_league_markups: _Optional[_Mapping[str, float]] = ..., min_markup: _Optional[float] = ..., max_markup: _Optional[float] = ..., spec_markup: _Optional[float] = ..., intercept_cents: _Optional[int] = ..., high_ops_cost_markup: _Optional[float] = ...) -> None: ...
