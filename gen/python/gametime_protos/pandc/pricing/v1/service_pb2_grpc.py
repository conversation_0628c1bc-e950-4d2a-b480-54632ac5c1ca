# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from gametime_protos.pandc.pricing.v1 import service_pb2 as gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2


class PricingServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Price = channel.unary_unary(
                '/pandc.pricing.v1.PricingService/Price',
                request_serializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServicePriceRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServicePriceResponse.FromString,
                _registered_method=True)
        self.InvertPrice = channel.unary_unary(
                '/pandc.pricing.v1.PricingService/InvertPrice',
                request_serializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceInvertPriceRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceInvertPriceResponse.FromString,
                _registered_method=True)
        self.FetchSettings = channel.unary_unary(
                '/pandc.pricing.v1.PricingService/FetchSettings',
                request_serializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceFetchSettingsRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceFetchSettingsResponse.FromString,
                _registered_method=True)
        self.UpdateMarkups = channel.unary_unary(
                '/pandc.pricing.v1.PricingService/UpdateMarkups',
                request_serializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceUpdateMarkupsRequest.SerializeToString,
                response_deserializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceUpdateMarkupsResponse.FromString,
                _registered_method=True)


class PricingServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Price(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def InvertPrice(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateMarkups(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PricingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Price': grpc.unary_unary_rpc_method_handler(
                    servicer.Price,
                    request_deserializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServicePriceRequest.FromString,
                    response_serializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServicePriceResponse.SerializeToString,
            ),
            'InvertPrice': grpc.unary_unary_rpc_method_handler(
                    servicer.InvertPrice,
                    request_deserializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceInvertPriceRequest.FromString,
                    response_serializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceInvertPriceResponse.SerializeToString,
            ),
            'FetchSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchSettings,
                    request_deserializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceFetchSettingsRequest.FromString,
                    response_serializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceFetchSettingsResponse.SerializeToString,
            ),
            'UpdateMarkups': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateMarkups,
                    request_deserializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceUpdateMarkupsRequest.FromString,
                    response_serializer=gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceUpdateMarkupsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'pandc.pricing.v1.PricingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('pandc.pricing.v1.PricingService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PricingService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Price(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/pandc.pricing.v1.PricingService/Price',
            gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServicePriceRequest.SerializeToString,
            gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServicePriceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def InvertPrice(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/pandc.pricing.v1.PricingService/InvertPrice',
            gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceInvertPriceRequest.SerializeToString,
            gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceInvertPriceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/pandc.pricing.v1.PricingService/FetchSettings',
            gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceFetchSettingsRequest.SerializeToString,
            gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceFetchSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateMarkups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/pandc.pricing.v1.PricingService/UpdateMarkups',
            gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceUpdateMarkupsRequest.SerializeToString,
            gametime__protos_dot_pandc_dot_pricing_dot_v1_dot_service__pb2.PricingServiceUpdateMarkupsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
