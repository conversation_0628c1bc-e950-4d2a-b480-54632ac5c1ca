# MLCTL - ML Platform Command Line Interface

This guide provides detailed instructions for installing and using the `mlctl` command line interface for Gametime's ML Platform.

## For Users

### Production Access (Standard Users)

1. Request VPN access:
   - Post in the #it Slack channel requesting to be added to the Baseline group in Perimeter81
   - Wait for confirmation that you've been added to the group
   - Ensure you can connect to the VPN

2. Install pipx and mlctl:
   ```bash
   brew install pipx
   pipx install "git+ssh://**************/gametimesf/mlplatform.git#subdirectory=mlctl"
   ```

3. Request service access:
   - Reach out to ML Platform team on Slack
   - Provide your Gametime email address
   - Wait for confirmation that access has been granted and token provided

4. Once you receive your token, configure mlctl:
   ```bash
   mlctl configure  # Uses default ~/.mlctl/config.json
   ```
   Enter:
   - URL: `baseline.production.gteng.co:443`
   - Email: Your Gametime email
   - Token: Provided access token

### Development Access (Baseline Developers)

If you're developing features for Baseline, you'll need both staging and production access.

#### Standard Development
For using released versions with multiple environments:

1. Create config files:
   ```bash
   mkdir -p ~/.mlctl
   touch ~/.mlctl/staging-config.json ~/.mlctl/prod-config.json
   ```
   Note: When no --config flag is specified, mlctl will use ~/.mlctl/config.json by default.

2. Install CLI:
   ```bash
   pipx install "git+ssh://**************/gametimesf/mlplatform.git#subdirectory=mlctl"
   ```

3. Configure environments:
   ```bash
   # Configure staging
   mlctl --config ~/.mlctl/staging-config.json configure
   # Configure production
   mlctl --config ~/.mlctl/prod-config.json configure
   # Optionally configure default environment
   mlctl configure  # Uses ~/.mlctl/config.json
   ```
   For staging, enter:
   - URL: `baseline.staging.gteng.co:443`
   - Email: Your Gametime email
   - Token: Provided staging token

   For production, enter:
   - URL: `baseline.production.gteng.co:443`
   - Email: Your Gametime email
   - Token: Provided production token

4. Create aliases (optional):
   Add to your ~/.bashrc or ~/.zshrc:
   ```bash
   alias mlctl-staging='mlctl --config ~/.mlctl/staging-config.json'
   alias mlctl-prod='mlctl --config ~/.mlctl/prod-config.json'
   # Commands without --config will use ~/.mlctl/config.json
   ```

#### Local Development

1. Setup AWS CodeArtifact authentication:

   ```bash
   # Install keyring and AWS CodeArtifact plugin globally
   uv tool install keyring --with keyrings.codeartifact

   # Set the required username for CodeArtifact (only needed once per terminal session, could also be set in .zshrc)
   export UV_INDEX_CODEARTIFACT_USERNAME=aws
   ```

2. Run uv commands:
   ```bash
   # Install dependencies
   uv sync --dev --upgrade

   # Run the CLI
   uv run -- mlctl --config ~/.mlctl/staging-config.json jupyter start
   ```

## Using MLCTL

### Managing Jobs

The `job` command allows you to run and manage ML jobs:

1. List jobs:
   ```bash
   # Basic usage (uses ~/.mlctl/config.json)
   mlctl job list <family-name>

   # With specific config
   mlctl --config ~/.mlctl/staging-config.json job list <family-name>

   # With alias
   mlctl-staging job list <family-name>
   ```

2. Run jobs:
   ```bash
   # Basic execution
   mlctl job run <family-name>

   # With parameters
   mlctl job run <family-name> -p learning_rate=0.01 -p batch_size=32

   # With git reference
   mlctl job run <family-name> --git-ref main

   # With specific instance type
   mlctl job run <family-name> --instance-type ml.m5.large
   ```

3. Job management:
   ```bash
   # Get job details
   mlctl job describe <job-ref>

   # Cancel running job
   mlctl job cancel <job-ref>

   # View results in browser
   mlctl job display <job-ref>
   ```

### Managing JupyterLab Instances

Manage JupyterLab instances with the `jupyter` command:

1. Start instance:
   ```bash
   # Default instance
   mlctl jupyter start

   # Custom instance
   mlctl jupyter start --instance-type ml.m5.large
   ```

2. Other commands:
   ```bash
   mlctl jupyter open      # Open in browser
   mlctl jupyter describe  # Check status
   mlctl jupyter stop     # Stop instance
   ```

### Managing Secrets

The `secret` command manages secrets:

```bash
mlctl secret list                                  # List all
mlctl secret add <name> <value> <description>      # Add new
mlctl secret update <name> -v <value> -d <desc>    # Update
mlctl secret remove <name>                         # Remove
```

### Available Instance Types
See the [`InstanceType` enum in the protos repo](https://github.com/gametimesf/protos/blob/97ed6aa4fde8c57208337aa7895480fbf826f913/gametime_protos/mlp/baseline/v1/service.proto)

## For Administrators

### Access Management Process

1. User requests access via Slack

2. Generate an access token:
   ```bash
   openssl rand -hex 16
   ```

3. Add user to appropriate environment:

For Production:

1. Get existing secret value from AWS Secrets Manager (us-west-1)
   - Secret name: baseline-system/production/USERS
   - Add new user to existing JSON:
   ```json
   {
     "<EMAIL>": {
       "permissions": ["user"],
       "password": "existing-token"
     },
     "<EMAIL>": {
       "permissions": ["user"],
       "password": "<generated-token>"
     }
   }
   ```

2. Update the secret with the modified JSON

3. Deploy changes:
   ```bash
   cd gametime-data/terraform/services/aws/production/baseline
   terraform init
   terraform plan
   terraform apply -var="force_new_deployment=true"
   ```

4. Provide token to user

For Staging:

1. Add user to terraform configuration
   - Navigate to gametime-data/terraform/services/aws/staging/baseline
   - Add email to users list in variables.tf or terraform.tfvars

2. Get existing secret value from AWS Secrets Manager (us-west-1)
   - Secret name: baseline-system/staging/USERS
   - Add new user to existing JSON:
   ```json
   {
     "<EMAIL>": {
       "permissions": ["user"],
       "password": "existing-token"
     },
     "<EMAIL>": {
       "permissions": ["user"],
       "password": "<generated-token>"
     }
   }
   ```

3. Update the secret with the modified JSON

4. Deploy changes:
   ```bash
   cd gametime-data/terraform/services/aws/staging/baseline
   terraform init
   terraform plan
   terraform apply -var="force_new_deployment=true"
   ```

5. Provide token to user

### Removing Access

1. Remove from USERS secret in AWS Secrets Manager
2. For staging, remove from terraform configuration
3. Redeploy service:
   ```bash
   cd gametime-data/terraform/services/aws/[staging|production]/baseline
   terraform apply -var="force_new_deployment=true"
   ```

## Maintenance

### Updates

Standard users:
   ```bash
   pipx upgrade mlctl
   ```

Development version:
   ```bash
   pipx upgrade --include-injected "mlctl@1.0.0-rc1"
   ```

### Uninstall

Standard users:
   ```bash
   pipx uninstall mlctl
   ```

Development version:
   ```bash
   pipx uninstall "mlctl@1.0.0-rc1"
   ```

## Troubleshooting

### Authentication Issues
- Verify token matches USERS secret
- Check environment URL
- Confirm email matches exactly
- Check config file:
  - Commands with --config flag use specified path
  - Commands without --config use `~/.mlctl/config.json`
- For development version, check suffix and config path combination

### Job Issues
- Verify git reference exists
- Check parameter formatting
- Confirm instance type is valid
- Verify network connectivity

### Jupyter Issues
- Only one instance allowed at a time
- Allow time for instance startup
- Check AWS permissions

## Environment Details

| Component         | Production                                                                                                                                | Staging                                                                                                                             |
|-------------------|-------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------|
| API Endpoint      | `baseline.production.gteng.co:443`                                                                                                        | `baseline.staging.gteng.co:443`                                                                                                     |
| Secret Name       | `baseline-system/production/USERS`                                                                                                        | `baseline-system/staging/USERS`                                                                                                     |
| Secret ARN        | `arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/production/USERS-14v8Ss`                                            | `arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/staging/USERS-Mu2FhG`                                         |
| Region            | `us-west-1`                                                                                                                               | `us-west-1`                                                                                                                         |
| Config Path       | `~/.mlctl/prod-config.json`                                                                                                               | `~/.mlctl/staging-config.json`                                                                                                      |
| Default Config    | `~/.mlctl/config.json` (used when no --config flag specified)                                                                             | `~/.mlctl/config.json` (used when no --config flag specified)                                                                       |
| User List         | [production/baseline/main.tf](https://github.com/gametimesf/gametime-data/blob/master/terraform/services/aws/production/baseline/main.tf) | [staging/baseline/main.tf](https://github.com/gametimesf/gametime-data/blob/master/terraform/services/aws/staging/baseline/main.tf) |
| Service Directory | `gametime-data/terraform/services/aws/production/baseline`                                                                                | `gametime-data/terraform/services/aws/staging/baseline`                                                                             |
| Default Instance  | `ml.t3.medium`                                                                                                                            | `ml.t3.medium`                                                                                                                      |
