from datetime import <PERSON><PERSON><PERSON>
from functools import partial
from tempfile import NamedTemporaryFile
from time import sleep
from urllib.request import urlretrieve

import click
import yaml
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceCancelRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceDescribeRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFetchOutputUrlRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceListRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceRunRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import JobStatus
from gametime_protos.mlp.baseline.v1.service_pb2 import JobsServiceFetchLogsRequest
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import JobsServiceStub

from mlctl.utils import DEFAULT_INSTANCE_TYPE
from mlctl.utils import echo_item
from mlctl.utils import echo_list
from mlctl.utils import friendly_timestamp
from mlctl.utils import INSTANCE_TYPE_ENUM_TO_STR
from mlctl.utils import INSTANCE_TYPE_STR_TO_ENUM
from mlctl.utils import pass_stub
from mlctl.utils import set_stub
from mlctl.utils import utcnow
from mlutils.grpc import proto_to_dict
import grpc


STATUS_MAP = {
    value: name.lower().replace("job_status_", "")
    for name, value in JobStatus.items()
    if value > 0
}
DEFAULT_WINDOW = timedelta(days=7)


def _map_item(item, full=False):
    mapped = {
        "Ref": item.ref,
        "Requested": friendly_timestamp(item.requested_at),
        "Requested By": item.run_by,
        "Status": STATUS_MAP[item.status],
    }
    if full:
        mapped["Instance Type"] = INSTANCE_TYPE_ENUM_TO_STR[item.instance_type]
        mapped["Git SHA"] = item.git_sha
        mapped["Status Reason"] = item.status_reason
        mapped["Started"] = (
            friendly_timestamp(item.started_at) if item.HasField("started_at") else None
        )
        mapped["Ended"] = (
            friendly_timestamp(item.ended_at) if item.HasField("ended_at") else None
        )
        mapped["Parameters"] = yaml.dump(dict(item.parameters))
        assets = proto_to_dict(item.asset_manifest)
        evaluation = proto_to_dict(item.evaluation_metrics)
        mapped["Assets"] = yaml.dump(assets) if assets else None
        mapped["Evaluation"] = yaml.dump(evaluation) if evaluation else None
    return mapped


@click.group()
@click.pass_context
def job(ctx):
    """
    Manage baseline jobs
    """
    set_stub(ctx, JobsServiceStub)


@job.command()
@click.argument("family")
@click.option("--git-ref", "-g", help="Git ref", type=str)
@click.option(
    "--parameter", "-p", help="Parameter (key=value)", type=str, multiple=True
)
@click.option(
    "--instance-type",
    "-i",
    help="Instance type",
    type=click.Choice(list(INSTANCE_TYPE_STR_TO_ENUM.keys()), case_sensitive=True),
    default=DEFAULT_INSTANCE_TYPE,
)
@pass_stub
def run(stub, family, git_ref, parameter, instance_type):
    """
    Run a job
    """
    request = JobsServiceRunRequest(
        family=family,
        instance_type=INSTANCE_TYPE_STR_TO_ENUM[instance_type],
        git_ref=git_ref,
    )
    for raw in parameter:
        k, v = raw.strip().split("=")
        request.parameters[k] = v
    response = stub.Run(request)
    click.echo(response.ref)


@job.command()
@click.argument("ref")
@pass_stub
def cancel(stub, ref):
    """
    Cancel a running job
    """
    request = JobsServiceCancelRequest(ref=ref)
    stub.Cancel(request)


@job.command()
@click.argument("ref")
@pass_stub
def describe(stub, ref):
    """
    Describe a job
    """
    request = JobsServiceDescribeRequest(ref=ref)
    response = stub.Describe(request)
    echo_item(response, partial(_map_item, full=True))


@job.command()
@click.argument("ref")
@pass_stub
def display(stub, ref):
    """
    Display a completed job in the browser
    """
    request = JobsServiceFetchOutputUrlRequest(ref=ref)
    response = stub.FetchOutputUrl(request)
    with NamedTemporaryFile(suffix=".ipynb") as temp:
        urlretrieve(response.url, temp.name)  # nosec
        click.launch(f"file:///{temp.name}")
        sleep(1)  # give it a chance to open before temp file is cleaned up


@job.command()
@click.argument("family")
@click.option(
    "--before",
    "-b",
    help="List jobs triggered before this timestamp",
    type=click.DateTime(),
)
@click.option(
    "--after",
    "-a",
    help="List jobs triggered after this timestamp",
    type=click.DateTime(),
)
@click.option(
    "--ascending", is_flag=True, default=False, help="Order by ascending timestamp"
)
@pass_stub
def list(stub, family, before, after, ascending):
    """
    List jobs
    """
    request = JobsServiceListRequest(family=family, ascending=ascending)
    if not before:
        before = utcnow()
    if not after:
        after = before - DEFAULT_WINDOW
    request.before.FromDatetime(before)
    request.after.FromDatetime(after)
    response = stub.List(request)
    echo_list(response.jobs, _map_item)


@job.command()
@click.argument("ref")
@click.option(
    "--tail",
    "-t",
    help="Number of lines to show from the end of the logs",
    type=click.INT,
    default=0,
)
@click.option(
    "--output",
    "-o",
    help="Output file path (default: stdout)",
    type=click.Path(),
    default=None,
)
@pass_stub
def logs(stub, ref, tail, output):
    """
    Fetch logs for a job

    If --tail is specified, only show the last N lines of logs.
    If --output is specified, write logs to a file instead of stdout.
    """
    request = JobsServiceFetchLogsRequest(ref=ref, tail=tail)
    try:
        response = stub.FetchLogs(request)
        if not response or not response.content:
            click.echo(
                f"No logs available yet for job {ref}. The job may still be running or hasn't started producing output."
            )
            return
    except grpc.RpcError as e:
        if e.code() == grpc.StatusCode.NOT_FOUND:
            click.echo(f"Job {ref} not found")
            return
        raise

    if output:
        with open(output, "w") as f:
            f.write(response.content)
    else:
        click.echo(response.content)
