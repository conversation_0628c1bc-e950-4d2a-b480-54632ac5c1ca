import click
import time

from gametime_protos.mlp.baseline.v1.service_pb2 import (
    JupyterServiceDescribeRequest,
    JupyterServiceFetchUrlRequest,
    JupyterServiceStartRequest,
    JupyterServiceStopRequest,
    JupyterStatus,
)
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import JupyterServiceStub

from mlctl.utils import DEFAULT_INSTANCE_TYPE
from mlctl.utils import echo_item
from mlctl.utils import INSTANCE_TYPE_ENUM_TO_STR
from mlctl.utils import INSTANCE_TYPE_STR_TO_ENUM
from mlctl.utils import pass_stub
from mlctl.utils import set_stub


STATUS_MAP = {
    value: name.lower().replace("jupyter_status_", "")
    for name, value in JupyterStatus.items()
    if value > 0
}


def _map_item(item):
    return {
        "Instance Type": INSTANCE_TYPE_ENUM_TO_STR[item.instance_type],
        "Status": STATUS_MAP[item.status],
    }


def poll_status(
    stub,
    *,
    success_status: int,
    success_message: str,
    failure_status: int,
    failure_message: str,
    poll_interval: int = 5,
):
    """
    Polls the Jupyter instance status until a terminal state is reached.

    :param stub: The gRPC stub to use for querying status.
    :param success_status: The status code indicating success.
    :param success_message: The message to show on success.
    :param failure_status: The status code indicating failure.
    :param failure_message: The error message if a failure is detected.
    :param poll_interval: Seconds to wait between polls.
    """
    while True:
        time.sleep(poll_interval)
        click.echo(".", nl=False)
        response = stub.Describe(JupyterServiceDescribeRequest())
        status_enum = response.status

        if status_enum == success_status:
            click.echo()
            click.echo(success_message)
            break
        elif status_enum == failure_status:
            click.echo()
            raise click.ClickException(failure_message)


@click.group()
@click.pass_context
def jupyter(ctx):
    """
    Manage baseline jupyter
    """
    set_stub(ctx, JupyterServiceStub)


@jupyter.command()
@click.option(
    "--instance-type",
    "-i",
    help="Instance type",
    type=click.Choice(list(INSTANCE_TYPE_STR_TO_ENUM.keys()), case_sensitive=True),
    default=DEFAULT_INSTANCE_TYPE,
)
@pass_stub
def start(stub, instance_type):
    """
    Start JupyterLab instance and wait until it is ready or has failed.
    """
    # Send the start request
    request = JupyterServiceStartRequest(
        instance_type=INSTANCE_TYPE_STR_TO_ENUM[instance_type]
    )
    stub.Start(request)

    click.echo("JupyterLab instance is starting", nl=False)
    # Poll until the instance is running (status 2) or failed (status 5)
    poll_status(
        stub,
        success_status=2,  # JUPYTER_STATUS_RUNNING
        success_message="JupyterLab instance is ready!",
        failure_status=5,  # JUPYTER_STATUS_FAILED
        failure_message="JupyterLab instance failed to start!",
    )


@jupyter.command()
@pass_stub
def stop(stub):
    """
    Stop JupyterLab instance and wait until it is stopped or has failed.
    """
    click.echo("JupyterLab instance is stopping", nl=False)
    request = JupyterServiceStopRequest()
    stub.Stop(request)

    # Poll until the instance is stopped (status 4) or failed (status 5)
    poll_status(
        stub,
        success_status=4,  # Assuming JUPYTER_STATUS_STOPPED
        success_message="JupyterLab instance is stopped!",
        failure_status=5,  # JUPYTER_STATUS_FAILED
        failure_message="JupyterLab instance failed to stop!",
    )


@jupyter.command()
@pass_stub
def describe(stub):
    """
    Describe JupyterLab instance
    """
    request = JupyterServiceDescribeRequest()
    response = stub.Describe(request)
    echo_item(response, _map_item)


@jupyter.command()
@pass_stub
def open(stub):
    """
    Open JupyterLab in browser
    """
    request = JupyterServiceFetchUrlRequest()
    response = stub.FetchUrl(request)
    click.launch(response.url)
