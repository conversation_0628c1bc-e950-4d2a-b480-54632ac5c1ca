import click
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceAddRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceListRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceRemoveRequest
from gametime_protos.mlp.baseline.v1.service_pb2 import SecretsServiceUpdateRequest
from gametime_protos.mlp.baseline.v1.service_pb2_grpc import SecretsServiceStub

from mlctl.utils import echo_list
from mlctl.utils import friendly_timestamp
from mlctl.utils import pass_stub
from mlctl.utils import set_stub


def _map_item(item):
    return {
        "Name": item.name,
        "Description": item.description,
        "Created": friendly_timestamp(item.created_at),
        "Updated": friendly_timestamp(item.updated_at),
    }


@click.group()
@click.pass_context
def secret(ctx):
    """
    Manage baseline secrets
    """
    set_stub(ctx, SecretsServiceStub)


@secret.command()
@click.argument("name")
@click.argument("value")
@click.argument("description")
@pass_stub
def add(stub, name, value, description):
    """
    Add a new secret
    """
    request = SecretsServiceAddRequest(name=name, description=description, value=value)
    stub.Add(request)


@secret.command()
@click.argument("name")
@click.option("--value", "-v", type=str, default="")
@click.option("--description", "-d", type=str, default="")
@pass_stub
def update(stub, name, value, description):
    """
    Update an existing secret
    """
    request = SecretsServiceUpdateRequest(
        name=name, value=value, description=description
    )
    stub.Update(request)


@secret.command()
@click.argument("name")
@pass_stub
def remove(stub, name):
    """
    Remove an existing secret
    """
    request = SecretsServiceRemoveRequest(name=name)
    stub.Remove(request)


@secret.command()
@pass_stub
def list(stub):
    """
    List all secrets
    """
    request = SecretsServiceListRequest()
    response = stub.List(request)
    echo_list(response.secrets, _map_item)
