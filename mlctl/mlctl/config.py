import json
from dataclasses import asdict
from dataclasses import dataclass
from pathlib import Path

DEFAULT_CONFIG_PATH = Path("~/.mlctl/config.json").expanduser()


class ConfigError(RuntimeError):
    """Exception class to raise if there is no valid config found"""


@dataclass
class Config:
    url: str
    email: str
    token: str

    @classmethod
    def read(cls, config_path=None):
        try:
            path = (
                Path(config_path).expanduser() if config_path else DEFAULT_CONFIG_PATH
            )
            with path.open("r") as f:
                raw = json.load(f)
                return cls(**raw)
        except (FileNotFoundError, json.JSONDecodeError, KeyError, TypeError) as e:
            raise ConfigError(f"could not find valid config at {path}") from e

    def write(self, config_path=None):
        path = Path(config_path).expanduser() if config_path else DEFAULT_CONFIG_PATH
        path.parent.mkdir(parents=True, exist_ok=True)
        path.touch(exist_ok=True)
        with path.open("w") as f:
            json.dump(asdict(self), f)
