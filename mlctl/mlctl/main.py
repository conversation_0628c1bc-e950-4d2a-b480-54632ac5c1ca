import click

from mlctl.commands.job import job
from mlctl.commands.jupyter import jupyter
from mlctl.commands.secret import secret
from mlctl.config import Config


@click.group()
@click.option("--config", help="Path to config file")
@click.pass_context
def cli(ctx, config):
    """Manage the Gametime ML Platform"""
    ctx.ensure_object(dict)
    ctx.obj["config_path"] = config


@cli.command()
@click.pass_context
def configure(ctx):
    """Configure mlctl"""
    url = click.prompt("URL", type=str)
    email = click.prompt("Email", type=str)
    token = click.prompt("Token", type=str)
    Config(url=url, email=email, token=token).write(ctx.obj.get("config_path"))


cli.add_command(job)
cli.add_command(jupyter)
cli.add_command(secret)
