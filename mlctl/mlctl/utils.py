from contextlib import contextmanager
from datetime import datetime
from datetime import timezone
from functools import wraps

import click
import grpc
from gametime_protos.mlp.baseline.v1.service_pb2 import InstanceType
from tabulate import tabulate

from mlctl.config import Config
from mlctl.config import ConfigError
from mlutils.grpc.auth import build_basic_auth
from mlutils.grpc.sync import HeaderAdderClientInterceptor


INSTANCE_TYPE_STR_TO_ENUM = {
    name.lower().replace("instance_type", "ml").replace("_", "."): value
    for name, value in InstanceType.items()
    if value > 0
}
INSTANCE_TYPE_ENUM_TO_STR = {v: k for k, v in INSTANCE_TYPE_STR_TO_ENUM.items()}
DEFAULT_INSTANCE_TYPE = list(INSTANCE_TYPE_STR_TO_ENUM.keys())[0]


@contextmanager
def make_stub(stub_class, config):
    credentials = build_basic_auth(config.email, config.token)
    interceptor = HeaderAdderClientInterceptor({"Authorization": credentials})
    credentials = grpc.ssl_channel_credentials()
    with grpc.secure_channel(config.url, credentials) as channel:
        intercept_channel = grpc.intercept_channel(channel, interceptor)
        yield stub_class(intercept_channel)


def set_stub(ctx, stub_class):
    try:
        config = Config.read(ctx.obj.get("config_path"))
        ctx.obj = ctx.with_resource(make_stub(stub_class, config))
    except ConfigError as e:
        cmd = ctx.command_path.split()[0]
        raise click.ClickException(
            f"Valid config not found. Please run `{cmd} configure`\n{str(e)}"
        )


def pass_stub(f):
    @wraps(f)
    @click.pass_obj
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except grpc.RpcError as e:
            raise click.ClickException(e.details())

    return wrapper


def friendly_timestamp(timestamp):
    return timestamp.ToDatetime().isoformat(timespec="seconds").replace("T", " ")


def echo_list(items, mapper):
    if len(items) > 0:
        rows = [mapper(item) for item in items]
        output = tabulate(rows, headers="keys", tablefmt="simple")
        click.echo(output)


def echo_item(item, mapper):
    rows = [(f"{k}:", v) for k, v in mapper(item).items()]
    output = tabulate(rows, tablefmt="plain")
    click.echo(output)


def utcnow():
    return datetime.now(timezone.utc)
