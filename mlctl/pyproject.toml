[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[dependency-groups]
dev = [
  "pytest>=8.3.4"
]

[project]
authors = [{name = "Gametime ML"}]
dependencies = [
  "click>=8.1.8",
  "gametime_protos>=1.0.0",
  "mlutils[grpc]>=1.8.0",
  "pyyaml>=6.0.2",
  "tabulate>=0.9.0"
]
description = "ML Platform CLI"
name = "mlctl"
requires-python = ">=3.10,<4.0"
version = "1.4.0"

[project.optional-dependencies]
test = [
  "pytest>=8.3.5"
]

[project.scripts]
mlctl = "mlctl.main:cli"

[tool.hatch.metadata]
allow-direct-references = true

[tool.semantic_release]
build_command = ""
changelog_file = "CHANGELOG.md"
commit_parser = "conventional"
tag_format = "mlctl-v{version}"
upload_to_release = true
upload_to_repository = false
version_toml = ["pyproject.toml:project.version"]

[tool.semantic_release.branches]
main.match = "main"

[[tool.uv.index]]
name = "codeartifact"
url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/"

[tool.uv.sources]
gametime_protos = {index = "codeartifact"}
mlutils = {index = "codeartifact"}
