import time
import pytest
import click
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from mlctl.commands import jupyter as jupyter_cmd


class FakeResponse:
    """A fake response to simulate a gRPC response with a status."""

    def __init__(self, status):
        self.status = status


class FakeStubPoll:
    """
    A fake stub for testing poll_status.
    Returns a sequence of statuses (e.g. first call non-terminal, then success or failure).
    """

    def __init__(self, statuses):
        self.statuses = statuses
        self.call_count = 0

    def Describe(self, request):
        # Return the next status from the list.
        response = FakeResponse(self.statuses[self.call_count])
        self.call_count += 1
        return response


class FakeJupyterStub:
    """
    A fake stub for testing the jupyter commands.
    It records whether Start, Stop, Describe, and FetchUrl have been called,
    and can simulate multiple Describe responses for polling.
    """

    def __init__(self, describe_statuses=None, fetch_url="http://fake-url"):
        self.start_called = False
        self.stop_called = False
        self.describe_called = False
        self.fetch_url_called = False
        self.describe_statuses = describe_statuses or []
        self.describe_call_count = 0
        self.fetch_url_value = fetch_url

    def Start(self, request):
        self.start_called = True
        self.start_request = request

    def Stop(self, request):
        self.stop_called = True
        self.stop_request = request

    def Describe(self, request):
        self.describe_called = True
        if self.describe_statuses:
            status = self.describe_statuses[self.describe_call_count]
            self.describe_call_count += 1
            return FakeResponse(status)
        # Default to a successful status if none specified
        return FakeResponse(2)

    def FetchUrl(self, request):
        self.fetch_url_called = True

        # Return a fake object with a 'url' attribute.
        class FakeFetchResponse:
            pass

        fake = FakeFetchResponse()
        fake.url = self.fetch_url_value
        return fake


@pytest.fixture(autouse=True)
def fast_sleep(monkeypatch):
    """Override time.sleep so that tests run instantly."""
    monkeypatch.setattr(time, "sleep", lambda s: None)


@pytest.fixture
def fake_ctx(monkeypatch):
    """
    Override the set_stub function so that our FakeJupyterStub
    is injected into the Click context.
    """
    fake_stub = FakeJupyterStub()

    def fake_set_stub(ctx, stub_class):
        # Instead of reading config and creating a real stub,
        # simply assign our fake stub.
        ctx.obj = fake_stub

    monkeypatch.setattr(jupyter_cmd, "set_stub", fake_set_stub)
    return fake_stub


def test_poll_status_success(capsys):
    """
    Test that poll_status eventually prints the success message.
    We simulate a stub whose Describe method first returns a non-success status,
    then a success status.
    """
    statuses = [0, 2]  # 0 (non-terminal), then 2 (success)
    fake_stub = FakeStubPoll(statuses)
    success_status = 2
    failure_status = 5
    success_message = "JupyterLab instance is ready!"
    failure_message = "JupyterLab instance failed to start!"

    jupyter_cmd.poll_status(
        fake_stub,
        success_status=success_status,
        success_message=success_message,
        failure_status=failure_status,
        failure_message=failure_message,
        poll_interval=0,  # no delay since sleep is patched
    )
    captured = capsys.readouterr().out
    assert success_message in captured


def test_poll_status_failure():
    """
    Test that poll_status raises a ClickException when a failure status is encountered.
    """
    statuses = [0, 5]  # 0 (non-terminal), then 5 (failure)
    fake_stub = FakeStubPoll(statuses)
    success_status = 2
    failure_status = 5
    success_message = "JupyterLab instance is ready!"
    failure_message = "JupyterLab instance failed to start!"

    with pytest.raises(click.ClickException) as excinfo:
        jupyter_cmd.poll_status(
            fake_stub,
            success_status=success_status,
            success_message=success_message,
            failure_status=failure_status,
            failure_message=failure_message,
            poll_interval=0,
        )
    assert failure_message in str(excinfo.value)


def test_start_command(monkeypatch, fake_ctx):
    """
    Test the `start` command.
    We simulate a scenario where the Start request is sent and poll_status eventually
    returns a success (status 2).
    """
    # Simulate that the first call returns a non-success and the second a success.
    fake_ctx.describe_statuses = [0, 2]
    runner = CliRunner()
    result = runner.invoke(jupyter_cmd.jupyter, ["start"])

    assert fake_ctx.start_called, "Expected Start() to be called on the stub."
    assert "JupyterLab instance is ready!" in result.output


def test_start_command_with_instance_type(monkeypatch, fake_ctx):
    """
    Test the `start` command when a specific instance type is provided.
    The test ensures that the Start() method receives the correct instance type.
    """
    # Simulate poll_status: first non-terminal, then success.
    fake_ctx.describe_statuses = [0, 2]
    runner = CliRunner()
    result = runner.invoke(jupyter_cmd.jupyter, "start --instance-type ml.m5.large")

    assert fake_ctx.start_called, "Expected Start() to be called on the stub."

    from mlctl.utils import INSTANCE_TYPE_STR_TO_ENUM

    expected_instance_type = INSTANCE_TYPE_STR_TO_ENUM["ml.m5.large"]
    assert fake_ctx.start_request.instance_type == expected_instance_type, (
        f"Expected instance_type {expected_instance_type}, got {fake_ctx.start_request.instance_type}"
    )
    assert "JupyterLab instance is ready!" in result.output


def test_stop_command(monkeypatch, fake_ctx):
    """
    Test the `stop` command.
    We simulate a scenario where the Stop request is sent and poll_status eventually
    returns a stopped status (4).
    """
    fake_ctx.describe_statuses = [0, 4]  # 4 is used as the success status for stop
    runner = CliRunner()
    result = runner.invoke(jupyter_cmd.jupyter, ["stop"])

    assert fake_ctx.stop_called, "Expected Stop() to be called on the stub."
    assert "JupyterLab instance is stopped!" in result.output


def test_describe_command(monkeypatch):
    """
    Test the `describe` command.
    We override the stub’s Describe method to return a fake response with
    attributes needed by _map_item, and then assert that the output contains expected fields.
    """
    fake_stub = FakeJupyterStub()
    fake_response = type("FakeResponseObj", (), {})()
    fake_response.instance_type = 1
    fake_response.status = 2
    fake_stub.Describe = lambda req: fake_response

    def fake_set_stub(ctx, stub_class):
        ctx.obj = fake_stub

    monkeypatch.setattr(jupyter_cmd, "set_stub", fake_set_stub)

    runner = CliRunner()
    result = runner.invoke(jupyter_cmd.jupyter, ["describe"])
    assert "Instance Type" in result.output
    assert "Status" in result.output


def test_open_command(monkeypatch, fake_ctx):
    """
    Test the `open` command.
    We simulate a fake FetchUrl response and patch click.launch to capture its argument.
    """

    # Ensure our fake stub returns a fake URL.
    def fake_fetch_url(req):
        fake_ctx.fetch_url_called = True

        class FakeFetch:
            pass

        fake = FakeFetch()
        fake.url = "http://test-url"
        return fake

    fake_ctx.FetchUrl = fake_fetch_url

    # Patch click.launch so that we capture the URL instead of actually launching a browser.
    launched_urls = []

    def fake_launch(url):
        launched_urls.append(url)

    monkeypatch.setattr(click, "launch", fake_launch)

    runner = CliRunner()
    runner.invoke(jupyter_cmd.jupyter, ["open"])
    assert launched_urls, "Expected click.launch to be called."
    assert "http://test-url" in launched_urls[0]
