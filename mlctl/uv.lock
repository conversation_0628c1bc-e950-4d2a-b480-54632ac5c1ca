version = 1
requires-python = ">=3.10, <4.0"
resolution-markers = [
    "python_full_version >= '3.13'",
    "python_full_version < '3.13'",
]

[[package]]
name = "cachetools"
version = "5.5.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cachetools/5.5.2/cachetools-5.5.2.tar.gz", hash = "sha256:1a661caa9175d26759571b2e19580f9d6393969e5dfca11fdb1f947a23e640d4" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/cachetools/5.5.2/cachetools-5.5.2-py3-none-any.whl", hash = "sha256:d26a22bcc62eb95c3beabd9f1ee5e820d3d2704fe2967cbe350e20c8ffcd3f0a" },
]

[[package]]
name = "certifi"
version = "2025.1.31"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/certifi/2025.1.31/certifi-2025.1.31.tar.gz", hash = "sha256:3d5da6925056f6f18f119200434a4780a94263f10d1c21d032a6f6b2baa20651" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/certifi/2025.1.31/certifi-2025.1.31-py3-none-any.whl", hash = "sha256:ca78db4565a652026a4db2bcdf68f2fb589ea80d0be70e03929ed730746b84fe" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1.tar.gz", hash = "sha256:44251f18cd68a75b56585dd00dae26183e102cd5e0f9f1466e6df5da2ed64ea3" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-macosx_10_9_universal2.whl", hash = "sha256:91b36a978b5ae0ee86c394f5a54d6ef44db1de0815eb43de826d41d21e4af3de" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:7461baadb4dc00fd9e0acbe254e3d7d2112e7f92ced2adc96e54ef6501c5f176" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:e218488cd232553829be0664c2292d3af2eeeb94b32bea483cf79ac6a694e037" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:80ed5e856eb7f30115aaf94e4a08114ccc8813e6ed1b5efa74f9f82e8509858f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:b010a7a4fd316c3c484d482922d13044979e78d1861f0e0650423144c616a46a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:4532bff1b8421fd0a320463030c7520f56a79c9024a4e88f01c537316019005a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-musllinux_1_2_aarch64.whl", hash = "sha256:d973f03c0cb71c5ed99037b870f2be986c3c05e63622c017ea9816881d2dd247" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-musllinux_1_2_i686.whl", hash = "sha256:3a3bd0dcd373514dcec91c411ddb9632c0d7d92aed7093b8c3bbb6d69ca74408" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-musllinux_1_2_ppc64le.whl", hash = "sha256:d9c3cdf5390dcd29aa8056d13e8e99526cda0305acc038b96b30352aff5ff2bb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-musllinux_1_2_s390x.whl", hash = "sha256:2bdfe3ac2e1bbe5b59a1a63721eb3b95fc9b6817ae4a46debbb4e11f6232428d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-musllinux_1_2_x86_64.whl", hash = "sha256:eab677309cdb30d047996b36d34caeda1dc91149e4fdca0b1a039b3f79d9a807" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-win32.whl", hash = "sha256:c0429126cf75e16c4f0ad00ee0eae4242dc652290f940152ca8c75c3a4b6ee8f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp310-cp310-win_amd64.whl", hash = "sha256:9f0b8b1c6d84c8034a44893aba5e767bf9c7a211e313a9605d9c617d7083829f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-macosx_10_9_universal2.whl", hash = "sha256:8bfa33f4f2672964266e940dd22a195989ba31669bd84629f05fab3ef4e2d125" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:28bf57629c75e810b6ae989f03c0828d64d6b26a5e205535585f96093e405ed1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:f08ff5e948271dc7e18a35641d2f11a4cd8dfd5634f55228b691e62b37125eb3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:234ac59ea147c59ee4da87a0c0f098e9c8d169f4dc2a159ef720f1a61bbe27cd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:fd4ec41f914fa74ad1b8304bbc634b3de73d2a0889bd32076342a573e0779e00" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:eea6ee1db730b3483adf394ea72f808b6e18cf3cb6454b4d86e04fa8c4327a12" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:c96836c97b1238e9c9e3fe90844c947d5afbf4f4c92762679acfe19927d81d77" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:4d86f7aff21ee58f26dcf5ae81a9addbd914115cdebcbb2217e4f0ed8982e146" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_ppc64le.whl", hash = "sha256:09b5e6733cbd160dcc09589227187e242a30a49ca5cefa5a7edd3f9d19ed53fd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_s390x.whl", hash = "sha256:5777ee0881f9499ed0f71cc82cf873d9a0ca8af166dfa0af8ec4e675b7df48e6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:237bdbe6159cff53b4f24f397d43c6336c6b0b42affbe857970cefbb620911c8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-win32.whl", hash = "sha256:8417cb1f36cc0bc7eaba8ccb0e04d55f0ee52df06df3ad55259b9a323555fc8b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp311-cp311-win_amd64.whl", hash = "sha256:d7f50a1f8c450f3925cb367d011448c39239bb3eb4117c36a6d354794de4ce76" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-macosx_10_13_universal2.whl", hash = "sha256:73d94b58ec7fecbc7366247d3b0b10a21681004153238750bb67bd9012414545" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:dad3e487649f498dd991eeb901125411559b22e8d7ab25d3aeb1af367df5efd7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:c30197aa96e8eed02200a83fba2657b4c3acd0f0aa4bdc9f6c1af8e8962e0757" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:2369eea1ee4a7610a860d88f268eb39b95cb588acd7235e02fd5a5601773d4fa" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:bc2722592d8998c870fa4e290c2eec2c1569b87fe58618e67d38b4665dfa680d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:ffc9202a29ab3920fa812879e95a9e78b2465fd10be7fcbd042899695d75e616" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:804a4d582ba6e5b747c625bf1255e6b1507465494a40a2130978bda7b932c90b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:0f55e69f030f7163dffe9fd0752b32f070566451afe180f99dbeeb81f511ad8d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_ppc64le.whl", hash = "sha256:c4c3e6da02df6fa1410a7680bd3f63d4f710232d3139089536310d027950696a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_s390x.whl", hash = "sha256:5df196eb874dae23dcfb968c83d4f8fdccb333330fe1fc278ac5ceeb101003a9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:e358e64305fe12299a08e08978f51fc21fac060dcfcddd95453eabe5b93ed0e1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-win32.whl", hash = "sha256:9b23ca7ef998bc739bf6ffc077c2116917eabcc901f88da1b9856b210ef63f35" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp312-cp312-win_amd64.whl", hash = "sha256:6ff8a4a60c227ad87030d76e99cd1698345d4491638dfa6673027c48b3cd395f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-macosx_10_13_universal2.whl", hash = "sha256:aabfa34badd18f1da5ec1bc2715cadc8dca465868a4e73a0173466b688f29dda" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:22e14b5d70560b8dd51ec22863f370d1e595ac3d024cb8ad7d308b4cd95f8313" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:8436c508b408b82d87dc5f62496973a1805cd46727c34440b0d29d8a2f50a6c9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:2d074908e1aecee37a7635990b2c6d504cd4766c7bc9fc86d63f9c09af3fa11b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:955f8851919303c92343d2f66165294848d57e9bba6cf6e3625485a70a038d11" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:44ecbf16649486d4aebafeaa7ec4c9fed8b88101f4dd612dcaf65d5e815f837f" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:0924e81d3d5e70f8126529951dac65c1010cdf117bb75eb02dd12339b57749dd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:2967f74ad52c3b98de4c3b32e1a44e32975e008a9cd2a8cc8966d6a5218c5cb2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:c75cb2a3e389853835e84a2d8fb2b81a10645b503eca9bcb98df6b5a43eb8886" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:09b26ae6b1abf0d27570633b2b078a2a20419c99d66fb2823173d73f188ce601" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:fa88b843d6e211393a37219e6a1c1df99d35e8fd90446f1118f4216e307e48cd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-win32.whl", hash = "sha256:eb8178fe3dba6450a3e024e95ac49ed3400e506fd4e9e5c32d30adda88cbd407" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-cp313-cp313-win_amd64.whl", hash = "sha256:b1ac5992a838106edb89654e0aebfc24f5848ae2547d22c2c3f66454daa11971" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/charset-normalizer/3.4.1/charset_normalizer-3.4.1-py3-none-any.whl", hash = "sha256:d98b1668f06378c6dbefec3b92299716b931cd4e6061f3c875a71ced1780ab85" },
]

[[package]]
name = "click"
version = "8.1.8"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/click/8.1.8/click-8.1.8.tar.gz", hash = "sha256:ed53c9d8990d83c2a27deae68e4ee337473f6330c040a31d4225c9574d16096a" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/click/8.1.8/click-8.1.8-py3-none-any.whl", hash = "sha256:63c132bbbed01578a06712a2d1f497bb62d9c1c0d329b7903a866228027263b2" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/colorama/0.4.6/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/colorama/0.4.6/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6" },
]

[[package]]
name = "exceptiongroup"
version = "1.2.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/exceptiongroup/1.2.2/exceptiongroup-1.2.2.tar.gz", hash = "sha256:47c2edf7c6738fafb49fd34290706d1a1a2f4d1c6df275526b62cbb4aa5393cc" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/exceptiongroup/1.2.2/exceptiongroup-1.2.2-py3-none-any.whl", hash = "sha256:3111b9d131c238bec2f8f516e123e14ba243563fb135d3fe885990585aa7795b" },
]

[[package]]
name = "gametime-protos"
version = "0.7.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "google-api-core" },
    { name = "grpcio" },
    { name = "protobuf" },
    { name = "types-protobuf" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/gametime-protos/0.7.0/gametime_protos-0.7.0.tar.gz", hash = "sha256:a98a7747ec7f09c5ff49d322b971c6d50003440c49d45ce2d9e2fcde8abe1f2a" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/gametime-protos/0.7.0/gametime_protos-0.7.0-py3-none-any.whl", hash = "sha256:260fad1b68874c3ae73da5c0a2ea748c2962da1b593a81b9a7b2e45f8e0070fd" },
]

[[package]]
name = "google-api-core"
version = "2.24.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "google-auth" },
    { name = "googleapis-common-protos" },
    { name = "proto-plus" },
    { name = "protobuf" },
    { name = "requests" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/google-api-core/2.24.2/google_api_core-2.24.2.tar.gz", hash = "sha256:81718493daf06d96d6bc76a91c23874dbf2fac0adbbf542831b805ee6e974696" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/google-api-core/2.24.2/google_api_core-2.24.2-py3-none-any.whl", hash = "sha256:810a63ac95f3c441b7c0e43d344e372887f62ce9071ba972eacf32672e072de9" },
]

[[package]]
name = "google-auth"
version = "2.38.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "cachetools" },
    { name = "pyasn1-modules" },
    { name = "rsa" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/google-auth/2.38.0/google_auth-2.38.0.tar.gz", hash = "sha256:8285113607d3b80a3f1543b75962447ba8a09fe85783432a784fdeef6ac094c4" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/google-auth/2.38.0/google_auth-2.38.0-py2.py3-none-any.whl", hash = "sha256:e7dae6694313f434a2727bf2906f27ad259bae090d7aa896590d86feec3d9d4a" },
]

[[package]]
name = "googleapis-common-protos"
version = "1.70.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "protobuf" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/googleapis-common-protos/1.70.0/googleapis_common_protos-1.70.0.tar.gz", hash = "sha256:0e1b44e0ea153e6594f9f394fef15193a68aaaea2d843f83e2742717ca753257" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/googleapis-common-protos/1.70.0/googleapis_common_protos-1.70.0-py3-none-any.whl", hash = "sha256:b8bfcca8c25a2bb253e0e0b0adaf8c00773e5e6af6fd92397576680b807e0fd8" },
]

[[package]]
name = "grpcio"
version = "1.71.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0.tar.gz", hash = "sha256:2b85f7820475ad3edec209d3d89a7909ada16caab05d3f2e08a7e8ae3200a55c" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-linux_armv7l.whl", hash = "sha256:c200cb6f2393468142eb50ab19613229dcc7829b5ccee8b658a36005f6669fdd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-macosx_12_0_universal2.whl", hash = "sha256:b2266862c5ad664a380fbbcdbdb8289d71464c42a8c29053820ee78ba0119e5d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-manylinux_2_17_aarch64.whl", hash = "sha256:0ab8b2864396663a5b0b0d6d79495657ae85fa37dcb6498a2669d067c65c11ea" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:c30f393f9d5ff00a71bb56de4aa75b8fe91b161aeb61d39528db6b768d7eac69" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:f250ff44843d9a0615e350c77f890082102a0318d66a99540f54769c8766ab73" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:e6d8de076528f7c43a2f576bc311799f89d795aa6c9b637377cc2b1616473804" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-musllinux_1_1_i686.whl", hash = "sha256:9b91879d6da1605811ebc60d21ab6a7e4bae6c35f6b63a061d61eb818c8168f6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:f71574afdf944e6652203cd1badcda195b2a27d9c83e6d88dc1ce3cfb73b31a5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-win32.whl", hash = "sha256:8997d6785e93308f277884ee6899ba63baafa0dfb4729748200fcc537858a509" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp310-cp310-win_amd64.whl", hash = "sha256:7d6ac9481d9d0d129224f6d5934d5832c4b1cddb96b59e7eba8416868909786a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-linux_armv7l.whl", hash = "sha256:d6aa986318c36508dc1d5001a3ff169a15b99b9f96ef5e98e13522c506b37eef" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-macosx_10_14_universal2.whl", hash = "sha256:d2c170247315f2d7e5798a22358e982ad6eeb68fa20cf7a820bb74c11f0736e7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-manylinux_2_17_aarch64.whl", hash = "sha256:e6f83a583ed0a5b08c5bc7a3fe860bb3c2eac1f03f1f63e0bc2091325605d2b7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:4be74ddeeb92cc87190e0e376dbc8fc7736dbb6d3d454f2fa1f5be1dee26b9d7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4dd0dfbe4d5eb1fcfec9490ca13f82b089a309dc3678e2edabc144051270a66e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:a2242d6950dc892afdf9e951ed7ff89473aaf744b7d5727ad56bdaace363722b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-musllinux_1_1_i686.whl", hash = "sha256:0fa05ee31a20456b13ae49ad2e5d585265f71dd19fbd9ef983c28f926d45d0a7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:3d081e859fb1ebe176de33fc3adb26c7d46b8812f906042705346b314bde32c3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-win32.whl", hash = "sha256:d6de81c9c00c8a23047136b11794b3584cdc1460ed7cbc10eada50614baa1444" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp311-cp311-win_amd64.whl", hash = "sha256:24e867651fc67717b6f896d5f0cac0ec863a8b5fb7d6441c2ab428f52c651c6b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-linux_armv7l.whl", hash = "sha256:0ff35c8d807c1c7531d3002be03221ff9ae15712b53ab46e2a0b4bb271f38537" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-macosx_10_14_universal2.whl", hash = "sha256:b78a99cd1ece4be92ab7c07765a0b038194ded2e0a26fd654591ee136088d8d7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-manylinux_2_17_aarch64.whl", hash = "sha256:dc1a1231ed23caac1de9f943d031f1bc38d0f69d2a3b243ea0d664fc1fbd7fec" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:e6beeea5566092c5e3c4896c6d1d307fb46b1d4bdf3e70c8340b190a69198594" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:d5170929109450a2c031cfe87d6716f2fae39695ad5335d9106ae88cc32dc84c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:5b08d03ace7aca7b2fadd4baf291139b4a5f058805a8327bfe9aece7253b6d67" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-musllinux_1_1_i686.whl", hash = "sha256:f903017db76bf9cc2b2d8bdd37bf04b505bbccad6be8a81e1542206875d0e9db" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:469f42a0b410883185eab4689060a20488a1a0a00f8bbb3cbc1061197b4c5a79" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-win32.whl", hash = "sha256:ad9f30838550695b5eb302add33f21f7301b882937460dd24f24b3cc5a95067a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp312-cp312-win_amd64.whl", hash = "sha256:652350609332de6dac4ece254e5d7e1ff834e203d6afb769601f286886f6f3a8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-linux_armv7l.whl", hash = "sha256:cebc1b34ba40a312ab480ccdb396ff3c529377a2fce72c45a741f7215bfe8379" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-macosx_10_14_universal2.whl", hash = "sha256:85da336e3649a3d2171e82f696b5cad2c6231fdd5bad52616476235681bee5b3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-manylinux_2_17_aarch64.whl", hash = "sha256:f9a412f55bb6e8f3bb000e020dbc1e709627dcb3a56f6431fa7076b4c1aab0db" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:47be9584729534660416f6d2a3108aaeac1122f6b5bdbf9fd823e11fe6fbaa29" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:7c9c80ac6091c916db81131d50926a93ab162a7e97e4428ffc186b6e80d6dda4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:789d5e2a3a15419374b7b45cd680b1e83bbc1e52b9086e49308e2c0b5bbae6e3" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-musllinux_1_1_i686.whl", hash = "sha256:1be857615e26a86d7363e8a163fade914595c81fec962b3d514a4b1e8760467b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:a76d39b5fafd79ed604c4be0a869ec3581a172a707e2a8d7a4858cb05a5a7637" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-win32.whl", hash = "sha256:74258dce215cb1995083daa17b379a1a5a87d275387b7ffe137f1d5131e2cfbb" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio/1.71.0/grpcio-1.71.0-cp313-cp313-win_amd64.whl", hash = "sha256:22c3bc8d488c039a199f7a003a38cb7635db6656fa96437a8accde8322ce2366" },
]

[[package]]
name = "grpcio-health-checking"
version = "1.62.3"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "grpcio" },
    { name = "protobuf" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio-health-checking/1.62.3/grpcio-health-checking-1.62.3.tar.gz", hash = "sha256:5074ba0ce8f0dcfe328408ec5c7551b2a835720ffd9b69dade7fa3e0dc1c7a93" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/grpcio-health-checking/1.62.3/grpcio_health_checking-1.62.3-py3-none-any.whl", hash = "sha256:f29da7dd144d73b4465fe48f011a91453e9ff6c8af0d449254cf80021cab3e0d" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/idna/3.10/idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/idna/3.10/idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3" },
]

[[package]]
name = "iniconfig"
version = "2.0.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/iniconfig/2.0.0/iniconfig-2.0.0.tar.gz", hash = "sha256:2d91e135bf72d31a410b17c16da610a82cb55f6b0477d1a902134b24a455b8b3" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/iniconfig/2.0.0/iniconfig-2.0.0-py3-none-any.whl", hash = "sha256:b6a85871a79d2e3b22d2d1b94ac2824226a63c6b741c88f7ae975f18b6778374" },
]

[[package]]
name = "mlctl"
version = "1.3.0"
source = { editable = "." }
dependencies = [
    { name = "click" },
    { name = "gametime-protos" },
    { name = "mlutils", extra = ["grpc"] },
    { name = "pyyaml" },
    { name = "tabulate" },
]

[package.optional-dependencies]
test = [
    { name = "pytest" },
]

[package.dev-dependencies]
dev = [
    { name = "pytest" },
]

[package.metadata]
requires-dist = [
    { name = "click", specifier = ">=8.1.8" },
    { name = "gametime-protos", specifier = "==0.7.0", index = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" },
    { name = "mlutils", extras = ["grpc"], specifier = ">=1.0.0", index = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" },
    { name = "pytest", marker = "extra == 'test'", specifier = ">=8.3.5" },
    { name = "pyyaml", specifier = ">=6.0.2" },
    { name = "tabulate", specifier = ">=0.9.0" },
]

[package.metadata.requires-dev]
dev = [{ name = "pytest", specifier = ">=8.3.4" }]

[[package]]
name = "mlutils"
version = "1.0.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/mlutils/1.0.0/mlutils-1.0.0.tar.gz", hash = "sha256:baad3cb15f591a4a77e70d4b7485849d166b3a2f56709d198ce27487063cd6d5" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/mlutils/1.0.0/mlutils-1.0.0-py3-none-any.whl", hash = "sha256:248c75fb82d95f73ab43113d66516a5576dc7f6b796b5bf6d81c7b941156bac7" },
]

[package.optional-dependencies]
grpc = [
    { name = "grpcio" },
    { name = "grpcio-health-checking" },
]

[[package]]
name = "packaging"
version = "24.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/packaging/24.2/packaging-24.2.tar.gz", hash = "sha256:c228a6dc5e932d346bc5739379109d49e8853dd8223571c7c5b55260edc0b97f" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/packaging/24.2/packaging-24.2-py3-none-any.whl", hash = "sha256:09abb1bccd265c01f4a3aa3f7a7db064b36514d2cba19a2f694fe6150451a759" },
]

[[package]]
name = "pluggy"
version = "1.5.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pluggy/1.5.0/pluggy-1.5.0.tar.gz", hash = "sha256:2cffa88e94fdc978c4c574f15f9e59b7f4201d439195c3715ca9e2486f1d0cf1" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pluggy/1.5.0/pluggy-1.5.0-py3-none-any.whl", hash = "sha256:44e1ad92c8ca002de6377e165f3e0f1be63266ab4d554740532335b9d75ea669" },
]

[[package]]
name = "proto-plus"
version = "1.26.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "protobuf" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/proto-plus/1.26.1/proto_plus-1.26.1.tar.gz", hash = "sha256:21a515a4c4c0088a773899e23c7bbade3d18f9c66c73edd4c7ee3816bc96a012" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/proto-plus/1.26.1/proto_plus-1.26.1-py3-none-any.whl", hash = "sha256:13285478c2dcf2abb829db158e1047e2f1e8d63a077d94263c2b88b043c75a66" },
]

[[package]]
name = "protobuf"
version = "6.30.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/6.30.2/protobuf-6.30.2.tar.gz", hash = "sha256:35c859ae076d8c56054c25b59e5e59638d86545ed6e2b6efac6be0b6ea3ba048" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/6.30.2/protobuf-6.30.2-cp310-abi3-win32.whl", hash = "sha256:b12ef7df7b9329886e66404bef5e9ce6a26b54069d7f7436a0853ccdeb91c103" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/6.30.2/protobuf-6.30.2-cp310-abi3-win_amd64.whl", hash = "sha256:7653c99774f73fe6b9301b87da52af0e69783a2e371e8b599b3e9cb4da4b12b9" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/6.30.2/protobuf-6.30.2-cp39-abi3-macosx_10_9_universal2.whl", hash = "sha256:0eb523c550a66a09a0c20f86dd554afbf4d32b02af34ae53d93268c1f73bc65b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/6.30.2/protobuf-6.30.2-cp39-abi3-manylinux2014_aarch64.whl", hash = "sha256:50f32cc9fd9cb09c783ebc275611b4f19dfdfb68d1ee55d2f0c7fa040df96815" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/6.30.2/protobuf-6.30.2-cp39-abi3-manylinux2014_x86_64.whl", hash = "sha256:4f6c687ae8efae6cf6093389a596548214467778146b7245e886f35e1485315d" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/protobuf/6.30.2/protobuf-6.30.2-py3-none-any.whl", hash = "sha256:ae86b030e69a98e08c77beab574cbcb9fff6d031d57209f574a5aea1445f4b51" },
]

[[package]]
name = "pyasn1"
version = "0.6.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyasn1/0.6.1/pyasn1-0.6.1.tar.gz", hash = "sha256:6f580d2bdd84365380830acf45550f2511469f673cb4a5ae3857a3170128b034" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyasn1/0.6.1/pyasn1-0.6.1-py3-none-any.whl", hash = "sha256:0d632f46f2ba09143da3a8afe9e33fb6f92fa2320ab7e886e2d0f7672af84629" },
]

[[package]]
name = "pyasn1-modules"
version = "0.4.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "pyasn1" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyasn1-modules/0.4.1/pyasn1_modules-0.4.1.tar.gz", hash = "sha256:c28e2dbf9c06ad61c71a075c7e0f9fd0f1b0bb2d2ad4377f240d33ac2ab60a7c" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyasn1-modules/0.4.1/pyasn1_modules-0.4.1-py3-none-any.whl", hash = "sha256:49bfa96b45a292b711e986f222502c1c9a5e1f4e568fc30e2574a6c7d07838fd" },
]

[[package]]
name = "pytest"
version = "8.3.5"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "exceptiongroup", marker = "python_full_version < '3.11'" },
    { name = "iniconfig" },
    { name = "packaging" },
    { name = "pluggy" },
    { name = "tomli", marker = "python_full_version < '3.11'" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pytest/8.3.5/pytest-8.3.5.tar.gz", hash = "sha256:f4efe70cc14e511565ac476b57c279e12a855b11f48f212af1080ef2263d3845" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pytest/8.3.5/pytest-8.3.5-py3-none-any.whl", hash = "sha256:c69214aa47deac29fad6c2a4f590b9c4a9fdb16a403176fe154b79c0b4d4d820" },
]

[[package]]
name = "pyyaml"
version = "6.0.2"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/pyyaml-6.0.2.tar.gz", hash = "sha256:d584d9ec91ad65861cc08d42e834324ef890a082e591037abe114850ff7bbc3e" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-macosx_10_9_x86_64.whl", hash = "sha256:0a9a2848a5b7feac301353437eb7d5957887edbf81d56e903999a75a3d743086" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-macosx_11_0_arm64.whl", hash = "sha256:29717114e51c84ddfba879543fb232a6ed60086602313ca38cce623c1d62cfbf" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:8824b5a04a04a047e72eea5cec3bc266db09e35de6bdfe34c9436ac5ee27d237" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:7c36280e6fb8385e520936c3cb3b8042851904eba0e58d277dca80a5cfed590b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:ec031d5d2feb36d1d1a24380e4db6d43695f3748343d99434e6f5f9156aaa2ed" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-musllinux_1_1_aarch64.whl", hash = "sha256:936d68689298c36b53b29f23c6dbb74de12b4ac12ca6cfe0e047bedceea56180" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-musllinux_1_1_x86_64.whl", hash = "sha256:23502f431948090f597378482b4812b0caae32c22213aecf3b55325e049a6c68" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-win32.whl", hash = "sha256:2e99c6826ffa974fe6e27cdb5ed0021786b03fc98e5ee3c5bfe1fd5015f42b99" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp310-cp310-win_amd64.whl", hash = "sha256:a4d3091415f010369ae4ed1fc6b79def9416358877534caf6a0fdd2146c87a3e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:cc1c1159b3d456576af7a3e4d1ba7e6924cb39de8f67111c735f6fc832082774" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:1e2120ef853f59c7419231f3bf4e7021f1b936f6ebd222406c3b60212205d2ee" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:5d225db5a45f21e78dd9358e58a98702a0302f2659a3c6cd320564b75b86f47c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:5ac9328ec4831237bec75defaf839f7d4564be1e6b25ac710bd1a96321cc8317" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:3ad2a3decf9aaba3d29c8f537ac4b243e36bef957511b4766cb0057d32b0be85" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-musllinux_1_1_aarch64.whl", hash = "sha256:ff3824dc5261f50c9b0dfb3be22b4567a6f938ccce4587b38952d85fd9e9afe4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-musllinux_1_1_x86_64.whl", hash = "sha256:797b4f722ffa07cc8d62053e4cff1486fa6dc094105d13fea7b1de7d8bf71c9e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-win32.whl", hash = "sha256:11d8f3dd2b9c1207dcaf2ee0bbbfd5991f571186ec9cc78427ba5bd32afae4b5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp311-cp311-win_amd64.whl", hash = "sha256:e10ce637b18caea04431ce14fabcf5c64a1c61ec9c56b071a4b7ca131ca52d44" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-macosx_10_9_x86_64.whl", hash = "sha256:c70c95198c015b85feafc136515252a261a84561b7b1d51e3384e0655ddf25ab" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:ce826d6ef20b1bc864f0a68340c8b3287705cae2f8b4b1d932177dcc76721725" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:1f71ea527786de97d1a0cc0eacd1defc0985dcf6b3f17bb77dcfc8c34bec4dc5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:9b22676e8097e9e22e36d6b7bda33190d0d400f345f23d4065d48f4ca7ae0425" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:80bab7bfc629882493af4aa31a4cfa43a4c57c83813253626916b8c7ada83476" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_aarch64.whl", hash = "sha256:0833f8694549e586547b576dcfaba4a6b55b9e96098b36cdc7ebefe667dfed48" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-musllinux_1_1_x86_64.whl", hash = "sha256:8b9c7197f7cb2738065c481a0461e50ad02f18c78cd75775628afb4d7137fb3b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-win32.whl", hash = "sha256:ef6107725bd54b262d6dedcc2af448a266975032bc85ef0172c5f059da6325b4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp312-cp312-win_amd64.whl", hash = "sha256:7e7401d0de89a9a855c839bc697c079a4af81cf878373abd7dc625847d25cbd8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:efdca5630322a10774e8e98e1af481aad470dd62c3170801852d752aa7a783ba" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:50187695423ffe49e2deacb8cd10510bc361faac997de9efef88badc3bb9e2d1" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:0ffe8360bab4910ef1b9e87fb812d8bc0a308b0d0eef8c8f44e0254ab3b07133" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:17e311b6c678207928d649faa7cb0d7b4c26a0ba73d41e99c4fff6b6c3276484" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:70b189594dbe54f75ab3a1acec5f1e3faa7e8cf2f1e08d9b561cb41b845f69d5" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_aarch64.whl", hash = "sha256:41e4e3953a79407c794916fa277a82531dd93aad34e29c2a514c2c0c5fe971cc" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-musllinux_1_1_x86_64.whl", hash = "sha256:68ccc6023a3400877818152ad9a1033e3db8625d899c72eacb5a668902e4d652" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-win32.whl", hash = "sha256:bc2fa7c6b47d6bc618dd7fb02ef6fdedb1090ec036abab80d4681424b84c1183" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/pyyaml/6.0.2/PyYAML-6.0.2-cp313-cp313-win_amd64.whl", hash = "sha256:8388ee1976c416731879ac16da0aff3f63b286ffdd57cdeb95f3f2e085687563" },
]

[[package]]
name = "requests"
version = "2.32.3"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/requests/2.32.3/requests-2.32.3.tar.gz", hash = "sha256:55365417734eb18255590a9ff9eb97e9e1da868d4ccd6402399eaf68af20a760" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/requests/2.32.3/requests-2.32.3-py3-none-any.whl", hash = "sha256:70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6" },
]

[[package]]
name = "rsa"
version = "4.9"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
dependencies = [
    { name = "pyasn1" },
]
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/rsa/4.9/rsa-4.9.tar.gz", hash = "sha256:e38464a49c6c85d7f1351b0126661487a7e0a14a50f1675ec50eb34d4f20ef21" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/rsa/4.9/rsa-4.9-py3-none-any.whl", hash = "sha256:90260d9058e514786967344d0ef75fa8727eed8a7d2e43ce9f4bcf1b536174f7" },
]

[[package]]
name = "tabulate"
version = "0.9.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tabulate/0.9.0/tabulate-0.9.0.tar.gz", hash = "sha256:0095b12bf5966de529c0feb1fa08671671b3368eec77d7ef7ab114be2c068b3c" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tabulate/0.9.0/tabulate-0.9.0-py3-none-any.whl", hash = "sha256:024ca478df22e9340661486f85298cff5f6dcdba14f3813e8830015b9ed1948f" },
]

[[package]]
name = "tomli"
version = "2.2.1"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1.tar.gz", hash = "sha256:cd45e1dc79c835ce60f7404ec8119f2eb06d38b1deba146f07ced3bbc44505ff" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-macosx_10_9_x86_64.whl", hash = "sha256:678e4fa69e4575eb77d103de3df8a895e1591b48e740211bd1067378c69e8249" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-macosx_11_0_arm64.whl", hash = "sha256:023aa114dd824ade0100497eb2318602af309e5a55595f76b626d6d9f3b7b0a6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:ece47d672db52ac607a3d9599a9d48dcb2f2f735c6c2d1f34130085bb12b112a" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:6972ca9c9cc9f0acaa56a8ca1ff51e7af152a9f87fb64623e31d5c83700080ee" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:c954d2250168d28797dd4e3ac5cf812a406cd5a92674ee4c8f123c889786aa8e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-musllinux_1_2_aarch64.whl", hash = "sha256:8dd28b3e155b80f4d54beb40a441d366adcfe740969820caf156c019fb5c7ec4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-musllinux_1_2_i686.whl", hash = "sha256:e59e304978767a54663af13c07b3d1af22ddee3bb2fb0618ca1593e4f593a106" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-musllinux_1_2_x86_64.whl", hash = "sha256:33580bccab0338d00994d7f16f4c4ec25b776af3ffaac1ed74e0b3fc95e885a8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-win32.whl", hash = "sha256:465af0e0875402f1d226519c9904f37254b3045fc5084697cefb9bdde1ff99ff" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp311-cp311-win_amd64.whl", hash = "sha256:2d0f2fdd22b02c6d81637a3c95f8cd77f995846af7414c5c4b8d0545afa1bc4b" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-macosx_10_13_x86_64.whl", hash = "sha256:4a8f6e44de52d5e6c657c9fe83b562f5f4256d8ebbfe4ff922c495620a7f6cea" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-macosx_11_0_arm64.whl", hash = "sha256:8d57ca8095a641b8237d5b079147646153d22552f1c637fd3ba7f4b0b29167a8" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:4e340144ad7ae1533cb897d406382b4b6fede8890a03738ff1683af800d54192" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:db2b95f9de79181805df90bedc5a5ab4c165e6ec3fe99f970d0e302f384ad222" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:40741994320b232529c802f8bc86da4e1aa9f413db394617b9a256ae0f9a7f77" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-musllinux_1_2_aarch64.whl", hash = "sha256:400e720fe168c0f8521520190686ef8ef033fb19fc493da09779e592861b78c6" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-musllinux_1_2_i686.whl", hash = "sha256:02abe224de6ae62c19f090f68da4e27b10af2b93213d36cf44e6e1c5abd19fdd" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-musllinux_1_2_x86_64.whl", hash = "sha256:b82ebccc8c8a36f2094e969560a1b836758481f3dc360ce9a3277c65f374285e" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-win32.whl", hash = "sha256:889f80ef92701b9dbb224e49ec87c645ce5df3fa2cc548664eb8a25e03127a98" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp312-cp312-win_amd64.whl", hash = "sha256:7fc04e92e1d624a4a63c76474610238576942d6b8950a2d7f908a340494e67e4" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:f4039b9cbc3048b2416cc57ab3bda989a6fcf9b36cf8937f01a6e731b64f80d7" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:286f0ca2ffeeb5b9bd4fcc8d6c330534323ec51b2f52da063b11c502da16f30c" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:a92ef1a44547e894e2a17d24e7557a5e85a9e1d0048b0b5e7541f76c5032cb13" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:9316dc65bed1684c9a98ee68759ceaed29d229e985297003e494aa825ebb0281" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:e85e99945e688e32d5a35c1ff38ed0b3f41f43fad8df0bdf79f72b2ba7bc5272" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:ac065718db92ca818f8d6141b5f66369833d4a80a9d74435a268c52bdfa73140" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:d920f33822747519673ee656a4b6ac33e382eca9d331c87770faa3eef562aeb2" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:a198f10c4d1b1375d7687bc25294306e551bf1abfa4eace6650070a5c1ae2744" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-win32.whl", hash = "sha256:d3f5614314d758649ab2ab3a62d4f2004c825922f9e370b29416484086b264ec" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-cp313-cp313-win_amd64.whl", hash = "sha256:a38aa0308e754b0e3c67e344754dff64999ff9b513e691d0e786265c93583c69" },
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/tomli/2.2.1/tomli-2.2.1-py3-none-any.whl", hash = "sha256:cb55c73c5f4408779d0cf3eef9f762b9c9f147a77de7b258bef0a5628adc85cc" },
]

[[package]]
name = "types-protobuf"
version = "5.29.1.20250403"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/types-protobuf/5.29.1.20250403/types_protobuf-5.29.1.20250403.tar.gz", hash = "sha256:7ff44f15022119c9d7558ce16e78b2d485bf7040b4fadced4dd069bb5faf77a2" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/types-protobuf/5.29.1.20250403/types_protobuf-5.29.1.20250403-py3-none-any.whl", hash = "sha256:c71de04106a2d54e5b2173d0a422058fae0ef2d058d70cf369fb797bf61ffa59" },
]

[[package]]
name = "urllib3"
version = "2.3.0"
source = { registry = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/" }
sdist = { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/urllib3/2.3.0/urllib3-2.3.0.tar.gz", hash = "sha256:f8c5449b3cf0861679ce7e0503c7b44b5ec981bec0d1d3795a07f1ba96f0204d" }
wheels = [
    { url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/urllib3/2.3.0/urllib3-2.3.0-py3-none-any.whl", hash = "sha256:1cee9ad369867bfdbbb48b7dd50374c0967a0bb7710050facf0dd6911440e3df" },
]
