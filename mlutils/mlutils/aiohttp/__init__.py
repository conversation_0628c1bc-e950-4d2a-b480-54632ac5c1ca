from aiohttp import ClientSession


class Client:
    def __init__(self, **kwargs):
        self._kwargs = kwargs
        self._session = None

    async def request(self, method, url, json=None):
        if self._session is None:
            raise RuntimeError("you must call connect before you can make requests")
        async with self._session.request(method, url, json=json) as response:
            response.raise_for_status()
            return await response.json()

    async def connect(self):
        self._session = await ClientSession(**self._kwargs).__aenter__()

    async def disconnect(self):
        await self._session.__aexit__(None, None, None)
