import asyncio
from functools import partial


def async_generator_from(generator_fn):
    async def wrapped(*args, **kwargs):
        loop = asyncio.get_running_loop()
        queue = asyncio.Queue(1)
        end = object()

        def produce():
            try:
                for item in generator_fn(*args, **kwargs):
                    asyncio.run_coroutine_threadsafe(queue.put(item), loop).result()
                asyncio.run_coroutine_threadsafe(queue.put(end), loop).result()
            except Exception as e:
                asyncio.run_coroutine_threadsafe(queue.put(e), loop).result()

        coro = loop.run_in_executor(None, produce)
        item = await queue.get()
        while item is not end:
            if isinstance(item, Exception):
                raise item
            yield item
            item = await queue.get()
        await coro

    return wrapped


async def gather_dict(aws):
    async def pair(key, coro):
        return key, await coro

    args = [pair(key, coro) for key, coro in aws.items()]
    return {key: result for key, result in await asyncio.gather(*args)}


class TaskRunner:
    def __init__(self):
        self._pending_tasks = set()

    def _done_callback(self, task, raise_exceptions=True):
        exception = task.exception()
        if raise_exceptions and exception:
            raise exception
        self._pending_tasks.discard(task)

    def run_in_background(self, coro, raise_exceptions=True):
        task = asyncio.create_task(coro)
        self._pending_tasks.add(task)
        task.add_done_callback(
            partial(self._done_callback, raise_exceptions=raise_exceptions)
        )

    async def setup(self):
        pass

    async def teardown(self):
        await asyncio.gather(*self._pending_tasks)
