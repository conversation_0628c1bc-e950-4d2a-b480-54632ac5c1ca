async def fetch_all(
    fn, response_key, start_kw="NextToken", next_kw="NextToken", *args, **kwargs
):
    items = []
    response = await fn(*args, **kwargs)
    items += response[response_key]

    if next_kw in response:
        while next_kw in response:
            kwargs[start_kw] = response[next_kw]
            response = await fn(*args, **kwargs)
            items += response[response_key]

    return items
