import os
from dataclasses import fields
from dataclasses import is_dataclass
from typing import get_args
from typing import get_origin
from typing import Union


try:
    from types import UnionType
except ImportError:
    UnionType = type(Union[str, int])


def from_env(cls, overrides={}, prefixes=[]):
    def is_optional(t):
        origin = get_origin(t)
        args = get_args(t)
        is_union = origin == Union or origin == UnionType
        return is_union and len(args) == 2 and args[1] is type(None)

    args = {}
    for field in fields(cls):
        if is_dataclass(field.type):
            args[field.name] = from_env(field.type, overrides, prefixes + [field.name])
        else:
            env_var_name = "_".join([*prefixes, field.name]).upper()
            key = overrides.get(env_var_name, env_var_name)
            env_var_value = os.environ.get(key)
            if env_var_value is not None:
                if field.type is bool:
                    args[field.name] = env_var_value not in ("False", "false")
                elif is_optional(field.type):
                    args[field.name] = get_args(field.type)[0](env_var_value)
                else:
                    args[field.name] = field.type(env_var_value)
    return cls(**args)
