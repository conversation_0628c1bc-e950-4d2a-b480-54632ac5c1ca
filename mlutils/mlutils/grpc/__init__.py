import asyncio
from collections import namedtuple

import grpc
from google.protobuf.json_format import MessageToDict
from google.protobuf.json_format import ParseDict
from grpc import aio


async def _run_handlers(handlers):
    for handler in handlers:
        if asyncio.iscoroutinefunction(handler):
            await handler()
        else:
            handler()


def proto_to_dict(message, **kwargs):
    default_kwargs = {
        "always_print_fields_with_no_presence": True,
        "preserving_proto_field_name": True,
    }
    return MessageToDict(message, **{**default_kwargs, **kwargs})


def dict_to_proto(d, klass, **kwargs):
    return ParseDict(d, klass, **kwargs)


class Service:
    def __init__(
        self, servicers, interceptors=None, options={}, on_startup=[], on_shutdown=[]
    ):
        self.on_startup = on_startup
        self.on_shutdown = on_shutdown
        self.servicers = servicers
        self.interceptors = interceptors
        self.options = options
        self._server = None

    async def start(self, host, port):
        self._server = aio.server(
            interceptors=self.interceptors, options=tuple(self.options.items())
        )
        for servicer, registration_function in self.servicers:
            registration_function(servicer, self._server)
        self._server.add_insecure_port(f"{host}:{port}")
        await _run_handlers(self.on_startup)
        await self._server.start()

    async def stop(self, grace=10):
        await self._server.stop(grace)
        await _run_handlers(self.on_shutdown)

    async def _serve(self, host, port):
        await self.start(host, port)
        await self._server.wait_for_termination()
        await _run_handlers(self.on_shutdown)

    def serve(self, host="0.0.0.0", port=8888):  # nosec
        asyncio.run(self._serve(host, port))


class _ClientCallDetails(
    namedtuple(
        "_ClientCallDetails",
        ("method", "timeout", "metadata", "credentials", "wait_for_ready"),
    ),
    grpc.ClientCallDetails,
):
    pass


class HeaderAdderClientInterceptor(grpc.aio.UnaryUnaryClientInterceptor):
    def __init__(self, headers):
        super().__init__()
        self.headers = headers

    async def intercept_unary_unary(self, continuation, details, request):
        metadata = []
        if details.metadata is not None:
            metadata = list(details.metadata)
        for key, value in self.headers.items():
            metadata.append((key.lower(), value))
        new_details = _ClientCallDetails(
            details.method,
            details.timeout,
            metadata,
            details.credentials,
            details.wait_for_ready,
        )
        return await continuation(new_details, request)
