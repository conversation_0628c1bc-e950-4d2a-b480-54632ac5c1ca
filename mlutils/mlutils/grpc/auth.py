import json
from base64 import b64decode
from base64 import b64encode
from contextvars import <PERSON><PERSON><PERSON><PERSON>
from dataclasses import dataclass
from functools import wraps
from typing import Any
from collections.abc import Callable

from grpc import aio
from grpc import StatusCode
from grpc import unary_unary_rpc_method_handler

from mlutils.grpc import HeaderAdderClientInterceptor


current_user = ContextVar("current_user")


@dataclass
class User:
    email: str
    password: str
    permissions: set[str]


def decode_users(encoded):
    d = json.loads(encoded)
    users = {}
    for k, v in d.items():
        permissions = set(v.get("permissions", []))
        users[k] = User(email=k, password=v["password"], permissions=permissions)
    return users


class BasicAuthServerInterceptor(aio.ServerInterceptor):
    ALLOWED_METHODS = ["/grpc.health.v1.Health/Check", "/grpc.health.v1.Health/Watch"]

    def __init__(self, encoded_users, excluded_methods=[]):
        self.users = decode_users(encoded_users)
        self._allowed_methods = self.ALLOWED_METHODS + excluded_methods

        async def terminate(_, context):
            await context.abort(StatusCode.UNAUTHENTICATED, "Authentication Required")

        self._terminator = unary_unary_rpc_method_handler(terminate)

    def _verify(self, handler_call_details):
        for header, value in handler_call_details.invocation_metadata:
            if header == "authorization":
                scheme, content = value.split(" ", 1)
                if scheme != "Basic":
                    return None
                decoded = b64decode(content).decode("UTF-8")
                email, password = decoded.split(":", 1)
                user = self.users.get(email)
                if user and password == user.password:
                    return user
                else:
                    return None
        return None

    async def intercept_service(self, continuation, handler_call_details):
        if handler_call_details.method in self._allowed_methods:
            return await continuation(handler_call_details)
        user = self._verify(handler_call_details)
        if user is None:
            return self._terminator
        current_user.set(user)
        return await continuation(handler_call_details)


def require_permission(permission: str) -> Callable:
    """Decorator to enforce permission checks on gRPC service methods.

    Handles both unary and streaming RPCs by checking if the result is an
    async generator. This allows the decorator to work transparently with
    any type of RPC without needing to know specific method names.

    Args:
        permission: Required permission string to access this endpoint
    """

    def wrapper(f: Callable) -> Callable:
        @wraps(f)
        async def wrapped(self, request: Any, context: aio.ServicerContext):
            # Verify permissions first
            try:
                user = current_user.get()
                if permission not in user.permissions:
                    await context.abort(
                        StatusCode.PERMISSION_DENIED,
                        f"User does not have required permission: {permission}",
                    )
            except LookupError:
                await context.abort(
                    StatusCode.UNAUTHENTICATED, "Authentication Required"
                )

            # Call the wrapped function
            result = f(self, request, context)

            # Check if this is a streaming RPC by looking for the async generator protocol
            if hasattr(result, "__aiter__"):
                return result  # Return the async generator directly

            # For regular RPCs, await the result
            return await result

        return wrapped

    return wrapper


def build_basic_auth(user, password):
    encoded = b64encode(f"{user}:{password}".encode())
    return f"Basic {encoded.decode('UTF-8')}"


class BasicAuthClientInterceptor(HeaderAdderClientInterceptor):
    def __init__(self, user, password):
        authorization = build_basic_auth(user, password)
        super().__init__({"Authorization": authorization})
