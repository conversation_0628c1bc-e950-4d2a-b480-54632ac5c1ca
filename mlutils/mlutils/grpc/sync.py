from collections import namedtuple
from concurrent.futures import ThreadPoolExecutor

import grpc
from grpc_health.v1.health import HealthServicer
from grpc_health.v1.health_pb2_grpc import add_HealthServicer_to_server
from grpc_health.v1.health_pb2 import HealthCheckResponse


class _ClientCallDetails(
    namedtuple("_ClientCallDetails", ("method", "timeout", "metadata", "credentials")),
    grpc.ClientCallDetails,
):
    pass


class HeaderAdderClientInterceptor(grpc.UnaryUnaryClientInterceptor):
    def __init__(self, headers):
        super().__init__()
        self.headers = headers

    def intercept_unary_unary(self, continuation, details, request):
        metadata = []
        if details.metadata is not None:
            metadata = list(details.metadata)
        for key, value in self.headers.items():
            metadata.append((key.lower(), value))
        new_details = _ClientCallDetails(
            details.method, details.timeout, metadata, details.credentials
        )
        return continuation(new_details, request)


def _run_handlers(handlers):
    for handler in handlers:
        handler()


class Service:
    def __init__(
        self,
        servicers,
        max_workers=None,
        on_startup=[],
        on_shutdown=[],
        health_check_services=[],
    ):
        self.servicers = servicers
        self.max_workers = max_workers
        self.on_startup = on_startup
        self.on_shutdown = on_shutdown
        self.health_check_services = health_check_services

    def serve(self, port=8888):
        _run_handlers(self.on_startup)
        executor = ThreadPoolExecutor(max_workers=self.max_workers)
        server = grpc.server(executor)
        health_servicer = HealthServicer()
        servicers = self.servicers + [(health_servicer, add_HealthServicer_to_server)]
        for servicer, registration_function in servicers:
            registration_function(servicer, server)
        health_servicer.set("", HealthCheckResponse.SERVING)
        for service in self.health_check_services:
            health_servicer.set(service, HealthCheckResponse.SERVING)
        server.add_insecure_port(f"[::]:{port}")
        server.start()
        server.wait_for_termination()
        _run_handlers(self.on_shutdown)
