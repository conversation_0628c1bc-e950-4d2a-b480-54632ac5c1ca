import numpy as np
from sklearn.base import BaseEstimator


class SigmoidScaler(BaseEstimator):
    def __init__(self, subtract_mean=False):
        self.subtract_mean = subtract_mean
        self.mean_ = None

    def fit(self, X, y=None):
        self.mean_ = X.mean()
        return self

    def predict(self, X):
        z = X - self.mean_ if self.subtract_mean else X
        return 1 / (1 + np.exp(-z))
