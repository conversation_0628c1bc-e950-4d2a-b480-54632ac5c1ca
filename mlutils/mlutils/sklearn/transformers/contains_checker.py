import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class Contains<PERSON>hecker(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    """
    Note that the transform will be much slower that other transforms
    due to pandas not supporting a vectorized version of str.contains()
    to-do: explore if <PERSON><PERSON> has vectorized functionality here
    """

    def __init__(self, substring_col, text_col, feature_names_out=None):
        self.substring_col = substring_col
        self.text_col = text_col
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        out = [
            str(sub).lower() in str(text).lower()
            if pd.notna(sub) and pd.notna(text)
            else np.nan
            for sub, text in zip(X[self.substring_col], X[self.text_col])
        ]
        return pd.Series(out, index=X.index, dtype="boolean").to_frame()
