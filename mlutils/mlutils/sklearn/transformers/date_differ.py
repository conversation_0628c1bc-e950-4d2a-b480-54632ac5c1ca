import pandas as pd
from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class DateDiffer(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    def __init__(
        self, minuend_col, subtrahend_col, timedelta_unit, feature_names_out=None
    ):
        self.minuend_col = minuend_col
        self.subtrahend_col = subtrahend_col
        self.timedelta_unit = timedelta_unit
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        subtrahend = pd.to_datetime(X[self.subtrahend_col], errors="coerce")
        minuend = pd.to_datetime(X[self.minuend_col], errors="coerce")
        out = (minuend - subtrahend) / pd.Timedelta(1, unit=self.timedelta_unit)
        return out.to_frame()
