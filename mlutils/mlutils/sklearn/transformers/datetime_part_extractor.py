from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class DatetimePartExtractor(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    def __init__(self, datetime_col, time_unit, feature_names_out=None):
        self.datetime_col = datetime_col
        self.time_unit = time_unit
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        dt_accessor = X[self.datetime_col].dt

        try:
            out = (
                getattr(dt_accessor.isocalendar(), "week")
                if self.time_unit == "week"
                else getattr(dt_accessor, self.time_unit)
            )
        except AttributeError:
            raise ValueError(
                f"Invalid time_unit '{self.time_unit}'. Must be a valid attribute of pd.Series.dt."
            )

        return out.to_frame()
