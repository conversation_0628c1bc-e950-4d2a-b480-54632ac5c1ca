import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class EqualityChecker(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    def __init__(self, col1, col2, feature_names_out=None):
        self.col1 = col1
        self.col2 = col2
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        col1_values = X[self.col1].values
        col2_values = X[self.col2].values

        # Equality check (elementwise)
        equals = col1_values == col2_values

        # Null check (either column)
        nulls = pd.isna(col1_values) | pd.isna(
            col2_values
        )  # Using pd.isna for more robust null checking

        # Where nulls are True, set equals to np.nan
        result = np.where(nulls, np.nan, equals).astype(float)

        # Return as DataFrame to maintain consistency and preserve the index
        return pd.DataFrame(result, index=X.index)
