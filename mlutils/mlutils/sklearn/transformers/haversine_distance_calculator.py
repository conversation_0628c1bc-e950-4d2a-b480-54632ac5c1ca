import numpy as np
from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin

EARTH_RADIUS_KM = 6371


class HaversineDistanceCalculator(
    BaseEstimator, TransformerMixin, FeatureNameOverrideMixin
):
    def __init__(
        self, lat_col_1, lon_col_1, lat_col_2, lon_col_2, feature_names_out=None
    ):
        self.lat_col_1 = lat_col_1
        self.lon_col_1 = lon_col_1
        self.lat_col_2 = lat_col_2
        self.lon_col_2 = lon_col_2
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        lat1_rad, lon1_rad = (
            np.radians(X[self.lat_col_1]),
            np.radians(X[self.lon_col_1]),
        )
        lat2_rad, lon2_rad = (
            np.radians(X[self.lat_col_2]),
            np.radians(X[self.lon_col_2]),
        )

        delta_lat = lat2_rad - lat1_rad
        delta_lon = lon2_rad - lon1_rad

        a = (
            np.sin(delta_lat / 2) ** 2
            + np.cos(lat1_rad) * np.cos(lat2_rad) * np.sin(delta_lon / 2) ** 2
        )
        c = 2 * np.arcsin(np.sqrt(a))

        out = EARTH_RADIUS_KM * c

        return out.to_frame()
