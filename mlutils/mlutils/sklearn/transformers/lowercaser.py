from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class Lowercaser(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    def __init__(self, column, feature_names_out=None):
        self.column = column
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        return X[self.column].str.lower().to_frame()
