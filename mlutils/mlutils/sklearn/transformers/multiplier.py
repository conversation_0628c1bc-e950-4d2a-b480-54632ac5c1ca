from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class Multiplier(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    def __init__(self, col1, col2, feature_names_out=None):
        self.col1 = col1
        self.col2 = col2
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        return (X[self.col1] * X[self.col2]).to_frame()
