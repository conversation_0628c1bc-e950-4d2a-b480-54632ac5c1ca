from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class Subtractor(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    def __init__(self, minuend_col, subtrahend_col, feature_names_out=None):
        self.minuend_col = minuend_col
        self.subtrahend_col = subtrahend_col
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        return (X[self.minuend_col] - X[self.subtrahend_col]).to_frame()
