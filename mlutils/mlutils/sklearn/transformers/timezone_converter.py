from sklearn.base import BaseEstimator, TransformerMixin

from mlutils.sklearn.transformers.mixins import FeatureNameOverrideMixin


class TimezoneConverter(BaseEstimator, TransformerMixin, FeatureNameOverrideMixin):
    def __init__(
        self,
        datetime_col,
        target_timezone,
        current_timezone=None,
        feature_names_out=None,
    ):
        self.datetime_col = datetime_col
        self.current_timezone = current_timezone
        self.target_timezone = target_timezone
        self.feature_names_out = feature_names_out

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        dt_series = X[self.datetime_col]

        if self.current_timezone:
            dt_series = dt_series.dt.tz_localize(
                self.current_timezone, ambiguous="NaT", nonexistent="NaT"
            )

        out = dt_series.dt.tz_convert(self.target_timezone)

        return out.to_frame()
