import socket


LOCALHOST = "127.0.0.1"


def get_free_tcp_port():
    sckt = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sckt.bind((LOCALHOST, 0))
    _, port = sckt.getsockname()
    sckt.close()
    return port


def chunked(items, n):
    for i in range(0, len(items), n):
        yield items[i : i + n]


def remove_none_values(obj):
    if isinstance(obj, list):
        return [remove_none_values(i) for i in obj]
    elif isinstance(obj, dict):
        return {k: v for k, v in obj.items() if v is not None}
    else:
        return obj
