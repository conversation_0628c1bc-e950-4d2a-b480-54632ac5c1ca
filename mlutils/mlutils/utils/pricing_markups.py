from gametime_protos.pandc.pricing.v1.service_pb2_grpc import PricingServiceStub
import logging
import boto3
import grpc
import pathlib
import socket
from gametime_protos.pandc.pricing.v1.service_pb2 import (
    PricingServiceUpdateMarkupsRequest,
)
import gametime_protos.pandc.pricing.v1.service_pb2_grpc as service_pb2_grpc
import gametime_protos.pandc.pricing.v1.service_pb2 as service_pb2
from redis import Redis


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Inspect proto definitions
logger.debug("Inspecting PricingServiceStub class details:")
logger.debug(f"Module location: {PricingServiceStub.__module__}")
logger.debug(
    f"Class methods: {[method for method in dir(PricingServiceStub) if not method.startswith('_')]}"
)

# Print available message types
logger.debug("Available message types in service_pb2:")
for name in dir(service_pb2):
    if not name.startswith("_"):
        logger.debug(f"- {name}")

# Print service proto path and content
logger.debug("Service proto path: %s", service_pb2_grpc.__file__)
logger.debug(
    "GRPC file content:\n%s", pathlib.Path(service_pb2_grpc.__file__).read_text()
)

# Print service descriptor info
logger.debug("Service DESCRIPTOR:\n%s", service_pb2.DESCRIPTOR.services_by_name)

# Print service details
logger.debug(
    "Service full name: %s",
    service_pb2.DESCRIPTOR.services_by_name["PricingService"].full_name,
)
logger.debug("Available methods:")
for method in service_pb2.DESCRIPTOR.services_by_name["PricingService"].methods:
    logger.debug("- %s", method.name)
logger.info("Full endpoint path: /pandc.pricing.v1.PricingService/FetchSettings")


def get_api_credentials() -> str:
    logger.info("Fetching API credentials from AWS Secrets Manager")
    secrets_client = boto3.client("secretsmanager", region_name="us-west-1")
    secret_arn = "arn:aws:secretsmanager:us-west-1:728489771660:secret:baseline-system/production/MACHINE_API_CREDENTIALS-px6Xlq"  # nosec
    response = secrets_client.get_secret_value(SecretId=secret_arn)
    logger.info("Successfully retrieved API credentials")
    return response["SecretString"]


PRICING_SERVICE_URL = "pricing.production.gteng.co:443"

REDIS_CONFIG = {
    "host": "production-pricing-cluster.ox65hv.ng.0001.usw1.cache.amazonaws.com",
    "port": 6379,
    "socket_timeout": 1.0,
}

REDIS_CLIENT = Redis(**REDIS_CONFIG, decode_responses=True)


def validate_markups(markups: dict) -> None:
    """
    Validates that the markups dictionary contains keys as strings and values as floats.

    Args:
        markups (dict): The dictionary to validate.

    Raises:
        ValueError: If the markups dictionary is invalid.
    """
    if not isinstance(markups, dict):
        raise ValueError(
            "Markups must be a dictionary with event IDs as keys and markups as float values."
        )

    for key, value in markups.items():
        if not isinstance(key, str):
            raise ValueError(
                f"Invalid key type in markups. Expected str, got {type(key).__name__}: {key}"
            )
        if not isinstance(value, float):
            raise ValueError(
                f"Invalid value type in markups for key '{key}'. Expected float, got {type(value).__name__}: {value}"
            )
        if not 0 < value < 1:
            raise ValueError(
                f"Invalid markup value for key '{key}'. Expected a float between 0 and 1, got {value}"
            )


# Add DNS resolution logging
logger.info(f"Resolving {PRICING_SERVICE_URL}...")
try:
    addr_info = socket.getaddrinfo(PRICING_SERVICE_URL.split(":")[0], 443)
    logger.info(f"Resolved addresses: {addr_info}")
except Exception as e:
    logger.error(f"Failed to resolve: {e}")


def get_channel_credentials() -> grpc.ChannelCredentials:
    return grpc.ssl_channel_credentials()


def update_markups(markups: dict) -> None:
    try:
        logger.info(f"Attempting to connect to gRPC service at {PRICING_SERVICE_URL}")

        auth_token = get_api_credentials()
        metadata = [("authorization", auth_token)]
        logger.info("Auth metadata prepared")

        # Add connection debugging options
        options = [
            ("grpc.keepalive_time_ms", 10000),
            ("grpc.keepalive_timeout_ms", 5000),
            ("grpc.keepalive_permit_without_calls", True),
            ("grpc.http2.min_time_between_pings_ms", 10000),
            ("grpc.http2.max_pings_without_data", 0),
            ("grpc.enable_retries", 1),
            (
                "grpc.service_config",
                '{"methodConfig": [{"name": [{"service": "pandc.pricing.v1.PricingService"}],"retryPolicy": {"maxAttempts": 5,"initialBackoff": "0.1s","maxBackoff": "1s","backoffMultiplier": 2,"retryableStatusCodes": ["UNAVAILABLE"]}}]}',
            ),
        ]

        with grpc.secure_channel(
            target=PRICING_SERVICE_URL,
            credentials=get_channel_credentials(),
            options=options,
        ) as channel:
            # Add connection state callback
            logger.info(
                "Channel state: %s", channel._channel.check_connectivity_state(True)
            )

            def channel_ready_future_callback(future):
                try:
                    future.result()
                    logger.info("Channel ready!")
                except Exception as e:
                    logger.error("Channel failed to become ready: %s", e)

            channel_ready = grpc.channel_ready_future(channel)
            channel_ready.add_done_callback(channel_ready_future_callback)

            # Wait for channel to be ready with timeout
            try:
                channel_ready.result(timeout=10)
            except grpc.FutureTimeoutError:
                logger.error("Timeout waiting for channel to be ready")
            except Exception as e:
                logger.error("Error waiting for channel: %s", e)

            logger.info("Secure channel established")

            stub = PricingServiceStub(channel)
            logger.info("Created PricingServiceStub")

            logger.info("Attempting to call PricingServiceUpdateMarkupsRequest...")
            request = PricingServiceUpdateMarkupsRequest(markups=markups)
            logger.info(f"Request object: {request}")

            response = stub.UpdateMarkups(request, metadata=metadata)
            logger.info(f"Received response: {response}")

    except grpc.RpcError as e:
        logger.error(f"RPC failed: {e.code()}")
        logger.error(f"Error details: {e.details()}")
        logger.error(f"Error debug string: {e}")
        if hasattr(e, "_state"):
            logger.error(f"Error state: {e._state}")
    except Exception:
        logger.exception("Unexpected error occurred")

    # Log proto module location
    logger.info(f"Proto module location: {service_pb2.__file__}")


def get_markups(event_ids: list) -> dict:
    """
    Retrieves the markups for a list of event IDs from Redis.

    Args:
        event_ids (list): A list of event IDs.

    Returns:
        dict: A dictionary mapping event IDs to their markup values.
    """
    keys = [_markup_key_for(event_id) for event_id in event_ids]
    values = REDIS_CLIENT.mget(keys)

    return {event_id: markup for event_id, markup in zip(event_ids, values)}


def _markup_key_for(event_id: str) -> str:
    return f"events:{event_id}:markup"


def get_all_markups() -> dict:
    """
    Retrieves all event markups stored in Redis using scan for better performance.

    Returns:
        dict: A dictionary mapping event IDs to their markups.
    """
    pattern = "events:*:markup"
    results = {}
    cursor = "0"

    while cursor != 0:
        cursor, keys = REDIS_CLIENT.scan(cursor=cursor, match=pattern, count=1000)
        if keys:
            values = REDIS_CLIENT.mget(keys)
            for key, value in zip(keys, values):
                event_id = key.split(":")[1]
                results[event_id] = value

    if not results:
        return "No markups found"

    return results
