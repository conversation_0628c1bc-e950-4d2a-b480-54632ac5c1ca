# prism

# 1. Overview and Motivation

Machine learning (ML) at our company spans a wide variety of use cases: fraud detection, pricing, curation/recommendation, and more. Each model needs to combine features (historical signals, aggregates, counters, etc.) from different data sources consistently. The goal of this feature platform is to provide:

1. A single, consistent way to **author** features (in a declarative manner).
2. A mechanism to **backfill** historical data for training.
3. A **low-latency** path to fetch the same features for online inference.
4. An architecture that supports both **batch** and **streaming** data sources without duplicating complex pipelines.

The long-term benefit is to eliminate ad-hoc feature engineering pipelines, speed up ML experimentation, and reduce operational burden by centralizing both offline and online feature generation in a single system.

# 2. Key Requirements and Goals

1. **Declarative Feature Definitions:**
    - Data scientists and ML engineers should specify features (and their transformations, aggregations, etc.) in a simple config file (e.g., YAML).
    - Each logical grouping of features is defined as a “feature family.”
2. **Seamless Offline and Online Access:**
    - The same feature definitions should be usable for both offline training (batch ingestion) and real-time scoring (online requests).
    - Ability to fetch feature values at any arbitrary historical timestamp (for training) or the current timestamp (for inference).
3. **Scalable Aggregations:**
    - Many features can be expressed as cumulative or windowed aggregates (e.g., sum, count, average).
    - We want to avoid repeated full-scan computations for training (e.g., re-aggregating each time from raw tables). Instead, a “partial aggregates” method maintains running computations and makes them available for both batch and real-time use.
4. **Robustness & Exactly-Once Semantics Per Entity:**
    - Feature rows must be processed in timestamp order *per entity*.
    - Must handle large data volume (billions of rows) without losing track of progress.
    - Recovery from failures without data corruption or “dropped tasks.”
5. **Ease of Development & Operation:**
    - Minimal friction for data scientists creating or modifying features.
    - Observability (latency, status, “frontier” timestamps) but with as little custom overhead as possible (prefer standard solutions like Datadog for alerts/monitoring).
    - Incremental rollout: start with batch-based ingestion, then add streaming.

# 3. High-Level Architecture

At a high level, the feature platform consists of:

1. **Feature Families**
    - Defined in a repo as small YAML (or alternative) files.
    - Each family groups related features derived from the same underlying event stream (e.g., “orders,” “page views,” “search events”).
2. **Core Service (gRPC)**
    - Implements the main logic for feature ingestion, partial-aggregation, data retrieval, etc.
    - Exposes RPCs to create new feature families, trigger backfills, retrieve features online/offline, and manage metadata.
3. **SDK & CLI**
    - An **SDK** for Python (and possibly other languages later) so data scientists can:
        - Interactively define & test new features in notebooks (draft mode).
        - Fetch features offline for training or batch inference.
        - Fetch features online for real-time use cases.
    - A **CLI** (folded into our existing ml-control or similar) to:
        - Inspect feature family status (frontiers, lags).
        - Possibly create/approve new feature families or manage production transitions.
        - Serve as a single pane for administrative tasks.
4. **Storage Layers**
    - **Redis** for fast, in-memory storage of the “latest” partial aggregates (per-entity).
    - **S3 (Express One-Zone)** for cost-effective, large-scale persistence of historical data, appended in an immutable log structure.
    - Combined “[LSM](https://en.wikipedia.org/wiki/Log-structured_merge-tree)-like” approach so that a small block of recent data lives in Redis, older data is flushed to S3, and queries for older timestamps are retrieved from S3.
5. **Workflow Orchestration**
    - **Temporal** to orchestrate asynchronous tasks (especially backfills and streaming ingestion).
    - Handles incremental updates from batch sources or row-by-row updates from Kafka.
    - Ensures we do not lose track of the ingestion progress and can recover from errors gracefully.

# 4. Data Model and Feature Definitions

## 4.1 Event Stream Format

Each feature family is backed by a table or stream of “events” that must meet a few conditions:

- **Timestamp Column** (ts): The time at which this event occurred (not the time it was ingested).
- **Entity Key Column(s)** (user_id, supplier_id, etc.): Identifies the entity these events belong to.
- **Additional Columns** (price_cents, quantity, etc.): The raw data fields used to compute features.
- **Immutability**: For a given event at timestamp t, its fields must not change in the future. (Prevents data leakage issues.)

This can come from:

- A **batch** source (e.g., Snowflake table).
- A **streaming** source (e.g., Kafka) for near real-time ingestion.

## 4.2 Defining a Feature Family

A **Feature Family** is specified via a config that can be defined in two ways:

1. **Interactive Development via SDK**: During development and experimentation, data scientists can define feature families using the SDK, typically as Python dictionaries. This is the most natural format for interactive work in notebooks.
2. **Production via Features Repository**: For production features, definitions are stored in a separate "features repo" as YAML files. This repo serves as the source of truth for all production feature families.

Both approaches specify the same core components:

- **Name** (e.g., "orders").
- **Version**
- **SQL** or logic describing how to select/format raw data into the required event stream shape. (For batch sources, typically a SQL query. For Kafka, a mapping from the raw message to the event schema.)
- **Entity Identifier** (e.g., user_id).
- **Timestamp Column** (the logical event timestamp).
- **Feature Columns & Aggregations**: For each raw column we want to transform, we specify which aggregations to compute. Examples:
    - Sum
    - Count (over the events)
    - Average
    - Possibly standard deviation
    - (Distinct count, median, or rank-based features are **not** directly supported by this approach without approximation/probabilistic data structures.)

**Example** (pseudo-YAML):

```yaml
name: orders
version: 1
source:
  type: BATCH
  query: >
    SELECT
      event_id AS id,
      user_id,
      timestamp AS ts,
      amount_cents
    FROM raw.purchase_flows
features:
  - column: amount_cents
    aggregations: [sum, avg]
  - column: any_field
    aggregations: [count]
```

From these definitions, the system will:

1. Create an immutable appended stream of events in the feature platform’s internal representation.
2. Maintain partial aggregates row-by-row.

## 4.3 Draft vs. Production Mode

The feature platform supports two modes for feature families:

- **Draft Mode**:
    - Created interactively via the SDK in notebooks
    - Enables rapid experimentation and iteration
    - **Limitations**:
        - Cannot be used for online inference (API requests are blocked)
        - May have limited observability compared to production features
        - No guarantee of long-term persistence
- **Production Mode**:
    - Created through a PR process to the features repository
    - Requires code review for quality control and awareness
    - Provides full functionality including online inference

**Workflow**:

1. Data scientists develop and test features interactively in notebooks using the SDK, creating draft feature families
2. Once satisfied with the features, they create a PR to the features repo with the YAML definition
3. After review and approval, the PR is merged and a GitHub action automatically creates or updates the production feature family
4. If a feature family already exists in draft mode and is then created in production mode, it's automatically upgraded to production status

## 4.4 Windowed vs. Unwindowed

- **Windowed** features (like count of orders in the past 4 weeks) are computed at query-time by subtracting partial aggregates at the start and end of the window.
- **Unwindowed** features (like a lifetime total) are just the cumulative partial aggregates up to the requested time.

# 5. Partial Aggregation Approach

Instead of repeatedly computing, e.g., “sum of amounts in the last month” from scratch, the platform keeps a “running sum” in each appended event row. This approach:

1. Processes each event in chronological order.
2. Updates the partial aggregates (the “state”) for that entity ID by referencing the previous row’s state + the new event’s value.
3. Save the updated row.

To fetch a windowed feature (e.g., “sum over the last 7 days”), we:

1. Identify the partial aggregate at the **end** timestamp.
2. Identify the partial aggregate at the **start** timestamp (7 days prior).
3. Subtract them: `sum_end - sum_start`.

This is highly efficient for many standard aggregations (count, sum, average, standard deviation, etc.). Complex or non-associative aggregations (like exact median) are not trivially supported; approximations or alternative data structures could be layered in if needed.

# 6. Storage Strategy

We use a combined **Redis + S3** (Express One-Zone) storage scheme with CSV as the primary storage format:

1. [**Redis**](https://redis.io/)
    - Holds the most recent block of events per entity ID as raw strings in CSV format
    - Provides extremely low-latency (sub-ms) for online queries
    - Cost constraints require careful management of Redis data volume
    - We keep a *bounded* number of rows in Redis (e.g., the most recent 10-20 events for each entity)
2. [**S3 (Express One-Zone)**](https://aws.amazon.com/s3/storage-classes/express-one-zone/)
    - Stores older, immutable "blocks" of these appended logs in CSV format
    - Single-digit millisecond latencies are typical, with tail latencies ~20ms under load
    - Infinitely scalable for historical data
    - Cheaper storage compared to in-memory or even DynamoDB
    - Less durable than multi-zone S3, but acceptable for derived data

## 6.1 Data Format and Encoding

We use CSV (Comma-Separated Values) as our primary storage format for several reasons:

- **Space Efficiency**: CSV is significantly more space-efficient than JSON or other formats with high overhead, which is critical for optimizing Redis storage costs
- **Append-Friendly**: Unlike formats like Parquet or Arrow, CSV allows simple appending without reading and rewriting entire files
- **Human Readability**: Makes debugging easier as data can be directly inspected
- **Performance**: Many optimized CSV readers exist, making parsing fast even for large blocks

Each entity's data is stored as a series of timestamped rows with partial aggregates. The same format is used in both Redis and S3, allowing the same code to operate on data regardless of where it's stored.

## 6.2 Redis Flushing Strategy

A critical aspect of the system is determining when to flush data from Redis to S3:

- **Trade-offs**:
    - More data in Redis means faster queries but higher costs
    - Less data in Redis means more S3 fetches, which have higher latency
- **Per-Entity Optimization**:
    - The system keeps a configurable number of the most recent rows per entity in Redis
    - For popular feature families (like orders), keeping 10-20 rows per entity might cover 95% of queries
    - This minimizes S3 fetches for common window sizes
- **Triggering Flush**:
    - Flushes can be triggered based on:
        - Number of rows per entity exceeding a threshold
        - Total size of data per entity exceeding a threshold
        - Time since the oldest row in Redis
- **Parameterized Approach**:
    - All thresholds are configurable per feature family
    - Will be refined based on performance testing and real-world usage patterns
    - System designed to allow adjusting these parameters without code changes

Performance testing and monitoring will guide the optimization of these parameters to balance cost and performance.

## 6.3 Query Flow

When an online or offline request asks for feature values:

1. Check Redis for the relevant entity’s latest partial aggregates.
2. If the requested time window extends further back, consult the block metadata to fetch blocks from S3.
3. Perform a quick binary search to find the correct partial aggregates for the desired timestamps.
4. Compute any needed window-based difference (e.g., sum or count) and return.

This approach avoids heavy scanning of large data sets and fetches only the minimal block from S3 needed to answer the query.

## 6.4 Data Retention and Cleanup

- **Data Retention Policy**: By default, keep ~1 year of data (configurable). We may provide admin tools to prune older blocks from S3.
- **Draft Families**: Data can be discarded quickly if the user never promotes to production or if the draft is abandoned.
- **Deletion**: Production families can only be removed or truncated under strict governance to avoid breaking dependent models.

# 7. Orchestration with [Temporal](https://temporal.io/)

## 7.1 Why Temporal?

Historically, systems like [Celery](https://docs.celeryq.dev/en/stable/) (Python) or [Airflow](https://airflow.apache.org/) are used to schedule ingestion jobs. These can drop tasks or require complicated “catch-up” logic. Temporal provides a robust, developer-friendly framework for “durable workflows”:

- Each workflow is just normal code (Python, Go, etc.) with built-in guarantees around replay, retries, states, and fault tolerance.
- If a worker node fails mid-execution, Temporal replays events so the workflow picks up without losing state.
- Great fit for batch increments, long-lived streaming ingestion, error handling and restarts.

## 7.2 Workflows for Batch Ingestion

- **Incremental Backfill**: After the initial large backfill, define a periodic workflow (e.g., runs hourly) that grabs new rows from Snowflake with timestamps > last frontier, updates partial aggregates, and appends to Redis/S3.
- Use Temporal’s state to track the “frontier” (the last time/timestamp ingested) so it never loses place if the workflow is interrupted.

## 7.3 Workflows for Streaming Ingestion

- Each feature family that uses Kafka or other real-time sources can have a streaming workflow.
- The workflow reads messages offset by offset, updating partial aggregates in near real-time. If a failure occurs, it resumes from the stored offset.
- Since we only need perfect ordering per entity, we can partition data by entity ID (Kafka partitions) if we outgrow single-thread throughput.

# 8. Feature Lifecycle

1. **Draft Feature Families**
    - Created via the SDK in a “draft” mode, enabling experimentation (usually offline only).
    - Might not do incremental ingestion or be restricted from online usage until “promoted” to production.
2. **Production Feature Families**
    - Once a code review or PR merges to the main config repo, the system stands them up fully (batch or streaming ingestion, partial aggregates, etc.).
    - Potentially require an approval flow to ensure no destructive changes to widely used features.
3. **Deletion**
    - Draft features can be deleted freely.
    - Production features may require admin permission to remove if any production model depends on them.

# 9. Phased Rollout Plan

## **Phase 1: Offline + Online Inference**

### 1.1 Offline **Inference**

- Implement the service, partial aggregation logic, Redwood + S3 push, an initial SDK interface.
- Limit data ingestion to batch sources (Snowflake).
- Provide offline feature fetch for training and batch inference.
- Deploy first use cases (e.g., Fraud model or Ticket Delivery model) that benefit from simpler features.

### **1.2: Online Inference**

- Enable an online fetch endpoint (gRPC) returning up-to-date features.
- Integrate with relevant services for real-time scoring (fraud checks, curated pricing, etc.).
- Put partial aggregates in Redis for immediate lookups.

## **Phase 2: Real-Time Streaming**

- Add streaming ingestion from Kafka for time-sensitive features.
- Orchestrate the “backfill plus continuous streaming” pattern with Temporal.
- Empower advanced use cases that need sub-minute fresh signals (e.g., real-time user actions).

## **Phase 3: Advanced Features & Tooling**

- Consider approximate distinct or quantile features if needed.
- Add improved Observability (web UI, better dashboards, more metadata surfacing).
- Potential expansions to multi-tenant or multi-zone setups if needed.

# 10. Observability and Operations

## 10.1 [Four Golden Signals](https://sre.google/sre-book/monitoring-distributed-systems/#xref_monitoring_golden-signals)

Monitor **latency, traffic, errors,** and **saturation** for both ingestion pipelines and online queries:

- **Latency**: Time to ingest and serve features (track both average and high-percentile latencies).
- **Traffic**: Volume of incoming events and request rates for online inference.
- **Errors**: Ingestion failures, workflow exceptions, or gRPC 5xx responses.
- **Saturation**: Resource usage (Redis memory, S3 throughput, CPU/memory on workers).

### 10.2 Monitoring Strategy

- **Symptom-Oriented Alerts**: Page on user-facing issues (e.g., ingestion lag beyond X hours, or 99th percentile fetch latency above SLA), rather than internal causes like a single node’s CPU spike.
- **Black-Box and White-Box**: Use synthetic checks (black-box) for end-to-end validation, and gather detailed internal metrics (white-box) for diagnosing root causes.
- **Dashboards**: Provide high-level views of ingestion lag, request latency, resource usage, and error rates. Allow drill-down into details (workflow queues, aggregator stats, etc.).

### 10.3 Alerting Principles

- **Actionable Pages**: Only page when immediate human intervention is needed. Use Slack for non-urgent notifications.
- **Minimize Noise**: Constantly review alerts that rarely fire or never prompt action. Remove or adjust them to reduce page fatigue.
- **Continuous Improvement**: Refine thresholds, improve coverage, and automate fixes whenever possible.

### 10.4 Observability Tooling

- **Datadog**
    - Expose counters on ingestion rates, Redis usage, S3 latencies, tail-latency metrics, etc.
    - Use open standards (e.g., [OpenTelemetry](https://opentelemetry.io/)) to avoid vendor lock-in and keep instrumentation portable.
- **Temporal Visibility**: Use Temporal’s UI and metrics to observe workflow health and ingestion progress.
- **Frontier Tracking**: Monitor each feature family’s frontier (most recent ingested timestamp) for lags and backpressure.

---

# Appendix

# Outstanding Questions and Next Steps

1. **Redis Flushing Strategy**:
    - Need to determine optimal thresholds for flushing data from Redis to S3 (will require experimentation and performance testing)
2. **Performance Testing**:
    - Benchmark the combination of Redis + S3 queries under concurrency
    - Understand tail latencies for large partitions
    - Validate that the partial-aggregation approach and block retrieval from S3 meets our real-time SLA
3. **Open Source Potential**:
    - Design with open source potential in mind
    - Avoid hardcoding organization-specific secrets or references
    - Use clean abstractions that would facilitate open-sourcing in the future
    - Consider creating the feature platform as a separate module that could be extracted later
