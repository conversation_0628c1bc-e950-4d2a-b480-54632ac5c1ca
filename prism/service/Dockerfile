FROM python:3.11-slim-bookworm

ENV SHELL=/bin/bash \
    PORT=8888 \
    PATH="/root/.local/bin:$PATH" \
    UV_COMPILE_BYTECODE=1 \
    UV_SYSTEM_PYTHON=1 \
    UV_LINK_MODE=copy

ARG UV_INDEX_CODEARTIFACT_USERNAME
ARG UV_INDEX_CODEARTIFACT_PASSWORD

ENV UV_INDEX_CODEARTIFACT_USERNAME=$UV_INDEX_CODEARTIFACT_USERNAME
ENV UV_INDEX_CODEARTIFACT_PASSWORD=$UV_INDEX_CODEARTIFACT_PASSWORD

# Copy uv from the official distroless image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

RUN --mount=type=cache,target=/var/cache/apt \
    --mount=type=cache,target=/var/lib/apt \
    apt-get update && \
    apt-get install -y ffmpeg \
                       openssh-client \
                       git \
                       gfortran \
                       libsm6 \
                       libxext6 \
                       zlib1g-dev \
                       libblas-dev \
                       liblapack-dev \
                       libjpeg-dev \
                       curl \
                       ca-certificates && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY pyproject.toml README.md ./

RUN --mount=type=ssh \
    --mount=type=cache,target=/root/.cache/uv \
    mkdir -p -m 0700 ~/.ssh && \
    ssh-keyscan github.com >> ~/.ssh/known_hosts && \
    uv pip install --no-cache-dir -e . --index-strategy unsafe-best-match

COPY src ./src/

CMD ["prism"]
