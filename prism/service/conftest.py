import os
import sys


sys.path.append("src")


# Import Snowflake configuration for tests
from tests.config.snowflake_config import SNOWFLAKE_URL

# we need to set these so that the config module can be imported without raising
os.environ.update(
    {
        "REDIS_HOST": "localhost",
        "REDIS_PORT": "1234",
        "TEMPORAL_HOST": "localhost",
        "TEMPORAL_PORT": "7233",
        "SOURCE_BATCH_KIND": "snowflake",
        "SOURCE_BATCH_URL": SNOWFLAKE_URL,
        "S3_BUCKET": "testing",
    }
)
