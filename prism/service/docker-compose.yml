services:
  postgresql:
    image: postgres:12
    environment:
      POSTGRES_PASSWORD: temporal
      POSTGRES_USER: temporal
    ports:
      - "5432:5432"
    volumes:
      - /var/lib/postgresql/data

  redis:
    image: redislabs/redismod:latest
    ports:
      - "6379:6379"

  temporal:
    image: temporalio/auto-setup:********
    depends_on:
      - postgresql
    environment:
      - DB=postgres12
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=postgresql
    ports:
      - "7233:7233"

  temporal-ui:
    image: temporalio/ui:2.36.1
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:8080
    ports:
      - "8080:8080"

  minio:
    image: minio/minio
    ports:
      - "9000:9000"  # Server port
      - "9001:9001"  # Console port
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"

  prism-service:
    image: prism-service:latest
    build:
      context: .
      dockerfile: Dockerfile
      args:
        UV_INDEX_CODEARTIFACT_USERNAME: ${UV_INDEX_CODEARTIFACT_USERNAME}
        UV_INDEX_CODEARTIFACT_PASSWORD: ${UV_INDEX_CODEARTIFACT_PASSWORD}
    depends_on:
      - redis
      - temporal
      - minio
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      TEMPORAL_HOST: temporal
      TEMPORAL_PORT: 7233
      SOURCE_BATCH_KIND: snowflake
      SOURCE_BATCH_URL: ${SNOWFLAKE_URL:-snowflake://user:pass@account/db/schema?warehouse=wh}
      S3_BUCKET: testing
      S3_ENDPOINT_URL: http://minio:9000
      AWS_ACCESS_KEY_ID: minioadmin
      AWS_SECRET_ACCESS_KEY: minioadmin
      PYTHONPATH: /app
      LOGGING_LEVEL: INFO
    ports:
      - "8888:8888"
    command: ["prism"]
