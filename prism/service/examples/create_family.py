import grpc
import time
import logging
import argparse
import os
from google.protobuf.wrappers_pb2 import StringValue
from gametime_protos.mlp.prism.v1.service_pb2 import (
    FamiliesServiceCreateRequest,
    FamiliesServiceListRequest,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
)
from gametime_protos.mlp.prism.v1.service_pb2_grpc import FamiliesServiceStub

# Set up logging
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Families Service Client")
    parser.add_argument(
        "--host",
        default=os.environ.get("SERVICE_HOST", "localhost"),
        help="Service host (default: from SERVICE_HOST env var or localhost)",
    )
    parser.add_argument(
        "--port",
        default=os.environ.get("SERVICE_PORT", "8888"),
        help="Service port (default: from SERVICE_PORT env var or 8888)",
    )
    return parser.parse_args()


def main():
    args = parse_args()
    service_address = f"{args.host}:{args.port}"

    # Connect to the exposed service
    logger.info(f"Connecting to service at {service_address}")
    channel = grpc.insecure_channel(service_address)
    stub = FamiliesServiceStub(channel)

    # Initial list to show empty state
    logger.info("Initial state:")
    initial_response = stub.List(FamiliesServiceListRequest())
    logger.info(f"Found {len(initial_response.families)} families")

    logger.info("Creating test family...")

    # Create each component separately
    batch_config = BatchSourceConfig(
        table="test_table", late_arriving_data_lag_seconds=3600
    )

    # For StringValue wrapper
    query_wrapper = StringValue(value="SELECT * FROM {{ ref }}")

    source_config = SourceConfig(batch=batch_config, query=query_wrapper)

    feature_config = FeatureConfig(
        column="value",
        aggregations=[1],  # COUNT
    )

    family_config = FamilyConfig(
        source=source_config,
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[feature_config],
    )

    create_request = FamiliesServiceCreateRequest(
        name="test_family", config=family_config
    )

    try:
        stub.Create(create_request)
        logger.info("Family created successfully!")
    except Exception as e:
        logger.error(f"Error creating family: {e}")
        exit(1)

    # List families immediately after creation
    logger.info("After creation:")
    after_create_response = stub.List(FamiliesServiceListRequest())
    logger.info(f"Found {len(after_create_response.families)} families:")
    for family in after_create_response.families:
        logger.info(f"- {family.name} (Status: {family.status})")

    # Wait a bit for workflows to process
    logger.info("Waiting 10 seconds for workflows to process...")
    time.sleep(10)

    # List families again to see status updates
    logger.info("After waiting:")
    final_response = stub.List(FamiliesServiceListRequest())
    logger.info(f"Found {len(final_response.families)} families:")
    for family in final_response.families:
        logger.info(f"- {family.name} (Status: {family.status})")


if __name__ == "__main__":
    main()
