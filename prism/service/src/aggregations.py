from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import IntEnum
from functools import cached_property
from typing import Any

import pyarrow as pa
import pyarrow.compute as pc


class AggregationFunction(IntEnum):
    """
    Enumeration of supported aggregation function types.

    These values are used to map between function identifiers and their
    implementations in the aggregation registry.
    """

    AGGREGATION_FUNCTION_UNSPECIFIED = 0
    AGGREGATION_FUNCTION_COUNT = 1
    AGGREGATION_FUNCTION_AVG = 2
    AGGREGATION_FUNCTION_SUM = 3
    AGGREGATION_FUNCTION_STDDEV = 4


class Aggregation(ABC):
    """
    Abstract base class for feature aggregation functions.

    Defines the interface for all aggregation implementations, including
    methods for accumulating values, computing final results, and calculating
    windowed aggregations between two states.
    """

    SHORT_NAME: str = "base"

    @staticmethod
    @abstractmethod
    def accumulate(value: float | None, state: Any) -> Any:
        """
        Update aggregation state with a new value.

        Args:
            value: The new value to incorporate into the aggregation state
            state: The current aggregation state

        Returns:
            Updated aggregation state after incorporating the new value
        """
        pass

    @staticmethod
    @abstractmethod
    def compute(state: Any) -> Any:
        """
        Compute the final aggregation result from the current state.

        Args:
            state: The current aggregation state

        Returns:
            The computed aggregation result
        """
        pass

    @staticmethod
    @abstractmethod
    def compute_windowed(start_state: Any, end_state: Any) -> Any:
        """
        Compute a windowed aggregation between two states.

        Args:
            start_state: The aggregation state at the start of the window
            end_state: The aggregation state at the end of the window

        Returns:
            The computed windowed aggregation result
        """
        pass


class CountAggregation(Aggregation):
    """
    Aggregation implementation for counting non-null values.

    Maintains a simple counter that increments for each non-null value.
    """

    SHORT_NAME: str = "count"

    @staticmethod
    def accumulate(value: float | None, state: tuple[int] | None) -> tuple[int]:
        """
        Update count state with a new value. Handles None state input.

        Args:
            value: The new value to count (if not None)
            state: The current count state as a tuple (int,) or None for initial state.

        Returns:
            Updated count state (new_count,)
        """
        # If the input state is None, use the default initial state (0,)
        current_state = state if state is not None else (0,)

        previous_count = current_state[0]
        new_count = previous_count if value is None else previous_count + 1
        return (new_count,)

    @staticmethod
    def compute(count: pa.Array) -> pa.Array:
        """
        Compute the final count, replacing nulls with 0.

        Args:
            count: Arrow array of count values

        Returns:
            Arrow array with nulls replaced by 0
        """
        return pc.coalesce(count, 0)

    @staticmethod
    def compute_windowed(start_count: pa.Array, end_count: pa.Array) -> pa.Array:
        """
        Compute the difference between end and start counts for a window.

        Args:
            start_count: Arrow array of count values at window start
            end_count: Arrow array of count values at window end

        Returns:
            Arrow array of differences between end and start counts
        """
        coalesced_start_count = pc.coalesce(start_count, 0)
        coalesced_end_count = pc.coalesce(end_count, 0)
        return pc.subtract(coalesced_end_count, coalesced_start_count)


def compute_windowed_average(
    start_average: pa.Array,
    start_count: pa.Array,
    end_average: pa.Array,
    end_count: pa.Array,
) -> pa.Array:
    """
    Compute a windowed average between two states.

    Uses a weighted calculation to derive the average within a window
    based on the averages and counts at window boundaries.

    Args:
        start_average: Arrow array of average values at window start
        start_count: Arrow array of count values at window start
        end_average: Arrow array of average values at window end
        end_count: Arrow array of count values at window end

    Returns:
        Arrow array of computed windowed averages, with null values
        when the count difference is zero (no events in window)
    """
    coalesced_start_count = pc.coalesce(start_count, 0)
    coalesced_start_average = pc.coalesce(start_average, 0.0)
    coalesced_end_count = pc.coalesce(end_count, 0)
    coalesced_end_average = pc.coalesce(end_average, 0.0)
    count_diff = pc.subtract(coalesced_end_count, coalesced_start_count)
    end_multiplier = pc.divide(coalesced_end_count.cast(pa.float64()), count_diff)
    start_multiplier = pc.divide(coalesced_start_count.cast(pa.float64()), count_diff)
    final = pc.subtract(
        pc.multiply(end_multiplier, coalesced_end_average),
        pc.multiply(start_multiplier, coalesced_start_average),
    )
    return pc.if_else(pc.equal(count_diff, 0), None, final)


class AverageAggregation(Aggregation):
    """
    Aggregation implementation for computing averages.

    Maintains running average and count for efficient incremental updates.
    """

    SHORT_NAME: str = "avg"

    @staticmethod
    def accumulate(
        value: float | None,
        state: tuple[float | None, int] | None,  # Allow None input
    ) -> tuple[float | None, int]:
        """
        Update average state with a new value. Handles None state input.

        Recalculates the running average and updates the count.

        Args:
            value: The new value to incorporate into the average
            state: Tuple of (current_average, count) or None for initial state.

        Returns:
            Updated state tuple (new_average, new_count)
        """
        # If the input state is None, use the default initial state (None, 0)
        current_state = state if state is not None else (None, 0)

        if value is None:
            # If value is None, just return the current (potentially initial) state unchanged
            return current_state
        else:
            # Now we know current_state is a tuple, and value is not None
            previous_average, previous_count = current_state
            # Handle potential None in previous_average from the initial state tuple
            previous_average = 0.0 if previous_average is None else previous_average
            new_count = previous_count + 1
            # Calculate new average using the potentially initialized previous values
            new_average = (previous_count / new_count) * previous_average + (
                value / new_count
            )
            return new_average, new_count

    @staticmethod
    def compute(average: pa.Array, _: pa.Array) -> pa.Array:
        """
        Compute the final average result.

        Args:
            average: Arrow array of average values
            _: Unused count array (kept for API consistency)

        Returns:
            The average array unchanged
        """
        return average

    @staticmethod
    def compute_windowed(
        start_average: pa.Array,
        start_count: pa.Array,
        end_average: pa.Array,
        end_count: pa.Array,
    ) -> pa.Array:
        """
        Compute a windowed average between two states.

        Args:
            start_average: Arrow array of average values at window start
            start_count: Arrow array of count values at window start
            end_average: Arrow array of average values at window end
            end_count: Arrow array of count values at window end

        Returns:
            Arrow array of computed windowed averages
        """
        return compute_windowed_average(
            start_average, start_count, end_average, end_count
        )


class SumAggregation(AverageAggregation):
    """
    Aggregation implementation for computing sums.

    Extends AverageAggregation to leverage its state management, but
    computes sums instead of averages from the stored state.
    """

    SHORT_NAME: str = "sum"

    @staticmethod
    def compute(average: pa.Array, count: pa.Array) -> pa.Array:
        """
        Compute the sum from average and count.

        Args:
            average: Arrow array of average values
            count: Arrow array of count values

        Returns:
            Arrow array of sums (average * count)
        """
        return pc.coalesce(pc.multiply(average, count), 0.0)

    @staticmethod
    def compute_windowed(
        start_average: pa.Array,
        start_count: pa.Array,
        end_average: pa.Array,
        end_count: pa.Array,
    ) -> pa.Array:
        """
        Compute a windowed sum between two states.

        Args:
            start_average: Arrow array of average values at window start
            start_count: Arrow array of count values at window start
            end_average: Arrow array of average values at window end
            end_count: Arrow array of count values at window end

        Returns:
            Arrow array of computed windowed sums
        """
        coalesced_start_count = pc.coalesce(start_count, 0)
        coalesced_start_average = pc.coalesce(start_average, 0.0)
        coalesced_end_count = pc.coalesce(end_count, 0)
        coalesced_end_average = pc.coalesce(end_average, 0.0)
        coalesced_end_sum = pc.multiply(coalesced_end_count, coalesced_end_average)
        multiplier = pc.multiply(coalesced_end_count, coalesced_start_count)
        diff = pc.subtract(
            pc.divide(coalesced_end_average, coalesced_start_count),
            pc.divide(coalesced_start_average, coalesced_end_count),
        )
        final = pc.multiply(multiplier, diff)
        return pc.if_else(pc.equal(coalesced_start_count, 0), coalesced_end_sum, final)


class StandardDeviationAggregation(Aggregation):
    """
    Aggregation implementation for computing standard deviations.

    Uses a numerically stable algorithm tracking both the average and
    average of squares to compute standard deviation.
    """

    SHORT_NAME: str = "stddev"

    @staticmethod
    def accumulate(
        value: float | None,
        state: tuple[float | None, float | None, int] | None,  # Allow None input
    ) -> tuple[float | None, float | None, int]:
        """
        Update standard deviation state with a new value. Handles None state input.

        Recalculates running average, average of squares, and count.

        Args:
            value: The new value to incorporate
            state: Tuple of (avg, avg_sq, count) or None for initial state.

        Returns:
            Updated state tuple (new_avg, new_avg_sq, new_count)
        """
        # If the input state is None, use the default initial state (None, None, 0)
        current_state = state if state is not None else (None, None, 0)

        if value is None:
            # If value is None, return the current (potentially initial) state unchanged
            return current_state
        else:
            previous_average, previous_average_of_square, previous_count = current_state
            previous_average = 0.0 if previous_average is None else previous_average
            previous_average_of_square = (
                0.0
                if previous_average_of_square is None
                else previous_average_of_square
            )
            new_count = previous_count + 1
            new_average = (previous_count / new_count) * previous_average + (
                value / new_count
            )
            new_average_of_square = (
                previous_count / new_count
            ) * previous_average_of_square + (value**2 / new_count)
            return new_average, new_average_of_square, new_count

    @staticmethod
    def compute(
        average: pa.Array, average_of_square: pa.Array, _: pa.Array
    ) -> pa.Array:
        """
        Compute the standard deviation from current state.

        Args:
            average: Arrow array of average values
            average_of_square: Arrow array of average of squares values
            _: Unused count array (kept for API consistency)

        Returns:
            Arrow array of computed standard deviations
        """
        return pc.sqrt(pc.subtract(average_of_square, pc.power(average, 2)))

    @staticmethod
    def compute_windowed(
        start_average: pa.Array,
        start_average_of_square: pa.Array,
        start_count: pa.Array,
        end_average: pa.Array,
        end_average_of_square: pa.Array,
        end_count: pa.Array,
    ) -> pa.Array:
        """
        Compute a windowed standard deviation between two states.

        Args:
            start_average: Arrow array of average values at window start
            start_average_of_square: Arrow array of average of squares at window start
            start_count: Arrow array of count values at window start
            end_average: Arrow array of average values at window end
            end_average_of_square: Arrow array of average of squares at window end
            end_count: Arrow array of count values at window end

        Returns:
            Arrow array of computed windowed standard deviations
        """
        average = compute_windowed_average(
            start_average, start_count, end_average, end_count
        )
        average_of_square = compute_windowed_average(
            start_average_of_square, start_count, end_average_of_square, end_count
        )
        return pc.sqrt(pc.subtract(average_of_square, pc.power(average, 2)))


@dataclass
class AggregationDescriptor:
    """
    Descriptor class for defining a specific aggregation on a column.

    Combines a column name with an aggregation function to fully specify
    a feature calculation within a feature family.
    """

    column: str
    aggregation: type[Aggregation]

    KEY_PREFIX: str = "agg"

    @cached_property
    def state_key(self) -> str:
        """
        Generate a unique key for storing this aggregation's state.

        Returns:
            A string key in the format "agg_{column}_{aggregation_short_name}"
        """
        return f"{self.KEY_PREFIX}_{self.column}_{self.aggregation.SHORT_NAME}"


# Map of aggregation function enum values to their implementations
REGISTRY: dict[AggregationFunction, type[Aggregation]] = {
    AggregationFunction.AGGREGATION_FUNCTION_AVG: AverageAggregation,
    AggregationFunction.AGGREGATION_FUNCTION_COUNT: CountAggregation,
    AggregationFunction.AGGREGATION_FUNCTION_STDDEV: StandardDeviationAggregation,
    AggregationFunction.AGGREGATION_FUNCTION_SUM: SumAggregation,
}
