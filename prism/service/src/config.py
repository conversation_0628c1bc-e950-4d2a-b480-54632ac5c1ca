from dataclasses import dataclass, field

from mlutils.dataclasses import from_env

from mlutils.utils import remove_none_values


@dataclass
class RedisConfig:
    host: str
    port: int


@dataclass
class TemporalConfig:
    host: str
    port: int

    @property
    def url(self):
        return f"{self.host}:{self.port}"


@dataclass
class BatchSourceConfig:
    kind: str
    url: str


@dataclass
class SourceConfig:
    batch: BatchSourceConfig


@dataclass
class S3Config:
    bucket: str
    endpoint_url: str | None = None
    aws_access_key_id: str | None = None
    aws_secret_access_key: str | None = None

    @property
    def settings(self):
        return remove_none_values(
            {
                "endpoint_url": self.endpoint_url,
                "aws_access_key_id": self.aws_access_key_id,
                "aws_secret_access_key": self.aws_secret_access_key,
            }
        )


@dataclass
class LoggingConfig:
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    json: bool = False


@dataclass
class Config:
    redis: RedisConfig
    temporal: TemporalConfig
    source: SourceConfig
    s3: S3Config
    logging: LoggingConfig = field(default_factory=LoggingConfig)


config = from_env(Config)
