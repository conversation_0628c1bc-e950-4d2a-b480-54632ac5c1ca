import io
import json
import math
import logging
from typing import Any
import csv
from datetime import datetime, timezone

from src.families import Family

logger = logging.getLogger(__name__)


def get_csv_column_order(family: Family, row: dict[str, Any] = None) -> list[str]:
    """Derives column order based ONLY on family definition for consistency."""
    state_keys = sorted([desc.state_key for desc in family.aggregation_descriptors])
    base_cols = [family.config.timestamp_column, family.config.id_column] + sorted(
        family.config.identifier_columns
    )
    feature_source_cols = sorted(list({fc.column for fc in family.config.features}))
    ordered_columns = base_cols + feature_source_cols + state_keys
    seen = set()
    final_columns = [x for x in ordered_columns if not (x in seen or seen.add(x))]
    return final_columns


def format_row_to_csv_string(
    row_dict: dict[str, Any], column_order: list[str]
) -> str | None:
    """Formats a dict row to a CSV string line."""
    values = []
    try:
        for col in column_order:
            value = row_dict.get(col)
            if isinstance(value, datetime):
                formatted_value = value.isoformat()
            elif isinstance(value, (list, tuple)):
                try:
                    serializable_tuple = tuple(
                        (
                            item
                            if not (isinstance(item, float) and math.isnan(item))
                            else None
                        )
                        for item in value
                    )
                    formatted_value = json.dumps(serializable_tuple)
                except TypeError:
                    formatted_value = str(value)
            elif value is None:
                formatted_value = ""
            else:
                formatted_value = str(value)
            formatted_value = formatted_value.replace('"', '""')
            if (
                "," in formatted_value
                or '"' in formatted_value
                or "\n" in formatted_value
            ):
                formatted_value = f'"{formatted_value}"'
            values.append(formatted_value)
        return ",".join(values)
    except Exception as e:
        logger.error(
            f"Error formatting row to CSV string: {e}, Row: {row_dict}", exc_info=True
        )
        return None


def parse_csv_string_to_row(
    csv_string: str, column_order: list[str], family: Family
) -> dict[str, Any] | None:
    """Parses a single CSV string line back into a dictionary."""
    try:
        reader = csv.reader(io.StringIO(csv_string), quoting=csv.QUOTE_MINIMAL)
        values = next(reader)
    except StopIteration:
        return {col: None for col in column_order}
    except Exception as e:
        logger.warning(f"Failed to read CSV line '{csv_string[:100]}...': {e}")
        return None

    row_dict = {}
    ts_col = family.config.timestamp_column
    state_prefixes = ("agg_",)

    if len(values) != len(column_order):
        values.extend([None] * (len(column_order) - len(values)))
        if len(values) > len(column_order):
            logger.warning(
                f"CSV row has more values ({len(values)}) than expected columns ({len(column_order)}). Truncating. Line: '{csv_string[:100]}...'"
            )
            values = values[: len(column_order)]

    for i, col_name in enumerate(column_order):
        val_str = values[i]
        if val_str is None or val_str == "":
            row_dict[col_name] = None
        elif col_name == ts_col:
            try:
                dt_str = val_str.replace("Z", "+00:00")
                dt_obj = datetime.fromisoformat(dt_str)
                if dt_obj.tzinfo is None:
                    dt_obj = dt_obj.replace(tzinfo=timezone.utc)
                else:
                    dt_obj = dt_obj.astimezone(timezone.utc)
                row_dict[col_name] = dt_obj
            except (ValueError, TypeError):
                logger.warning(
                    f"Could not parse timestamp '{val_str}' for column '{col_name}'. Storing as string."
                )
                row_dict[col_name] = val_str
        elif (
            col_name.startswith(state_prefixes)
            and val_str.startswith(("[", "("))
            and val_str.endswith(("]", ")"))
        ):
            try:
                parsed_json = json.loads(val_str)
                row_dict[col_name] = (
                    tuple(parsed_json) if isinstance(parsed_json, list) else parsed_json
                )
            except (json.JSONDecodeError, TypeError):
                logger.warning(
                    f"Could not JSON decode state value '{val_str}' for column '{col_name}'. Storing as string."
                )
                row_dict[col_name] = val_str
        else:
            try:
                row_dict[col_name] = int(val_str)
            except (ValueError, TypeError):
                try:
                    row_dict[col_name] = float(val_str)
                except (ValueError, TypeError):
                    row_dict[col_name] = val_str

    return row_dict
