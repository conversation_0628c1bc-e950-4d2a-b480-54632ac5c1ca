import asyncio
import logging
import os
import signal
from contextlib import AsyncExitStack

from redis.asyncio import Redis
from mlutils.grpc import Service

from src.config import config
from src.families import Registry
from src.servicers.families import (
    Servicer as FamiliesServicer,
    register_servicer as register_families_servicer,
)
from src.servicers.offline_features import (
    Servicer as OfflineFeaturesServicer,
    register_servicer as register_offline_features_servicer,
)
from src.servicers.online_features import (
    Servicer as OnlineFeaturesServicer,
    register_servicer as register_online_features_servicer,
)
from src.temporal import ClientWrapper

from src.stores import OnlineStore, OfflineStore
from src.worker import create_worker


async def shutdown(signal_name: str, loop: asyncio.AbstractEventLoop) -> None:
    """Gracefully shutdown both server and worker"""
    logging.info(f"Received {signal_name}, shutting down")
    running_tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
    logging.info(f"Cancelling {len(running_tasks)} running tasks...")
    [task.cancel() for task in running_tasks]
    await asyncio.sleep(1)
    cancellation_results = await asyncio.gather(*running_tasks, return_exceptions=True)
    logging.debug(f"Gather results after cancellation: {cancellation_results}")
    if loop.is_running():
        loop.stop()
    logging.info("Shutdown complete.")


async def run_services() -> None:
    """Run both the gRPC server and Temporal worker"""
    async with AsyncExitStack() as stack:
        redis = await stack.enter_async_context(
            Redis(
                host=config.redis.host, port=config.redis.port, decode_responses=False
            )
        )
        redis_decoded = await stack.enter_async_context(
            Redis(host=config.redis.host, port=config.redis.port, decode_responses=True)
        )
        logging.info(f"Connected to Redis at {config.redis.host}:{config.redis.port}")

        temporal_wrapper = ClientWrapper(config.temporal.url)
        try:
            await temporal_wrapper.connect()
            logging.info(f"Connected to Temporal at {config.temporal.url}")
        except Exception as e:
            logging.error(f"Failed to connect to Temporal: {e}", exc_info=True)
            raise

        registry = Registry(redis_decoded)
        online_store = OnlineStore(redis)
        offline_store = OfflineStore(
            bucket_name=config.s3.bucket, s3_config=config.s3.settings
        )
        logging.info(f"Offline store configured for bucket '{config.s3.bucket}'")

        families_servicer = FamiliesServicer(registry, temporal_wrapper)
        offline_features_servicer = OfflineFeaturesServicer(
            registry, online_store, offline_store
        )
        online_features_servicer = OnlineFeaturesServicer(
            registry, online_store, offline_store
        )

        servicers = [
            (families_servicer, register_families_servicer),
            (offline_features_servicer, register_offline_features_servicer),
            (online_features_servicer, register_online_features_servicer),
        ]

        service = Service(servicers, on_startup=[], on_shutdown=[])

        worker = None
        worker_shutdown_handlers = []
        try:
            worker, worker_shutdown_handlers = await create_worker()
            logging.info("Temporal worker initialized.")
            stack.push_async_callback(worker.shutdown)
            for handler in worker_shutdown_handlers:
                if asyncio.iscoroutinefunction(handler):
                    stack.push_async_callback(handler)

        except Exception as e:
            logging.error(f"Failed to create Temporal worker: {e}", exc_info=True)
            raise

        port = os.environ.get("PORT", "8888")
        host_addr = os.environ.get("HOST", "127.0.0.1")

        logging.info(f"Starting gRPC service on {host_addr}:{port}")
        server_task = asyncio.create_task(service.serve(host=host_addr, port=port))
        stack.push_async_callback(service.stop)

        logging.info("Starting Temporal worker task.")
        worker_task = asyncio.create_task(worker.run())

        completed_tasks, still_running_tasks = await asyncio.wait(
            [server_task, worker_task],
            return_when=asyncio.FIRST_COMPLETED,
        )

        for task in completed_tasks:
            try:
                result = task.result()
                logging.info(
                    f"Task {task.get_name()} finished unexpectedly with result: {result}"
                )
            except asyncio.CancelledError:
                logging.info(f"Task {task.get_name()} was cancelled.")
            except Exception as e:
                logging.error(
                    f"Task {task.get_name()} failed unexpectedly: {e}", exc_info=True
                )


def main() -> None:
    from src.shared import setup_logging

    setup_logging()

    loop = asyncio.get_event_loop()
    for sig in (signal.SIGINT, signal.SIGTERM):
        try:
            loop.add_signal_handler(
                sig, lambda s=sig.name: asyncio.create_task(shutdown(s, loop))
            )
            logging.debug(f"Registered signal handler for {sig.name}")
        except ValueError as e:
            logging.warning(
                f"Could not set signal handler for {sig.name} (possibly running on Windows?): {e}"
            )

    try:
        logging.info("Starting event loop...")
        loop.run_until_complete(run_services())
    except asyncio.CancelledError:
        logging.info("Main coroutine cancelled.")
    except Exception as e:
        logging.critical(f"Unhandled exception in main loop: {e}", exc_info=True)
    finally:
        if not loop.is_closed():
            remaining_tasks = asyncio.all_tasks(loop=loop)
            if remaining_tasks:
                logging.info(
                    f"Gathering {len(remaining_tasks)} remaining tasks before closing loop..."
                )
                loop.run_until_complete(
                    asyncio.gather(*remaining_tasks, return_exceptions=True)
                )
            logging.info("Closing event loop.")
            loop.close()
            logging.info("Event loop closed.")
        logging.info("Service shut down.")


if __name__ == "__main__":
    main()
