import asyncio
import logging
from datetime import date as DateType

import polars as pl
from temporalio import activity

from src.families import Registry, Family
from src.stores import OnlineStore, OfflineStore, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
from src.shared import FlushDetails
from src.utils import calculate_partition
from src.csv_utils import parse_csv_string_to_row

logger = logging.getLogger(__name__)
activity_logger = activity.logger


class FamilyPersistenceActivities:
    """Activities related to moving data between storage tiers."""

    def __init__(
        self,
        registry: Registry,
        online_store: OnlineStore,
        offline_store: OfflineStore,
    ):
        self.registry = registry
        self.online_store = online_store
        self.offline_store = offline_store

    @activity.defn
    async def flush_entity_redis_to_s3(self, details: FlushDetails) -> None:
        """
        Flushes older rows for a specific entity from Redis (OnlineStore)
        to S3 (OfflineStore).
        """
        family_name = details.family_name
        packed_identifiers = details.packed_identifiers
        activity_logger.info(
            f"Starting flush activity for entity '{packed_identifiers}' in family '{family_name}'"
        )

        try:
            family: Family = await self.registry.fetch_one(family_name)
            if not family.pipeline_settings:
                activity_logger.error(
                    f"Cannot flush entity '{packed_identifiers}' for family '{family_name}': Missing pipeline settings."
                )
                return

            redis_key = self.online_store._key_for(family, packed_identifiers)
            current_length = await self.online_store.redis.llen(redis_key)

            if current_length <= MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS:
                activity_logger.info(
                    f"No rows to flush for entity '{packed_identifiers}' (length {current_length} <= {MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS})."
                )
                return

            start_index_to_fetch = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
            end_index_to_fetch = -1
            num_to_flush = current_length - start_index_to_fetch

            activity_logger.info(
                f"Entity '{packed_identifiers}' has {current_length} rows. Flushing oldest {num_to_flush} rows (indices {start_index_to_fetch} to {end_index_to_fetch})."
            )

            rows_to_flush_bytes = await self.online_store.redis.lrange(
                redis_key, start_index_to_fetch, end_index_to_fetch
            )

            if not rows_to_flush_bytes:
                activity_logger.warning(
                    f"Expected to fetch {num_to_flush} rows for entity '{packed_identifiers}', but got none."
                )
                await self.online_store.redis.ltrim(
                    redis_key, 0, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 1
                )
                activity_logger.info(
                    f"Performed defensive trim for entity '{packed_identifiers}'."
                )
                return

            parsed_rows = []
            column_order = self.online_store._derive_column_order_for_read(family)
            if not column_order:
                activity_logger.error(
                    f"Could not derive column order for family {family.name}, cannot parse rows."
                )
                raise ValueError(f"Column order derivation failed for {family.name}")

            for i, csv_bytes in enumerate(rows_to_flush_bytes):
                try:
                    decoded_string = csv_bytes.decode("utf-8")
                    parsed_dict = parse_csv_string_to_row(
                        decoded_string, column_order, family
                    )
                    if parsed_dict:
                        parsed_rows.append(parsed_dict)
                except Exception as e:
                    activity_logger.warning(
                        f"Failed to parse row {i} for entity '{packed_identifiers}': {e}. String: '{decoded_string[:100]}...'. Skipping row."
                    )
                    continue

            if not parsed_rows:
                activity_logger.warning(
                    f"No rows successfully parsed for entity '{packed_identifiers}'. Cannot write to S3."
                )
                await self.online_store.redis.ltrim(
                    redis_key, 0, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 1
                )
                activity_logger.info(
                    f"Trimmed Redis for entity '{packed_identifiers}' after parsing failure."
                )
                return

            try:
                df_to_flush = pl.DataFrame(parsed_rows, strict=False)
                ts_col = family.config.timestamp_column
                if ts_col not in df_to_flush.columns:
                    activity_logger.error(
                        f"Timestamp column '{ts_col}' missing in parsed rows for '{packed_identifiers}'. Cannot flush."
                    )
                    raise ValueError(f"Timestamp column '{ts_col}' missing")

                df_to_flush = df_to_flush.with_columns(
                    pl.col(ts_col).cast(pl.Datetime(time_unit="us", time_zone="UTC"))
                )
                df_to_flush = df_to_flush.filter(pl.col(ts_col).is_not_null())

                if df_to_flush.is_empty():
                    activity_logger.warning(
                        f"DataFrame is empty after timestamp conversion/filtering for '{packed_identifiers}'."
                    )
                    await self.online_store.redis.ltrim(
                        redis_key, 0, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 1
                    )
                    activity_logger.info(
                        f"Trimmed Redis for entity '{packed_identifiers}' after empty DataFrame."
                    )
                    return

                grouped_by_date = df_to_flush.group_by(
                    pl.col(ts_col).dt.date().alias("event_date")
                )
                activity_logger.debug(
                    f"Grouped {len(df_to_flush)} rows into {grouped_by_date.len()} dates for entity '{packed_identifiers}'."
                )

                partition_num = calculate_partition(
                    packed_identifiers, family.pipeline_settings.num_partitions
                )
                activity_logger.debug(
                    f"Calculated partition {partition_num} for entity '{packed_identifiers}'."
                )

                write_tasks = []
                processed_dates = []
                for date_group_key, date_df in grouped_by_date:
                    event_date: DateType = date_group_key[0]
                    processed_dates.append(event_date)
                    if date_df.is_empty():
                        activity_logger.warning(
                            f"DataFrame for date {event_date} is empty for entity '{packed_identifiers}'. Skipping write."
                        )
                        continue

                    activity_logger.debug(
                        f"Scheduling S3 write for entity '{packed_identifiers}', date {event_date}, partition {partition_num} ({len(date_df)} rows)."
                    )
                    write_tasks.append(
                        self.offline_store.write_batch(
                            family=family,
                            df=date_df,
                            partition=partition_num,
                            date=event_date,
                        )
                    )

                if write_tasks:
                    await asyncio.gather(*write_tasks)
                    activity_logger.info(
                        f"Successfully wrote flushed data to S3 for entity '{packed_identifiers}' for dates: {sorted(processed_dates)}"
                    )
                else:
                    activity_logger.info(
                        f"No S3 write tasks were scheduled for entity '{packed_identifiers}'."
                    )

            except Exception as e:
                activity_logger.error(
                    f"Error processing or writing flushed data for entity '{packed_identifiers}': {e}",
                    exc_info=True,
                )
                raise

            await self.online_store.redis.ltrim(
                redis_key, 0, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 1
            )
            activity_logger.info(
                f"Successfully trimmed Redis list for entity '{packed_identifiers}' to {MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS} items."
            )

        except Exception as e:
            activity_logger.error(
                f"Flush activity failed for entity '{packed_identifiers}' in family '{family_name}': {e}",
                exc_info=True,
            )
            raise
