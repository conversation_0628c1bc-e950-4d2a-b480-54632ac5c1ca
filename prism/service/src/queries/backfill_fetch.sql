WITH events AS (
	{{family.batch_query}}
)

SELECT
	*
FROM events
WHERE (UNHEX(SUBSTRING(MD5(CONCAT_WS('|', {{ family.config.identifier_columns|join(", ") }})), 17, 16))::BIT::INT64 % {{family.pipeline_settings.num_partitions}})={{partition}}
	AND {{family.config.timestamp_column}} < CURRENT_TIMESTAMP::TIMESTAMP_NTZ - INTERVAL '{{family.config.source.batch.late_arriving_data_lag_seconds}} seconds'
ORDER BY {{family.config.timestamp_column}} ASC, {{family.config.id_column}} ASC
