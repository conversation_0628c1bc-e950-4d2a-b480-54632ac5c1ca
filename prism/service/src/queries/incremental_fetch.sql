WITH events AS (
    {{ family.batch_query }}
)
SELECT
    *
FROM events
WHERE
    {{ family.config.timestamp_column }} > '{{ current_frontier.isoformat() }}'::TIMESTAMP_TZ
    AND {{ family.config.timestamp_column }} < CURRENT_TIMESTAMP::TIMESTAMP_NTZ - INTERVAL '{{ family.config.source.batch.late_arriving_data_lag_seconds }} seconds'
ORDER BY
    {{ family.config.timestamp_column }} ASC,
    {{ family.config.id_column }} ASC
