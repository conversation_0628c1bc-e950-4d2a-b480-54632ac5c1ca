-- src/queries/plan_max_window_seconds.sql
WITH base_data AS (
    -- Execute the family's base query as a subquery
    {{ family.batch_query }}
)
SELECT
    COUNT(*) as event_count
FROM base_data -- Reference the CTE containing the base data
WHERE {{ family.config.timestamp_column }} >= DATEADD(day, -{{ lookback_days }}, CURRENT_TIMESTAMP())
  AND {{ family.config.timestamp_column }} < CURRENT_TIMESTAMP();
