-- src/queries/plan_num_partitions.sql
WITH base_data AS (
    -- Execute the family's base query as a subquery
    {{ family.batch_query }}
), hourly_counts AS (
    SELECT
        DATE_TRUNC('hour', {{ family.config.timestamp_column }}) as event_hour,
        COUNT(*) as event_count
    FROM base_data -- Reference the CTE containing the base data
    WHERE {{ family.config.timestamp_column }} >= DATEADD(day, -{{ lookback_days }}, CURRENT_TIMESTAMP())
      AND {{ family.config.timestamp_column }} < CURRENT_TIMESTAMP()
    GROUP BY 1
)
SELECT
    event_hour,
    event_count
FROM hourly_counts;
