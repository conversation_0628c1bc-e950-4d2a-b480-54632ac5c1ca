from typing import Any
from datetime import datetime, timezone
from copy import deepcopy

from src.families import Family
from src.aggregations import AggregationDescriptor


class FeatureRow:
    """
    A class representing a row of feature data with dictionary-like access but improved type safety.

    This class wraps a dictionary that stores the actual row data, but provides attribute-style
    access and type hints for common operations. It maintains compatibility with the existing
    dictionary-based approach while improving code readability and type safety.
    """

    def __init__(self, family: Family, data: dict[str, Any]):
        """
        Initialize a FeatureRow with a Family and data dictionary.

        Args:
            family: The Family object that defines the schema for this row
            data: The dictionary containing the row data
        """
        self._family = family
        self._data = data

    @property
    def timestamp(self) -> datetime | None:
        """Get the timestamp value for this row."""
        return self._data.get(self._family.config.timestamp_column)

    @timestamp.setter
    def timestamp(self, value: datetime) -> None:
        """Set the timestamp value for this row."""
        self._data[self._family.config.timestamp_column] = value

    @property
    def id(self) -> str | None:
        """Get the ID value for this row."""
        return self._data.get(self._family.config.id_column)

    @id.setter
    def id(self, value: str) -> None:
        """Set the ID value for this row."""
        self._data[self._family.config.id_column] = value

    def get_identifier(self, column: str) -> Any:
        """Get an identifier value by column name."""
        return self._data.get(column)

    def set_identifier(self, column: str, value: Any) -> None:
        """Set an identifier value by column name."""
        self._data[column] = value

    def get_feature(self, column: str) -> Any:
        """Get a feature value by column name."""
        return self._data.get(column)

    def set_feature(self, column: str, value: Any) -> None:
        """Set a feature value by column name."""
        self._data[column] = value

    def get_state(self, descriptor: AggregationDescriptor) -> Any:
        """Get an aggregation state by descriptor."""
        return self._data.get(descriptor.state_key)

    def set_state(self, descriptor: AggregationDescriptor, value: Any) -> None:
        """Set an aggregation state by descriptor."""
        self._data[descriptor.state_key] = value

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value by key with an optional default.

        This method maintains compatibility with the dictionary-based approach.
        """
        return self._data.get(key, default)

    def __getitem__(self, key: str) -> Any:
        """
        Get a value by key using dictionary-style access.

        This method maintains compatibility with the dictionary-based approach.
        """
        return self._data[key]

    def __setitem__(self, key: str, value: Any) -> None:
        """
        Set a value by key using dictionary-style access.

        This method maintains compatibility with the dictionary-based approach.
        """
        self._data[key] = value

    def __contains__(self, key: str) -> bool:
        """
        Check if a key exists in the row.

        This method maintains compatibility with the dictionary-based approach.
        """
        return key in self._data

    def keys(self) -> list[str]:
        """
        Get the keys in the row.

        This method maintains compatibility with the dictionary-based approach.
        """
        return list(self._data.keys())

    def items(self) -> list[tuple[str, Any]]:
        """
        Get the items in the row.

        This method maintains compatibility with the dictionary-based approach.
        """
        return list(self._data.items())

    def values(self) -> list[Any]:
        """
        Get the values in the row.

        This method maintains compatibility with the dictionary-based approach.
        """
        return list(self._data.values())

    def copy(self) -> "FeatureRow":
        """
        Create a deep copy of this FeatureRow.
        """
        return FeatureRow(self._family, deepcopy(self._data))

    def to_dict(self) -> dict[str, Any]:
        """Convert to a plain dictionary."""
        return self._data.copy()

    @classmethod
    def from_dict(cls, family: Family, data: dict[str, Any]) -> "FeatureRow":
        """Create a FeatureRow from a dictionary."""
        return cls(family, data.copy())

    @classmethod
    def from_dicts(
        cls, family: Family, data_list: list[dict[str, Any]]
    ) -> list["FeatureRow"]:
        """Create a list of FeatureRows from a list of dictionaries."""
        return [cls(family, data.copy()) for data in data_list]

    def ensure_timestamp_timezone(self) -> None:
        """
        Ensure the timestamp has timezone information (UTC).

        If the timestamp is None or already has timezone info, it is left unchanged.
        """
        ts = self.timestamp
        if ts is not None and ts.tzinfo is None:
            self.timestamp = ts.replace(tzinfo=timezone.utc)
