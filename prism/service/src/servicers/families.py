from gametime_protos.mlp.prism.v1.service_pb2 import (
    FamiliesServiceCreateResponse,
    FamiliesServiceDeleteResponse,
    FamiliesServiceDescribeResponse,
    FamiliesServiceListResponse,
)
from gametime_protos.mlp.prism.v1.service_pb2_grpc import (
    FamiliesServiceServicer,
    add_FamiliesServiceServicer_to_server,
)
from grpc import StatusCode

from src.families import (
    Family,
    FamilyInvalidError,
    FamilyAlreadyExistsError,
    FamilyNotFoundError,
)
from datetime import timedelta
from src.shared import FamilyDetails, TASK_QUEUE_NAME
from src.workflows import FamilyPipeline, DEFAULT_RETRY_POLICY


register_servicer = add_FamiliesServiceServicer_to_server


class Servicer(FamiliesServiceServicer):
    def __init__(self, registry, temporal_wrapper):
        self.registry = registry
        self.temporal_wrapper = temporal_wrapper

    async def Create(self, request, context):
        try:
            family = Family.from_proto(request)
            await self.registry.add(family)
            family_details = FamilyDetails(name=family.name)
            workflow_id = FamilyPipeline.id_for(family_details)
            await self.temporal_wrapper.client.start_workflow(
                FamilyPipeline.run,
                family_details,
                id=workflow_id,
                task_queue=TASK_QUEUE_NAME,
            )
            # TODO: Set up Temporal Schedule for FamilyIncrementalWorkflow here or via an admin tool
            # Example (conceptual, requires temporalio.schedule):
            # schedule_id = f"schedule-{family.name}"
            # try:
            #     await self.temporal_wrapper.client.create_schedule(
            #         schedule_id,
            #         temporalio.schedule.Schedule(
            #             spec=temporalio.schedule.ScheduleSpec(
            #                 intervals=[temporalio.schedule.ScheduleIntervalSpec(every=timedelta(hours=4))] # TODO: Fetch interval from family.pipeline_settings
            #             ),
            #             state=temporalio.schedule.ScheduleState(paused=False)
            #         )
            #     )
            # except temporalio.service.RPCError as rpc_err:
            #     if rpc_err.status == temporalio.service.StatusCode.ALREADY_EXISTS:
            #        pass
            #     else:
            #        raise
        except FamilyInvalidError as e:
            await context.abort(StatusCode.INVALID_ARGUMENT, str(e))
        except FamilyAlreadyExistsError as e:
            await context.abort(StatusCode.ALREADY_EXISTS, str(e))
        return FamiliesServiceCreateResponse()

    async def Delete(self, request, context):
        family_details = FamilyDetails(name=request.name)
        try:
            # Attempt to delete the schedule first
            try:
                await self.temporal_wrapper.client.execute_activity(
                    "delete_schedule",
                    args=[family_details],
                    start_to_close_timeout=timedelta(seconds=30),
                    retry_policy=DEFAULT_RETRY_POLICY,
                    task_queue=TASK_QUEUE_NAME,
                )
            except Exception as schedule_delete_err:
                # Log the error but proceed with other cleanup
                import logging

                logging.error(
                    f"Failed to delete schedule for family {request.name}: {schedule_delete_err}",
                    exc_info=True,
                )
                # Optionally, you could abort here or just log

            # Terminate running workflows (FamilyPipeline or FamilyIncrementalProcessingWorkflow)
            pipeline_workflow_id = FamilyPipeline.id_for(family_details)
            # Incremental workflows are harder to get IDs for without listing,
            # but schedules are often set up to terminate existing workflows on overlap or new start.
            # For now, just terminate the main pipeline if it's somehow still running.
            try:
                await self.temporal_wrapper.client.get_workflow_handle(
                    pipeline_workflow_id
                ).terminate("Family deleted by user request")
                import logging

                logging.info(
                    f"Terminated workflow {pipeline_workflow_id} for family {request.name}"
                )
            except Exception:  # Catch specific "not found" errors if possible
                import logging

                logging.info(
                    f"Workflow {pipeline_workflow_id} for family {request.name} not found or already completed."
                )
                pass  # It's okay if it's not running

            # Finally, remove the family from the registry
            await self.registry.remove(request.name)
        except FamilyNotFoundError as e:
            await context.abort(StatusCode.NOT_FOUND, str(e))
        return FamiliesServiceDeleteResponse()

    async def Describe(self, request, context):
        try:
            family = await self.registry.fetch_one(request.name)
        except FamilyNotFoundError as e:
            await context.abort(StatusCode.NOT_FOUND, str(e))
        return FamiliesServiceDescribeResponse(family=family.to_proto())

    async def List(self, request, context):
        families = await self.registry.fetch_all()
        return FamiliesServiceListResponse(families=[f.to_proto() for f in families])
