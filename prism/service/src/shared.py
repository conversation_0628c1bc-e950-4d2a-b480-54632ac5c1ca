import logging
import json
import sys
from dataclasses import dataclass
from datetime import datetime
from typing import Any
from collections.abc import Callable

from src.config import config
from src.families import FamilyStatus


TASK_QUEUE_NAME = "default"


@dataclass
class FamilyDetails:
    name: str


@dataclass
class FamilyBackfillDetails:
    name: str
    partition: int


@dataclass
class UpdateFamilyStatusDetails:
    name: str
    status: FamilyStatus
    status_detail: str | None


@dataclass
class FlushDetails:
    family_name: str
    packed_identifiers: str


def setup_logging() -> None:
    """
    Configure application-wide logging based on configuration.

    This sets up consistent logging across the application with support for:
    - Console logging
    - JSON formatted logs (optional)
    - Customizable log levels
    - Contextual logging
    """
    root_logger = logging.getLogger()

    # Clear any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Set log level from config
    level = getattr(logging, config.logging.level.upper(), logging.INFO)
    root_logger.setLevel(level)

    # Create handler for stdout
    handler = logging.StreamHandler(sys.stdout)

    # Configure formatter based on config
    if config.logging.json:
        handler.setFormatter(JsonFormatter())
    else:
        handler.setFormatter(logging.Formatter(config.logging.format))

    root_logger.addHandler(handler)

    # Log initial setup information
    logging.info("Logging initialized", extra={"level": level})


class JsonFormatter(logging.Formatter):
    """
    Custom formatter that outputs logs as JSON objects.
    """

    def format(self, record: logging.LogRecord) -> str:
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "name": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # Add any additional context provided in extra
        if hasattr(record, "data") and isinstance(record.data, dict):
            log_data.update(record.data)

        # Add any extra attributes from the record
        for key, value in record.__dict__.items():
            if key not in [
                "args",
                "asctime",
                "created",
                "exc_info",
                "exc_text",
                "filename",
                "funcName",
                "id",
                "levelname",
                "levelno",
                "lineno",
                "module",
                "msecs",
                "message",
                "msg",
                "name",
                "pathname",
                "process",
                "processName",
                "relativeCreated",
                "stack_info",
                "thread",
                "threadName",
                "data",
            ]:
                if not key.startswith("_") and isinstance(
                    value, (str, int, float, bool, type(None))
                ):
                    log_data[key] = value

        return json.dumps(log_data)


class Logger:
    """
    Logger class that provides structured and contextual logging capabilities.

    This class wraps a standard Python logger with additional functionality:
    - Contextual logging through extra fields
    - Simplified logging methods with context preservation
    - Service-specific log formatting
    """

    def __init__(self, name: str, context: dict[str, Any] | None = None):
        self.logger = logging.getLogger(name)
        self.context = context or {}

    def _log(self, level: int, msg: str, *args, **kwargs) -> None:
        """Internal logging method that preserves context."""
        extra = kwargs.get("extra", {})

        # Merge the instance context with any call-specific extra data
        merged_extra = {**self.context, **extra}
        kwargs["extra"] = merged_extra

        self.logger.log(level, msg, *args, **kwargs)

    def debug(self, msg: str, *args, **kwargs) -> None:
        """Log a debug message with context."""
        self._log(logging.DEBUG, msg, *args, **kwargs)

    def info(self, msg: str, *args, **kwargs) -> None:
        """Log an info message with context."""
        self._log(logging.INFO, msg, *args, **kwargs)

    def warning(self, msg: str, *args, **kwargs) -> None:
        """Log a warning message with context."""
        self._log(logging.WARNING, msg, *args, **kwargs)

    def error(self, msg: str, *args, **kwargs) -> None:
        """Log an error message with context."""
        self._log(logging.ERROR, msg, *args, **kwargs)

    def critical(self, msg: str, *args, **kwargs) -> None:
        """Log a critical message with context."""
        self._log(logging.CRITICAL, msg, *args, **kwargs)

    def with_context(self, **context) -> "Logger":
        """Create a new logger with additional context."""
        new_context = {**self.context, **context}
        return Logger(self.logger.name, new_context)


def get_logger(name: str, **context) -> Logger:
    """Get a logger instance with optional context."""
    return Logger(name, context)


def log_call(logger: Logger | None = None) -> Callable:
    """
    Decorator to log function calls with parameters and return values.

    Args:
        logger: Optional Logger instance. If not provided, will use the module logger.

    Usage:
        @log_call()
        def my_function(arg1, arg2):
            return result

        @log_call(logger=custom_logger)
        async def my_async_function(arg1, arg2):
            return result
    """

    def decorator(func: Callable) -> Callable:
        import functools
        import inspect

        func_logger = logger or get_logger(func.__module__)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create a copy of the arguments that can be safely logged
            safe_args = [repr(arg) for arg in args]
            safe_kwargs = {k: repr(v) for k, v in kwargs.items()}

            func_logger.debug(
                f"Calling {func.__name__}",
                extra={"args": safe_args, "kwargs": safe_kwargs},
            )

            try:
                result = func(*args, **kwargs)
                func_logger.debug(
                    f"{func.__name__} completed", extra={"result": repr(result)}
                )
                return result
            except Exception as e:
                func_logger.error(f"{func.__name__} failed: {str(e)}", exc_info=True)
                raise

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Create a copy of the arguments that can be safely logged
            safe_args = [repr(arg) for arg in args]
            safe_kwargs = {k: repr(v) for k, v in kwargs.items()}

            func_logger.debug(
                f"Calling async {func.__name__}",
                extra={"args": safe_args, "kwargs": safe_kwargs},
            )

            try:
                result = await func(*args, **kwargs)
                func_logger.debug(
                    f"{func.__name__} completed", extra={"result": repr(result)}
                )
                return result
            except Exception as e:
                func_logger.error(f"{func.__name__} failed: {str(e)}", exc_info=True)
                raise

        if inspect.iscoroutinefunction(func):
            return async_wrapper
        return wrapper

    return decorator
