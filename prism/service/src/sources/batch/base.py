import asyncio
import typing
from abc import ABC, abstractmethod

import sqlglot
from mlutils.asyncio import async_generator_from

from src.queries import SOURCE_DIALECT


class BatchSource(ABC):
    DIALECT = "default"

    def __init__(self, connection: typing.Any) -> None:
        self.connection = connection

    @staticmethod
    @abstractmethod
    def _connect(url: str) -> typing.Any:
        pass

    @classmethod
    def from_url(cls, url: str) -> "BatchSource":
        return cls(cls._connect(url))

    @abstractmethod
    def _fetch_all_sync(self, query: str) -> typing.Any:
        pass

    def _transpile_query(self, query: str) -> str:
        return sqlglot.transpile(query, read=SOURCE_DIALECT, write=self.DIALECT)[0]

    async def fetch_all(self, query: str) -> typing.Any:
        transpiled_query = self._transpile_query(query)
        return await asyncio.to_thread(self._fetch_all_sync, transpiled_query)

    @abstractmethod
    def _fetch_batches_sync(
        self, query: str, max_batch_size: int
    ) -> typing.Generator[typing.Any, None, None]:
        pass

    async def fetch_batches(
        self, query: str, max_batch_size: int
    ) -> typing.AsyncGenerator[typing.Any, None]:
        transpiled_query = self._transpile_query(query)
        async for df in async_generator_from(self._fetch_batches_sync)(
            transpiled_query, max_batch_size
        ):
            yield df
