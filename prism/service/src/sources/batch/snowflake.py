from urllib.parse import urlparse, parse_qs
import sys
import typing
import polars as pl

if "pytest" in sys.modules:
    from tests.mocks.snowflake_connector import snowflake
else:
    import snowflake.connector as snowflake

from src.sources.batch.base import BatchSource

HOST_SUFFIX = ".snowflakecomputing.com"


def chunked_polars(
    df: pl.DataFrame, chunk_size: int
) -> typing.Generator[pl.DataFrame, None, None]:
    """Yields chunks of a Polars DataFrame."""
    offset = 0
    while offset < len(df):
        yield df.slice(offset, chunk_size)
        offset += chunk_size


class SnowflakeBatchSource(BatchSource):
    DIALECT = "snowflake"

    @staticmethod
    def _connect(url: str) -> snowflake.SnowflakeConnection:  # type: ignore
        """Connects to Snowflake using a URL."""
        parsed_url = urlparse(url)
        query_string = parsed_url.query if parsed_url.query else ""
        parsed_query_string = parse_qs(query_string)
        kwargs = {k: v[0] for k, v in parsed_query_string.items() if v and v[0]}

        account = parsed_url.hostname
        if account and account.endswith(HOST_SUFFIX):
            account = account[: -len(HOST_SUFFIX)]

        database = parsed_url.path[1:] if parsed_url.path else None
        schema = kwargs.pop("schema", None)

        user = parsed_url.username
        password = parsed_url.password
        warehouse = kwargs.pop("warehouse", None)

        missing_components = []
        if not user:
            missing_components.append("user")
        if not password:
            missing_components.append("password")
        if not account:
            missing_components.append("account")
        if not warehouse:
            missing_components.append("warehouse")

        if missing_components:
            raise ValueError(
                f"Snowflake URL missing required components: {', '.join(missing_components)}. "
                f"URL format should be: snowflake://user:<EMAIL>/database/schema?warehouse=name&role=name"
            )

        connect_kwargs = {
            "user": user,
            "password": password,
            "account": account,
            "warehouse": warehouse,
            "database": database,
            "schema": schema,
            **kwargs,
        }
        connect_kwargs = {k: v for k, v in connect_kwargs.items() if v is not None}

        if hasattr(snowflake, "connector") and hasattr(snowflake.connector, "connect"):
            return snowflake.connector.connect(**connect_kwargs)  # type: ignore
        elif hasattr(snowflake, "connect"):
            return snowflake.connect(**connect_kwargs)  # type: ignore
        else:
            raise RuntimeError(
                "Cannot find Snowflake connect function in imported module."
            )

    def _fetch_all_sync(self, query: str) -> pl.DataFrame:
        """Fetches all results as a Polars DataFrame."""
        # Snowflake cursor doesn't support context manager protocol
        cursor = self.connection.cursor()
        try:
            cursor.execute(query)
            if hasattr(cursor, "fetch_polars_all"):
                return cursor.fetch_polars_all()
            else:
                try:
                    arrow_table = cursor.fetch_arrow_all()
                    return pl.from_arrow(arrow_table) if arrow_table else pl.DataFrame()
                except AttributeError:
                    all_data = cursor.fetchall()
                    colnames = (
                        [desc[0] for desc in cursor.description]
                        if cursor.description
                        else []
                    )
                    return (
                        pl.DataFrame(all_data, schema=colnames)
                        if all_data
                        else pl.DataFrame()
                    )
        finally:
            cursor.close()

    def _fetch_batches_sync(
        self, query: str, max_batch_size: int
    ) -> typing.Generator[pl.DataFrame, None, None]:
        """Fetches results in Polars DataFrame batches."""
        # Snowflake cursor doesn't support context manager protocol
        cursor = self.connection.cursor()
        try:
            cursor.execute(query)
            if hasattr(cursor, "fetch_polars_batches"):
                for df_batch in cursor.fetch_polars_batches():
                    yield from chunked_polars(df_batch, max_batch_size)
            else:
                try:
                    for arrow_batch in cursor.fetch_arrow_batches():
                        df_batch = pl.from_arrow(arrow_batch)
                        yield from chunked_polars(df_batch, max_batch_size)
                except AttributeError:
                    df_all = self._fetch_all_sync(query)
                    if not df_all.is_empty():
                        yield from chunked_polars(df_all, max_batch_size)
        finally:
            cursor.close()
