import io
import math
from typing import Any
from collections.abc import Coroutine
import logging
import json
from contextlib import asynccontextmanager
from collections import defaultdict
import asyncio
from datetime import date as DateType, datetime, timezone

from redis.asyncio import Redis
import aioboto3
from botocore.exceptions import ClientError
import polars as pl
from polars.exceptions import NoDataError, ComputeError, <PERSON><PERSON>peError

from src.families import Family
from src.csv_utils import (
    get_csv_column_order,
    format_row_to_csv_string,
    parse_csv_string_to_row,
)


MAX_COMMANDS_PER_PIPELINE = 1000
MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS = 50

logger = logging.getLogger(__name__)


class OnlineStore:
    def __init__(self, redis: Redis):
        self.redis = redis

    def _key_for(self, family: Family, packed_identifiers: str) -> str:
        """Generates the Redis key for a given family and entity."""
        family_id_part = (
            family.id
            if isinstance(family.id, (int, str)) and family.id
            else family.name
        )
        return f"p:{family_id_part}:{packed_identifiers}"

    def _derive_column_order_for_family(
        self, family: Family, sample_row: dict
    ) -> list[str]:
        """Derives column order based on family and a sample row for consistency."""
        return get_csv_column_order(family, sample_row)

    async def write_rows(self, family: Family, rows: list[dict[str, Any]]) -> list[str]:
        """
        Writes rows to Redis Lists, storing recent history as CSVs.
        Checks list lengths after writing and returns identifiers exceeding the threshold.
        """
        if not rows:
            logger.debug("Received empty list for write_rows, skipping.")
            return []

        try:
            if not all(col in rows[0] for col in family.config.identifier_columns):
                logger.warning(
                    f"First row missing required identifiers {family.config.identifier_columns} for {family.name}. Skipping Redis write."
                )
                return []
            column_order = self._derive_column_order_for_family(family, rows[0])
            if not column_order:
                logger.error(
                    f"Derived empty column order for family {family.name}. Skipping Redis write."
                )
                return []
            logger.debug(
                f"Using column order for Redis write {family.name}: {column_order}"
            )
        except IndexError:
            logger.warning(
                f"Received empty list for write_rows for {family.name}, skipping."
            )
            return []
        except Exception as e:
            logger.error(
                f"Error getting CSV column order for {family.name}: {e}", exc_info=True
            )
            return []

        rows_by_entity: dict[str, list[str | None]] = defaultdict(list)
        for row_dict in rows:
            try:
                packed_identifiers = family.pack_identifiers(row_dict)
                csv_string = format_row_to_csv_string(row_dict, column_order)
                if csv_string:
                    rows_by_entity[packed_identifiers].append(csv_string)
            except KeyError as e:
                logger.warning(
                    f"Missing identifier column {e} in row for {family.name}. Skipping row ID: {row_dict.get(family.config.id_column, 'N/A')}"
                )
                continue
            except Exception as e:
                logger.error(
                    f"Error packing identifiers or formatting row for {family.name}: {e}. Row ID: {row_dict.get(family.config.id_column, 'N/A')}",
                    exc_info=True,
                )
                continue

        identifiers_needing_flush: list[str] = []
        entities_in_current_batch = list(rows_by_entity.keys())
        current_batch_start_index = 0

        while current_batch_start_index < len(entities_in_current_batch):
            async with self.redis.pipeline(transaction=False) as pipe:
                num_commands = 0
                batch_end_index = current_batch_start_index
                entities_processed_in_pipe: list[str] = []

                while (
                    batch_end_index < len(entities_in_current_batch)
                    and num_commands < MAX_COMMANDS_PER_PIPELINE - 3
                ):
                    packed_identifiers = entities_in_current_batch[batch_end_index]
                    csv_strings = rows_by_entity[packed_identifiers]
                    batch_end_index += 1

                    if not csv_strings:
                        continue

                    redis_key = self._key_for(family, packed_identifiers)
                    csv_strings_to_push = [
                        s for s in reversed(csv_strings) if isinstance(s, str)
                    ]
                    if not csv_strings_to_push:
                        continue

                    entities_processed_in_pipe.append(packed_identifiers)
                    pipe.lpush(redis_key, *csv_strings_to_push)
                    num_commands += 1
                    pipe.llen(redis_key)
                    num_commands += 1
                    pipe.ltrim(redis_key, 0, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 1)
                    num_commands += 1

                if num_commands == 0:
                    current_batch_start_index = batch_end_index
                    continue

                try:
                    results = await pipe.execute(raise_on_error=True)
                    logger.debug(
                        f"Executed Redis pipeline with {num_commands} commands for {len(entities_processed_in_pipe)} entities."
                    )

                    result_idx = 0
                    for entity_processed in entities_processed_in_pipe:
                        llen_result_index = result_idx * 3 + 1
                        if llen_result_index < len(results):
                            current_length = results[llen_result_index]
                            if (
                                isinstance(current_length, int)
                                and current_length > MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
                            ):
                                identifiers_needing_flush.append(entity_processed)
                        else:
                            logger.warning(
                                f"Missing length result for entity {entity_processed} in pipeline results."
                            )
                        result_idx += 1

                except Exception as pipe_exec_err:
                    logger.error(
                        f"Error executing final Redis pipeline: {pipe_exec_err}",
                        exc_info=True,
                    )

                current_batch_start_index = batch_end_index

        unique_identifiers = sorted(list(set(identifiers_needing_flush)))
        if unique_identifiers:
            logger.info(
                f"Identified {len(unique_identifiers)} entities potentially needing flush for {family.name}: {unique_identifiers[:5]}..."
            )
        else:
            logger.debug(
                f"No entities needed flushing for {family.name} in this batch."
            )
        return unique_identifiers

    def _derive_column_order_for_read(self, family: Family) -> list[str]:
        """Derives the expected CSV column order based only on the Family definition."""
        return get_csv_column_order(family)

    async def read_recent_rows(
        self,
        family: Family,
        packed_identifiers: str,
        num_rows: int = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS,
        timeout: float = 5.0,
    ) -> list[dict[str, Any]]:
        """Reads the most recent rows (CSV strings) for an entity from Redis and parses them back into dictionaries."""
        redis_key = self._key_for(family, packed_identifiers)
        logger.debug(f"Reading max {num_rows} rows from Redis key: {redis_key}")
        try:
            csv_strings_bytes = await asyncio.wait_for(
                self.redis.lrange(redis_key, 0, num_rows - 1), timeout=timeout
            )

            if not csv_strings_bytes:
                logger.debug(f"No rows found in Redis for key: {redis_key}")
                return []
            logger.debug(
                f"Retrieved {len(csv_strings_bytes)} rows from Redis for key: {redis_key}"
            )
        except asyncio.TimeoutError:
            logger.warning(f"Timeout ({timeout}s) reading from Redis key: {redis_key}")
            return []
        except Exception as e:
            logger.error(
                f"Error reading from Redis key {redis_key}: {e}", exc_info=True
            )
            return []

        parsed_rows = []
        try:
            column_order = self._derive_column_order_for_read(family)
            if not column_order:
                logger.error(
                    f"Derived empty column order for family {family.name}. Cannot parse rows from Redis."
                )
                return []
            logger.debug(f"Using column order for parsing Redis rows: {column_order}")
        except Exception as e:
            logger.error(
                f"Failed to derive column order for parsing family {family.name}: {e}",
                exc_info=True,
            )
            return []

        for i, csv_bytes in enumerate(csv_strings_bytes):
            try:
                if not isinstance(csv_bytes, bytes):
                    logger.warning(
                        f"Item at index {i} from Redis key {redis_key} is not bytes: {type(csv_bytes)}. Skipping."
                    )
                    continue
                decoded_string = csv_bytes.decode("utf-8")
                parsed_dict = parse_csv_string_to_row(
                    decoded_string, column_order, family
                )
                if parsed_dict:
                    parsed_rows.append(parsed_dict)
                else:
                    pass

            except UnicodeDecodeError:
                logger.warning(
                    f"Could not decode UTF-8 CSV string from Redis for {family.name}/{packed_identifiers}, index {i}. Skipping."
                )
                continue
            except Exception as e:
                logger.error(
                    f"Unexpected error decoding or parsing CSV string from Redis for {family.name}/{packed_identifiers}, index {i}: {e}",
                    exc_info=True,
                )
                continue
        logger.debug(
            f"Successfully parsed {len(parsed_rows)} rows from Redis for key: {redis_key}"
        )
        return parsed_rows


class OfflineStore:
    """
    Stores historical feature data in S3 using Polars for CSV I/O.
    Data is stored as compressed CSV files, partitioned by family, partition number, and date.
    State tuples are stored as JSON strings within the CSV.
    """

    def __init__(self, bucket_name: str, s3_config: dict):
        self.bucket_name = bucket_name
        self.s3_config = s3_config
        self._session = aioboto3.Session()
        logger.info(
            f"OfflineStore initialized for bucket '{bucket_name}' with endpoint '{s3_config.get('endpoint_url')}'"
        )

    @asynccontextmanager
    async def _get_s3_client(self):
        client_kwargs = {}
        if self.s3_config.get("endpoint_url"):
            client_kwargs["endpoint_url"] = self.s3_config["endpoint_url"]
        if self.s3_config.get("aws_access_key_id"):
            client_kwargs["aws_access_key_id"] = self.s3_config["aws_access_key_id"]
        if self.s3_config.get("aws_secret_access_key"):
            client_kwargs["aws_secret_access_key"] = self.s3_config[
                "aws_secret_access_key"
            ]

        async with self._session.client("s3", **client_kwargs) as client:
            yield client

    def _key_for_partition_data(
        self, family: Family, partition: int, date: DateType
    ) -> str:
        family_name = family.name
        return f"{family_name}/partition={partition}/year={date.year}/month={date.month:02d}/day={date.day:02d}/data.csv.gz"

    def _prefix_for_family_date_range(
        self,
        family_name: str,
        start_date: DateType,
        end_date: DateType,
        partition_num: int | None = None,
    ) -> list[str]:
        """Generates potential S3 prefixes covering the date range for a family, optionally filtered by partition."""
        generated_prefixes = set()
        curr = start_date
        partition_segment = (
            f"partition={partition_num}" if partition_num is not None else "partition=*"
        )

        while curr <= end_date:
            monthly_prefix = f"{family_name}/{partition_segment}/year={curr.year}/month={curr.month:02d}/"
            generated_prefixes.add(monthly_prefix)

            month = curr.month + 1
            year = curr.year
            if month > 12:
                month = 1
                year += 1
            try:
                next_month_first_day = DateType(year, month, 1)
                curr = next_month_first_day
            except ValueError:
                logger.warning(
                    f"Error advancing date from {curr}, stopping prefix generation."
                )
                break

        prefix_list = sorted(list(generated_prefixes))
        logger.debug(
            f"Generated S3 prefixes for {family_name} ({start_date} to {end_date}), partition '{partition_segment}': {prefix_list}"
        )
        return prefix_list

    async def _list_keys_for_prefixes(
        self,
        prefixes: list[str],
        start_date: DateType,
        end_date: DateType,
        timeout: float = 5.0,
    ) -> list[str]:
        """Helper function to list keys matching prefixes within a precise date range."""
        partition_keys = []

        max_prefixes = 3
        if len(prefixes) > max_prefixes:
            logger.warning(
                f"Limiting S3 prefixes to process from {len(prefixes)} to {max_prefixes}."
            )
            prefixes = prefixes[:max_prefixes]

        async with self._get_s3_client() as s3_client:
            list_tasks: list[Coroutine] = []

            async def list_prefix(prefix_base):
                keys_in_prefix = []
                paginator = s3_client.get_paginator("list_objects_v2")
                logger.debug(
                    f"Listing objects with prefix: {prefix_base} in bucket {self.bucket_name}"
                )
                try:
                    page_count = 0
                    max_pages = 1

                    async for page in paginator.paginate(
                        Bucket=self.bucket_name,
                        Prefix=prefix_base,
                        PaginationConfig={"MaxItems": 100},
                    ):
                        page_count += 1
                        if page_count > max_pages:
                            logger.warning(
                                f"Reached max pages ({max_pages}) for prefix {prefix_base}, stopping pagination"
                            )
                            break

                        for obj in page.get("Contents", []):
                            key = obj["Key"]
                            if key.endswith("data.csv.gz"):
                                try:
                                    parts = key.split("/")
                                    year_part = None
                                    month_part = None
                                    day_part = None

                                    for part in parts:
                                        if part.startswith("year="):
                                            year_part = part
                                        elif part.startswith("month="):
                                            month_part = part
                                        elif part.startswith("day="):
                                            day_part = part

                                    if year_part and month_part and day_part:
                                        year = int(year_part.split("=")[-1])
                                        month = int(month_part.split("=")[-1])
                                        day = int(day_part.split("=")[-1])
                                        file_date = DateType(year, month, day)
                                        if start_date <= file_date <= end_date:
                                            keys_in_prefix.append(key)
                                            logger.debug(
                                                f"Found matching partition key: {key} for date {file_date}"
                                            )
                                        else:
                                            logger.debug(
                                                f"Key {key} date {file_date} outside range {start_date}-{end_date}. Skipping."
                                            )
                                    else:
                                        logger.warning(
                                            f"Unexpected key structure, cannot extract date: {key}"
                                        )
                                except (IndexError, ValueError, TypeError) as e:
                                    logger.warning(
                                        f"Could not parse date from key {key}: {e}"
                                    )
                except ClientError as e:
                    logger.error(
                        f"S3 ClientError listing objects with prefix {prefix_base}: {e}",
                        exc_info=True,
                    )
                except Exception as e:
                    logger.error(
                        f"Unexpected error during S3 list operation for {prefix_base}: {e}",
                        exc_info=True,
                    )
                return keys_in_prefix

            for prefix in prefixes:
                list_tasks.append(list_prefix(prefix))

            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*list_tasks, return_exceptions=True), timeout=timeout
                )

                for result in results:
                    if isinstance(result, Exception):
                        logger.warning(f"Error listing S3 keys: {result}")
                    else:
                        partition_keys.extend(result)

            except asyncio.TimeoutError:
                logger.warning(
                    f"Timeout ({timeout}s) listing S3 keys for prefixes: {prefixes}"
                )
                return []

        max_keys = 10
        unique_keys = sorted(list(set(partition_keys)))
        if len(unique_keys) > max_keys:
            logger.warning(f"Limiting S3 keys from {len(unique_keys)} to {max_keys}.")
            unique_keys = unique_keys[:max_keys]

        return unique_keys

    async def list_partitions_in_range(
        self, family_name: str, start_date: DateType, end_date: DateType
    ) -> list[str]:
        """Lists S3 keys matching the partition data format within the date range across ALL partitions."""
        prefixes = self._prefix_for_family_date_range(
            family_name, start_date, end_date, partition_num=None
        )
        unique_keys = await self._list_keys_for_prefixes(prefixes, start_date, end_date)

        logger.info(
            f"Found {len(unique_keys)} unique S3 partition keys for {family_name} across all partitions between {start_date} and {end_date}"
        )
        return unique_keys

    async def list_specific_partition_keys_in_range(
        self,
        family: Family,
        partition_num: int,
        start_date: DateType,
        end_date: DateType,
    ) -> list[str]:
        """Lists S3 keys for a SPECIFIC partition within the date range."""
        prefixes = self._prefix_for_family_date_range(
            family.name, start_date, end_date, partition_num=partition_num
        )
        unique_keys = await self._list_keys_for_prefixes(prefixes, start_date, end_date)
        logger.info(
            f"Found {len(unique_keys)} S3 keys for {family.name} partition {partition_num} between {start_date} and {end_date}"
        )
        return unique_keys

    def _get_polars_schema_for_family(self, family: Family) -> dict[str, pl.DataType]:
        """Derives the expected Polars schema for reading CSVs."""
        schema = {}
        schema[family.config.timestamp_column] = pl.Utf8
        schema[family.config.id_column] = pl.Utf8

        for col in family.config.identifier_columns:
            schema[col] = pl.Utf8

        for feat in family.config.features:
            schema[feat.column] = pl.Utf8

        for desc in family.aggregation_descriptors:
            schema[desc.state_key] = pl.Utf8

        logger.debug(f"Derived Polars read schema for {family.name}: {schema}")
        return schema

    def _parse_state_column(self, series: pl.Series) -> pl.Series:
        """Helper to parse JSON string state column into Polars List of Float64."""

        def safe_json_loads(json_str):
            if json_str is None:
                return None
            try:
                loaded = json.loads(json_str)
                if isinstance(loaded, (list, tuple)):
                    parsed_list = []
                    for item in loaded:
                        if isinstance(item, str):
                            if item == "Infinity":
                                parsed_list.append(float("inf"))
                            elif item == "-Infinity":
                                parsed_list.append(float("-inf"))
                            else:
                                try:
                                    parsed_list.append(float(item))
                                except ValueError:
                                    parsed_list.append(None)
                        elif isinstance(item, (int, float)):
                            parsed_list.append(float(item))
                        elif item is None:
                            parsed_list.append(None)
                        else:
                            parsed_list.append(None)
                    return parsed_list
                else:
                    logger.debug(
                        f"Expected list/tuple state, got {type(loaded)}: {json_str}"
                    )
                    return None
            except (json.JSONDecodeError, TypeError, ValueError) as e:
                logger.debug(
                    f"Failed to parse state JSON: {json_str}. Error: {e}",
                    exc_info=False,
                )
                return None

        return series.map_elements(
            safe_json_loads, return_dtype=pl.List(pl.Float64), skip_nulls=False
        )

    async def read_partition_data(
        self, s3_key: str, family: Family, timeout: float = 10.0
    ) -> pl.DataFrame | None:
        """
        Reads, decompresses, and parses a single S3 partition file (CSV) using Polars.
        State columns (JSON strings) are parsed into Polars Lists. Timestamps are parsed.

        Returns:
            A Polars DataFrame or None if reading fails.
        """
        logger.debug(
            f"Attempting to read S3 object using Polars: s3://{self.bucket_name}/{s3_key}"
        )
        async with self._get_s3_client() as s3_client:
            try:
                s3_object = await asyncio.wait_for(
                    s3_client.get_object(Bucket=self.bucket_name, Key=s3_key),
                    timeout=timeout,
                )

                async with s3_object["Body"] as stream:
                    compressed_data = await asyncio.wait_for(
                        stream.read(), timeout=timeout
                    )

                logger.debug(
                    f"Read {len(compressed_data)} compressed bytes from {s3_key}"
                )

                data_buffer = io.BytesIO(compressed_data)

                read_schema = self._get_polars_schema_for_family(family)
                state_cols = [desc.state_key for desc in family.aggregation_descriptors]

                try:
                    df = pl.read_csv(
                        data_buffer,
                        compression="gzip",
                        schema=read_schema,
                        try_parse_dates=False,
                        null_values=["", "None", "null", "NULL", "NaN", "NA"],
                        infer_schema_length=0,
                        ignore_errors=True,
                    )
                except TypeError:
                    import gzip

                    logger.warning(
                        f"Polars read_csv doesn't support compression parameter, using gzip manually for {s3_key}."
                    )
                    with gzip.GzipFile(
                        fileobj=io.BytesIO(data_buffer.getvalue()), mode="rb"
                    ) as gz_file:
                        decompressed_data = io.StringIO(gz_file.read().decode("utf-8"))
                        df = pl.read_csv(
                            decompressed_data,
                            schema=read_schema,
                            try_parse_dates=False,
                            null_values=["", "None", "null", "NULL", "NaN", "NA"],
                            infer_schema_length=0,
                            ignore_errors=True,
                        )
                logger.debug(f"Polars initially read {len(df)} rows from {s3_key}")

                ts_col = family.config.timestamp_column
                if ts_col in df.columns:
                    logger.debug(f"Parsing timestamp column '{ts_col}' for {s3_key}...")
                    original_rows = len(df)
                    try:
                        df = df.with_columns(
                            pl.col(ts_col)
                            .str.to_datetime(
                                time_unit="us",
                                time_zone="UTC",
                                format="%Y-%m-%dT%H:%M:%S.%fZ",
                                strict=False,
                                exact=False,
                                cache=False,
                            )
                            .alias(ts_col)
                        )
                    except Exception as e:
                        logger.warning(
                            f"Failed to parse timestamp with ISO format: {e}"
                        )
                        try:
                            df = df.with_columns(
                                pl.col(ts_col)
                                .str.to_datetime(
                                    time_unit="us",
                                    time_zone="UTC",
                                    strict=False,
                                    exact=False,
                                    cache=False,
                                )
                                .alias(ts_col)
                            )
                        except Exception as e2:
                            logger.error(f"Failed to parse timestamp column: {e2}")
                            df = df.with_columns(
                                pl.lit(datetime.now(timezone.utc)).alias(ts_col)
                            )

                    df = df.filter(pl.col(ts_col).is_not_null())
                    rows_after_ts_parse = len(df)
                    if rows_after_ts_parse < original_rows:
                        logger.warning(
                            f"Filtered out {original_rows - rows_after_ts_parse} rows due to failed timestamp parsing in {s3_key}"
                        )
                else:
                    logger.warning(
                        f"Timestamp column '{ts_col}' not found in DataFrame read from {s3_key}"
                    )

                for state_col in state_cols:
                    if state_col in df.columns:
                        logger.debug(
                            f"Parsing state column '{state_col}' from JSON string for {s3_key}..."
                        )
                        df = df.with_columns(
                            self._parse_state_column(pl.col(state_col)).alias(state_col)
                        )

                expected_cols = (
                    set(family.config.identifier_columns)
                    | {family.config.id_column}
                    | {fc.column for fc in family.config.features}
                    | set(state_cols)
                    | {ts_col}
                )
                for col in expected_cols:
                    if col not in df.columns:
                        logger.warning(
                            f"Expected column '{col}' missing from DataFrame read from {s3_key}. Adding as null."
                        )
                        df = df.with_columns(pl.lit(None).alias(col))

                logger.info(
                    f"Successfully parsed Polars DataFrame ({len(df)} rows) from partition {s3_key}"
                )
                return df

            except ClientError as e:
                error_code = e.response.get("Error", {}).get("Code")
                if error_code in ("404", "NoSuchKey", "NotFound"):
                    logger.warning(
                        f"S3 key not found: s3://{self.bucket_name}/{s3_key}"
                    )
                else:
                    logger.error(f"S3 ClientError reading {s3_key}: {e}", exc_info=True)
                return None
            except NoDataError:
                logger.warning(
                    f"Polars found no data in {s3_key} (possibly empty or header only). Returning empty DataFrame."
                )
                return pl.DataFrame()
            except (ComputeError, ShapeError) as e:
                logger.error(
                    f"Polars compute/shape error parsing {s3_key}: {e}", exc_info=True
                )
                return None
            except (gzip.BadGzipFile,) as e:
                logger.error(f"Failed to decompress {s3_key}: {e}", exc_info=True)
                return None
            except Exception as e:
                logger.error(
                    f"Unexpected error reading or parsing partition {s3_key} with Polars: {e}",
                    exc_info=True,
                )
                return None

    async def read_specific_partition_in_range(
        self,
        family: Family,
        partition_num: int,
        start_date: DateType,
        end_date: DateType,
        timeout: float = 10.0,
    ) -> pl.DataFrame | None:
        """Reads all data for a specific partition within a date range."""
        logger.info(
            f"Reading data for {family.name}, partition {partition_num}, from {start_date} to {end_date}"
        )
        try:
            s3_keys = await asyncio.wait_for(
                self.list_specific_partition_keys_in_range(
                    family, partition_num, start_date, end_date
                ),
                timeout=timeout,
            )
        except asyncio.TimeoutError:
            logger.warning(
                f"Timeout ({timeout}s) listing S3 keys for {family.name}, partition {partition_num}. Returning empty DataFrame."
            )
            return pl.DataFrame()
        except Exception as e:
            logger.error(
                f"Error listing S3 keys for {family.name}, partition {partition_num}: {e}",
                exc_info=True,
            )
            return pl.DataFrame()

        if not s3_keys:
            logger.info(
                f"No S3 keys found for {family.name}, partition {partition_num} in range {start_date}-{end_date}. Returning empty DataFrame."
            )
            return pl.DataFrame()

        logger.debug(
            f"Found {len(s3_keys)} S3 keys to read for partition {partition_num}."
        )

        max_keys_to_read = 5
        if len(s3_keys) > max_keys_to_read:
            logger.warning(
                f"Limiting S3 keys to read from {len(s3_keys)} to {max_keys_to_read} for partition {partition_num}."
            )
            s3_keys = s3_keys[:max_keys_to_read]

        read_tasks = [self.read_partition_data(key, family) for key in s3_keys]
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*read_tasks, return_exceptions=True), timeout=timeout * 2
            )
        except asyncio.TimeoutError:
            logger.warning(
                f"Timeout ({timeout * 2}s) reading S3 data for {family.name}, partition {partition_num}. Returning empty DataFrame."
            )
            return pl.DataFrame()

        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.warning(f"Error reading S3 key {s3_keys[i]}: {result}")
            elif result is not None and not (
                isinstance(result, pl.DataFrame) and result.is_empty()
            ):
                valid_results.append(result)

        valid_dfs = valid_results

        if not valid_dfs:
            logger.warning(
                f"No valid data could be read from {len(s3_keys)} keys for partition {partition_num}. Returning empty DataFrame."
            )
            return pl.DataFrame()

        logger.info(
            f"Successfully read {len(valid_dfs)} non-empty DataFrames for partition {partition_num}."
        )
        try:
            combined_df = pl.concat(valid_dfs, how="vertical_relaxed")
            logger.info(
                f"Combined DataFrame shape for partition {partition_num}: {combined_df.shape}"
            )
            return combined_df
        except Exception as e:
            logger.error(
                f"Error concatenating DataFrames for partition {partition_num}: {e}",
                exc_info=True,
            )
            return pl.DataFrame()

    def _serialize_state_column(self, series: pl.Series) -> pl.Series:
        """Helper to serialize Polars List/Struct state column to JSON string."""

        def safe_json_dumps(state_data):
            if state_data is None:
                return "null"
            try:
                if isinstance(state_data, (list, tuple)):
                    serializable_list = []
                    for item in state_data:
                        if isinstance(item, float):
                            if math.isnan(item):
                                serializable_list.append(None)
                            elif math.isinf(item):
                                serializable_list.append(str(item))
                            else:
                                serializable_list.append(item)
                        elif item is None:
                            serializable_list.append(None)
                        else:
                            serializable_list.append(item)
                    return json.dumps(serializable_list)
                elif isinstance(state_data, dict):
                    return json.dumps(state_data)
                else:
                    return json.dumps(state_data)

            except TypeError as e:
                logger.warning(
                    f"Could not JSON serialize state: {state_data}. Error: {e}",
                    exc_info=False,
                )
                return None

        return series.map_elements(
            safe_json_dumps, return_dtype=pl.Utf8, skip_nulls=False
        )

    async def write_batch(
        self,
        family: Family,
        df: pl.DataFrame | list[dict],
        partition: int,
        date: DateType,
    ) -> None:
        """
        Writes a batch of rows (Polars DataFrame or list of dicts) to S3 as a compressed CSV file.
        State columns (lists/structs) are serialized to JSON strings before writing.
        """
        if isinstance(df, list):
            if not df:
                logger.warning(
                    f"Received empty list for {family.name}/partition={partition}/{date}, skipping S3 write."
                )
                return
            try:
                df = pl.DataFrame(df, strict=False)
            except Exception as e:
                logger.error(
                    f"Error converting list of dicts to DataFrame for {family.name}/partition={partition}/{date}: {e}",
                    exc_info=True,
                )
                raise

        if df.is_empty():
            logger.warning(
                f"Received empty DataFrame for {family.name}/partition={partition}/{date}, skipping S3 write."
            )
            return

        key = self._key_for_partition_data(family, partition, date)
        logger.info(
            f"Writing {len(df)} rows using Polars to compressed CSV: s3://{self.bucket_name}/{key}"
        )

        try:
            df_to_write = df.clone()

            state_cols = [desc.state_key for desc in family.aggregation_descriptors]
            for col_name in state_cols:
                if col_name in df_to_write.columns:
                    if df_to_write[col_name].dtype.is_nested():
                        logger.debug(
                            f"Serializing state column '{col_name}' to JSON string for {key}."
                        )
                        df_to_write = df_to_write.with_columns(
                            self._serialize_state_column(pl.col(col_name)).alias(
                                col_name
                            )
                        )
                    elif df_to_write[col_name].dtype != pl.Utf8:
                        logger.warning(
                            f"State column '{col_name}' in {key} has unexpected type {df_to_write[col_name].dtype} before write. Attempting JSON serialization/string cast."
                        )
                        df_to_write = df_to_write.with_columns(
                            self._serialize_state_column(pl.col(col_name))
                            .cast(pl.Utf8)
                            .alias(col_name)
                        )

            ts_col = family.config.timestamp_column
            if ts_col in df_to_write.columns:
                if df_to_write[ts_col].dtype == pl.Datetime:
                    logger.debug(
                        f"Ensuring timestamp column '{ts_col}' is in UTC for {key}."
                    )
                    df_to_write = df_to_write.with_columns(
                        pl.col(ts_col).dt.replace_time_zone("UTC").alias(ts_col)
                    )
                elif isinstance(df_to_write[ts_col].dtype, pl.Date):
                    df_to_write = df_to_write.with_columns(
                        pl.col(ts_col).cast(pl.Datetime).dt.replace_time_zone("UTC")
                    )
                else:
                    logger.warning(
                        f"Timestamp column '{ts_col}' is not Datetime ({df_to_write[ts_col].dtype}) in {key}. Attempting conversion to UTC Datetime."
                    )
                    try:
                        df_to_write = df_to_write.with_columns(
                            pl.col(ts_col)
                            .str.to_datetime(
                                time_unit="us",
                                time_zone="UTC",
                                format=None,
                                strict=False,
                                exact=False,
                                cache=False,
                            )
                            .alias(ts_col)
                        )
                        if df_to_write[ts_col].dtype != pl.Datetime(
                            time_unit="us", time_zone="UTC"
                        ):
                            df_to_write = df_to_write.with_columns(
                                pl.col(ts_col).cast(
                                    pl.Datetime(time_unit="us", time_zone="UTC")
                                )
                            )
                    except Exception as e:
                        logger.error(
                            f"Failed to convert timestamp column '{ts_col}' to Datetime before writing CSV for {key}: {e}",
                            exc_info=True,
                        )

            id_cols = [family.config.id_column] + family.config.identifier_columns
            for col in id_cols:
                if col in df_to_write.columns and df_to_write[col].dtype != pl.Utf8:
                    df_to_write = df_to_write.with_columns(pl.col(col).cast(pl.Utf8))

            compressed_buffer = io.BytesIO()

            try:
                df_to_write.write_csv(
                    compressed_buffer,
                    compression="gzip",
                    datetime_format="%Y-%m-%dT%H:%M:%S.%fZ",
                    float_precision=8,
                    null_value="",
                    quote_style="necessary",
                )
            except TypeError:
                import gzip

                logger.warning(
                    f"Polars write_csv failed with TypeError, trying alternative approach for {key}."
                )

                csv_buffer = io.StringIO()
                df_to_write.write_csv(
                    csv_buffer,
                    datetime_format="%Y-%m-%dT%H:%M:%S.%fZ",
                    float_precision=8,
                    null_value="",
                    quote_style="necessary",
                )

                csv_buffer.seek(0)
                csv_data = csv_buffer.getvalue().encode("utf-8")

                with gzip.GzipFile(fileobj=compressed_buffer, mode="wb") as gz_file:
                    gz_file.write(csv_data)

            compressed_buffer.seek(0)
            compressed_size = compressed_buffer.getbuffer().nbytes

            async with self._get_s3_client() as s3_client:
                await s3_client.put_object(
                    Bucket=self.bucket_name, Key=key, Body=compressed_buffer
                )

            logger.info(
                f"Successfully wrote {len(df)} rows with Polars: s3://{self.bucket_name}/{key} ({compressed_size:,} bytes compressed)"
            )

        except Exception as e:
            logger.error(
                f"ERROR writing Polars DataFrame to compressed CSV {key}: {e}",
                exc_info=True,
            )
            raise
