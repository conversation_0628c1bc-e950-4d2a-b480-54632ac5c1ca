from temporalio.client import Client


class ClientWrapper:
    def __init__(self, *args, **kwargs):
        self.args = args
        self.kwargs = kwargs
        self._client = None

    @property
    def client(self):
        if self._client is None:
            raise RuntimeError("you must call `connect` before accessing `client`")
        return self._client

    async def connect(self):
        self._client = await Client.connect(*self.args, **self.kwargs)
