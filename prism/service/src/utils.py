from datetime import datetime, timezone
import hashlib
import struct


def utcnow():
    return datetime.now(timezone.utc)


def to_utc_timestamp(dt):
    return dt.replace(tzinfo=timezone.utc).timestamp()


def calculate_partition(packed_identifiers: str, num_partitions: int) -> int:
    """
    Calculates the target partition for an entity based on its packed identifiers,
    replicating the logic used in the Snowflake backfill query:
    `UNHEX(SUBSTRING(MD5(packed_identifiers), 17, 16))::BIT::INT64 % num_partitions`

    Args:
        packed_identifiers: The '|' separated string of identifier values.
        num_partitions: The total number of partitions for the family.

    Returns:
        The calculated partition number (0 to num_partitions - 1).
    """
    if num_partitions <= 0:
        raise ValueError("Number of partitions must be positive")
    if num_partitions == 1:
        return 0

    md5_hash = hashlib.md5(
        packed_identifiers.encode("utf-8"), usedforsecurity=False
    ).hexdigest()
    hex_substring = md5_hash[16:32]

    hash_bytes = bytes.fromhex(hex_substring)

    try:
        int_value = struct.unpack(">q", hash_bytes)[0]
    except struct.error:
        raise ValueError("Could not unpack hash bytes into 64-bit integer")

    partition = int_value % num_partitions

    if partition < 0:
        partition += num_partitions

    return partition
