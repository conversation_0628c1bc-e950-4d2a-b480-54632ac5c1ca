from temporalio.worker.workflow_sandbox import (
    SandboxRestrictions,
    SandboxedWorkflowRunner,
)
from temporalio.worker import UnsandboxedWorkflowRunner

SNOWFLAKE_MODULES = [
    "snowflake",
    "snowflake.connector",
    "snowflake.connector.constants",
    "snowflake.connector.compat",
    "snowflake.connector.telemetry",
    "snowflake.connector.test_util",
    "snowflake.connector.errors",
    "snowflake.connector.connection",
    "snowflake.connector.proxy",
]

AWS_SDK_MODULES = [
    "aioboto3",
    "boto3",
    "botocore",
]

HTTP_MODULES = [
    "urllib3",
    "http",
]

SYSTEM_MODULES = [
    "platform",
    "subprocess",
]

PASSTHROUGH_MODULES = (
    SNOWFLAKE_MODULES + AWS_SDK_MODULES + HTTP_MODULES + SYSTEM_MODULES
)

CUSTOM_RESTRICTIONS = SandboxRestrictions.default.with_passthrough_modules(
    *PASSTHROUGH_MODULES
)

CUSTOM_WORKFLOW_RUNNER = SandboxedWorkflowRunner(restrictions=CUSTOM_RESTRICTIONS)

TEST_WORKFLOW_RUNNER = UnsandboxedWorkflowRunner()
