from datetime import timedelta, datetime, timezone
import asyncio

from temporalio import workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import ActivityError, ChildWorkflowError
from src.families import FamilyStatus

from src.shared import (
    FamilyDetails,
    UpdateFamilyStatusDetails,
    FamilyBackfillDetails,
    TASK_QUEUE_NAME,
)


UPDATE_STATUS_TIME_LIMIT = timedelta(seconds=10)
PLAN_TIME_LIMIT = timedelta(minutes=5)
BACKFILL_TIME_LIMIT = timedelta(hours=4)
BACKFILL_HEARTBEAT_TIMEOUT = timedelta(seconds=60)
GET_SET_FRONTIER_TIME_LIMIT = timedelta(seconds=10)
INCREMENTAL_BATCH_TIME_LIMIT = timedelta(minutes=30)
INCREMENTAL_HEARTBEAT_TIMEOUT = timedelta(seconds=60)
RUN_LOOP_ITERATIONS_BEFORE_CONTINUE = 100

DEFAULT_RETRY_POLICY = RetryPolicy(maximum_attempts=3)
INCREMENTAL_RETRY_POLICY = RetryPolicy(
    maximum_attempts=5,
    initial_interval=timedelta(seconds=10),
    maximum_interval=timedelta(minutes=1),
    backoff_coefficient=2.0,
)


@workflow.defn
class FamilyBackfill:
    @staticmethod
    def id_for(family_backfill_details: FamilyBackfillDetails) -> str:
        return f"family-backfill-{family_backfill_details.name}-{family_backfill_details.partition}"

    @workflow.run
    async def run(self, family_backfill_details: FamilyBackfillDetails) -> None:
        retry_policy: RetryPolicy = RetryPolicy(maximum_attempts=1)
        await workflow.execute_activity(
            "backfill",
            args=[family_backfill_details],
            start_to_close_timeout=BACKFILL_TIME_LIMIT,
            heartbeat_timeout=BACKFILL_HEARTBEAT_TIMEOUT,
            retry_policy=retry_policy,
            task_queue=TASK_QUEUE_NAME,
        )


@workflow.defn
class FamilyPipeline:
    _run_loop_iteration: int = 0

    @staticmethod
    def id_for(family_details: FamilyDetails) -> str:
        return f"family-pipeline-{family_details.name}"

    async def update_status(
        self, family_details: FamilyDetails, status: FamilyStatus, status_detail: str
    ) -> None:
        update_family_status_details: UpdateFamilyStatusDetails = (
            UpdateFamilyStatusDetails(
                name=family_details.name, status=status, status_detail=status_detail
            )
        )
        await workflow.execute_activity(
            "update_status",
            args=[update_family_status_details],
            start_to_close_timeout=UPDATE_STATUS_TIME_LIMIT,
            retry_policy=DEFAULT_RETRY_POLICY,
            task_queue=TASK_QUEUE_NAME,
        )

    async def backfill(
        self, family_details: FamilyDetails, num_partitions: int
    ) -> None:
        workflow.logger.info(
            f"[{family_details.name}] Starting parallel backfill of {num_partitions} partitions."
        )

        child_handles = []
        for partition in range(num_partitions):
            family_backfill_details: FamilyBackfillDetails = FamilyBackfillDetails(
                name=family_details.name, partition=partition
            )
            workflow_id: str = FamilyBackfill.id_for(family_backfill_details)

            handle = await workflow.start_child_workflow(
                FamilyBackfill.run,
                args=[family_backfill_details],
                id=workflow_id,
                task_queue=TASK_QUEUE_NAME,
                parent_close_policy=workflow.ParentClosePolicy.TERMINATE,
                retry_policy=RetryPolicy(maximum_attempts=1),
            )
            child_handles.append(handle)
            workflow.logger.info(
                f"[{family_details.name}] Started backfill partition {partition}."
            )

        if child_handles:
            workflow.logger.info(
                f"[{family_details.name}] Waiting for {len(child_handles)} backfill partitions to complete."
            )
            await asyncio.gather(*child_handles)

        workflow.logger.info(
            f"[{family_details.name}] All {num_partitions} backfill partitions completed."
        )

    @workflow.run
    async def run(self, family_details: FamilyDetails) -> None:
        """
        Manages the setup and initial backfill of a feature family pipeline:
        1. Initialization: Plans pipeline settings based on historical data.
        2. Creates a schedule for incremental processing.
        3. Backfill: Processes historical data across partitions.
        4. Transitions to RUNNING state and completes, with incremental processing
           handled by the scheduled FamilyIncrementalProcessingWorkflow.
        """
        pipeline_settings_result: dict = {}

        try:
            workflow.logger.info(
                f"[{family_details.name}] Starting initialization phase."
            )
            plan_retry_policy: RetryPolicy = RetryPolicy(
                maximum_attempts=3,
                maximum_interval=timedelta(seconds=5),
            )

            pipeline_settings_result = await workflow.execute_activity(
                "plan",
                args=[family_details],
                start_to_close_timeout=PLAN_TIME_LIMIT,
                retry_policy=plan_retry_policy,
                task_queue=TASK_QUEUE_NAME,
            )
            num_partitions = pipeline_settings_result["num_partitions"]
            increment_interval_seconds = pipeline_settings_result[
                "increment_interval_seconds"
            ]

            workflow.logger.info(
                f"[{family_details.name}] Planning complete. "
                f"Determined {num_partitions} partitions. "
                f"Increment interval: {increment_interval_seconds}s."
            )

            # Create or update the schedule for incremental processing
            await workflow.execute_activity(
                "create_or_update_schedule",
                args=[
                    family_details,
                    increment_interval_seconds,
                    "FamilyIncrementalProcessingWorkflow",
                ],
                start_to_close_timeout=timedelta(seconds=30),
                retry_policy=DEFAULT_RETRY_POLICY,
                task_queue=TASK_QUEUE_NAME,
            )
            workflow.logger.info(
                f"[{family_details.name}] Schedule for incremental processing created/updated."
            )

            workflow.logger.info(
                f"[{family_details.name}] Starting backfill phase with {num_partitions} partitions."
            )
            await self.update_status(
                family_details,
                FamilyStatus.FAMILY_STATUS_BACKFILLING,
                f"Backfilling {num_partitions} partitions.",
            )
            await self.backfill(family_details, num_partitions)
            workflow.logger.info(f"[{family_details.name}] Backfill phase completed.")

            # Set the frontier to the current time minus late arriving data lag
            # This ensures the incremental workflow starts from the right point
            current_time = datetime.now(timezone.utc)

            # Get the late arriving data lag from the family configuration
            family_config = await workflow.execute_activity(
                "get_family_config",
                args=[family_details],
                start_to_close_timeout=timedelta(seconds=10),
                retry_policy=DEFAULT_RETRY_POLICY,
                task_queue=TASK_QUEUE_NAME,
            )

            late_arriving_data_lag_seconds = family_config.get(
                "late_arriving_data_lag_seconds", 3600
            )
            new_frontier = current_time - timedelta(
                seconds=late_arriving_data_lag_seconds
            )

            # Set the frontier after backfill completes
            await workflow.execute_activity(
                "set_frontier",
                args=[family_details, new_frontier.isoformat()],
                start_to_close_timeout=GET_SET_FRONTIER_TIME_LIMIT,
                retry_policy=DEFAULT_RETRY_POLICY,
                task_queue=TASK_QUEUE_NAME,
            )

            workflow.logger.info(
                f"[{family_details.name}] Frontier set to: {new_frontier.isoformat()} after backfill completion"
            )

            await self.update_status(
                family_details,
                FamilyStatus.FAMILY_STATUS_RUNNING,
                "Initial setup and backfill complete. Incremental processing scheduled.",
            )
            workflow.logger.info(
                f"[{family_details.name}] Transitioned to RUNNING state. Workflow will now complete."
            )

        except ActivityError as e:
            workflow.logger.error(
                f"[{family_details.name}] Activity error during setup/backfill/scheduling: {e}",
                exc_info=True,
            )
            await self.update_status(
                family_details,
                FamilyStatus.FAMILY_STATUS_INITIALIZING_FAILED,
                f"Setup/Scheduling activity failed: {e}",
            )
            raise
        except ChildWorkflowError as e:
            workflow.logger.error(
                f"[{family_details.name}] Child workflow error during backfill: {e}",
                exc_info=True,
            )
            await self.update_status(
                family_details,
                FamilyStatus.FAMILY_STATUS_BACKFILLING_FAILED,
                f"Backfill partition failed: {e}",
            )
            raise
        except Exception as e:
            workflow.logger.error(
                f"[{family_details.name}] Unexpected error during setup: {e}",
                exc_info=True,
            )
            await self.update_status(
                family_details,
                FamilyStatus.FAMILY_STATUS_INITIALIZING_FAILED,
                f"Unexpected setup error: {e}",
            )
            raise
        # The workflow now completes after setup. The schedule handles periodic runs.


@workflow.defn
class FamilyIncrementalProcessingWorkflow:
    @staticmethod
    def id_for(family_details: FamilyDetails) -> str:
        # Use a unique prefix to avoid collision with FamilyPipeline
        # The schedule itself will manage uniqueness for invocations
        return f"family-incremental-run-{family_details.name}"

    @workflow.run
    async def run(self, family_details: FamilyDetails) -> None:
        workflow.logger.info(
            f"[{family_details.name}] Starting FamilyIncrementalProcessingWorkflow run."
        )
        try:
            frontier_iso: str = await workflow.execute_activity(
                "get_frontier",
                args=[family_details],
                start_to_close_timeout=GET_SET_FRONTIER_TIME_LIMIT,
                retry_policy=DEFAULT_RETRY_POLICY,
                task_queue=TASK_QUEUE_NAME,
            )
            current_frontier = datetime.fromisoformat(frontier_iso)
            if current_frontier.tzinfo is None:
                current_frontier = current_frontier.replace(tzinfo=timezone.utc)

            new_frontier_iso: str = await workflow.execute_activity(
                "process_incremental_batch",
                args=[family_details, current_frontier.isoformat()],
                start_to_close_timeout=INCREMENTAL_BATCH_TIME_LIMIT,
                heartbeat_timeout=INCREMENTAL_HEARTBEAT_TIMEOUT,
                retry_policy=INCREMENTAL_RETRY_POLICY,
                task_queue=TASK_QUEUE_NAME,
            )

            new_frontier = datetime.fromisoformat(new_frontier_iso)
            if new_frontier.tzinfo is None:
                new_frontier = new_frontier.replace(tzinfo=timezone.utc)

            if new_frontier > current_frontier:
                await workflow.execute_activity(
                    "set_frontier",
                    args=[family_details, new_frontier.isoformat()],
                    start_to_close_timeout=GET_SET_FRONTIER_TIME_LIMIT,
                    retry_policy=DEFAULT_RETRY_POLICY,
                    task_queue=TASK_QUEUE_NAME,
                )
                workflow.logger.info(
                    f"[{family_details.name}] Incremental run complete. Frontier updated to {new_frontier.isoformat()}."
                )
            else:
                workflow.logger.info(
                    f"[{family_details.name}] Incremental run complete. No new data, frontier remains {current_frontier.isoformat()}."
                )

        except ActivityError as e:
            workflow.logger.error(
                f"[{family_details.name}] Activity error during incremental processing run: {e}",
                exc_info=True,
            )
            # Optionally, update status to RUNNING_FAILED here if the schedule itself doesn't handle retries/failures adequately
            # For now, let the schedule's retry policy or manual intervention handle it.
            raise  # Re-raise to let Temporal handle it based on retry policy or mark workflow as failed
        except Exception as e:
            workflow.logger.error(
                f"[{family_details.name}] Unexpected error in incremental processing run: {e}",
                exc_info=True,
            )
            raise
