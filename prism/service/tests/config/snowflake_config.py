"""
Snowflake configuration for tests.

This module provides the Snowflake connection URL and credentials for tests.
It uses environment variables when available, falling back to default test values.
"""

import os

# Default test credentials - these are placeholders and not real credentials
DEFAULT_SNOWFLAKE_USER = "SVC_BASELINE"
# Using a placeholder string that won't trigger security scanners
DEFAULT_SNOWFLAKE_PASSWORD = "dummy_pwd_for_tests"  # nosec B105 - This is a placeholder for tests
DEFAULT_SNOWFLAKE_ACCOUNT = "gkb98284"
DEFAULT_SNOWFLAKE_DATABASE = "GAMETIME"
DEFAULT_SNOWFLAKE_SCHEMA = "BASELINE"
DEFAULT_SNOWFLAKE_WAREHOUSE = "BASELINE_S_WH"
DEFAULT_SNOWFLAKE_ROLE = "BASELINE_ROLE"

# Get values from environment variables if available, otherwise use defaults
SNOWFLAKE_USER = os.environ.get("SNOWFLAKE_USER", DEFAULT_SNOWFLAKE_USER)
SNOWFLAKE_PASSWORD = os.environ.get("SNOWFLAKE_PASSWORD", DEFAULT_SNOWFLAKE_PASSWORD)
SNOWFLAKE_ACCOUNT = os.environ.get("SNOWFLAKE_ACCOUNT", DEFAULT_SNOWFLAKE_ACCOUNT)
SNOWFLAKE_DATABASE = os.environ.get("SNOWFLAKE_DATABASE", DEFAULT_SNOWFLAKE_DATABASE)
SNOWFLAKE_SCHEMA = os.environ.get("SNOWFLAKE_SCHEMA", DEFAULT_SNOWFLAKE_SCHEMA)
SNOWFLAKE_WAREHOUSE = os.environ.get("SNOWFLAKE_WAREHOUSE", DEFAULT_SNOWFLAKE_WAREHOUSE)
SNOWFLAKE_ROLE = os.environ.get("SNOWFLAKE_ROLE", DEFAULT_SNOWFLAKE_ROLE)

# Construct the Snowflake URL
SNOWFLAKE_URL = (
    f"snowflake://{SNOWFLAKE_USER}:{SNOWFLAKE_PASSWORD}@{SNOWFLAKE_ACCOUNT}.snowflakecomputing.com/"
    f"{SNOWFLAKE_DATABASE}/{SNOWFLAKE_SCHEMA}?warehouse={SNOWFLAKE_WAREHOUSE}&role={SNOWFLAKE_ROLE}"
)


def setup_snowflake_env():
    """Set up environment variables for Snowflake tests."""
    os.environ["SOURCE_BATCH_KIND"] = "snowflake"
    os.environ["SOURCE_BATCH_URL"] = SNOWFLAKE_URL
