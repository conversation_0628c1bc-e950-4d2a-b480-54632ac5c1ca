"""Mock implementations of schedule management activities for testing."""

from datetime import datetime, timezone
from typing import Any

from temporalio import activity
from temporalio.client import Client as TemporalClient

from src.shared import FamilyDetails


class MockScheduleActivities:
    """Mock implementations of schedule management activities for testing."""

    def __init__(self, temporal_client: TemporalClient):
        self.temporal_client = temporal_client
        self.schedules: dict[str, dict[str, Any]] = {}

    @activity.defn(name="create_or_update_schedule")
    async def create_or_update_schedule(
        self,
        family_details: FamilyDetails,
        interval_seconds: int,
        workflow_type_name: str,
    ) -> None:
        """Mock implementation of create_or_update_schedule activity."""
        schedule_id = f"schedule-family-incremental-{family_details.name}"

        self.schedules[schedule_id] = {
            "family_details": family_details,
            "interval_seconds": interval_seconds,
            "workflow_type_name": workflow_type_name,
            "created_at": datetime.now(timezone.utc),
            "paused": False,
        }

        print(
            f"Mock schedule '{schedule_id}' created for family '{family_details.name}'"
        )
        return None

    @activity.defn(name="delete_schedule")
    async def delete_schedule(self, family_details: FamilyDetails) -> None:
        """Mock implementation of delete_schedule activity."""
        schedule_id = f"schedule-family-incremental-{family_details.name}"

        if schedule_id in self.schedules:
            del self.schedules[schedule_id]
            print(
                f"Mock schedule '{schedule_id}' deleted for family '{family_details.name}'"
            )
        else:
            print(
                f"Mock schedule '{schedule_id}' not found for family '{family_details.name}'"
            )

        return None

    async def get_schedule(self, schedule_id: str) -> dict[str, Any] | None:
        """Get a schedule by ID."""
        return self.schedules.get(schedule_id)

    async def list_schedules(self) -> list[dict[str, Any]]:
        """List all schedules."""
        return list(self.schedules.values())

    async def trigger_schedule(self, schedule_id: str) -> None:
        """Trigger a schedule manually."""
        if schedule_id not in self.schedules:
            raise ValueError(f"Schedule '{schedule_id}' not found")

        schedule = self.schedules[schedule_id]
        family_details = schedule["family_details"]
        # workflow_type_name is available in schedule["workflow_type_name"] if needed

        # Start the workflow directly
        workflow_id = f"family-incremental-run-{family_details.name}-{datetime.now(timezone.utc).isoformat()}"

        # We don't actually start the workflow, just record that it was triggered
        self.schedules[schedule_id]["last_triggered"] = datetime.now(timezone.utc)
        self.schedules[schedule_id]["last_workflow_id"] = workflow_id

        print(
            f"Mock schedule '{schedule_id}' triggered, would start workflow '{workflow_id}'"
        )
        return None
