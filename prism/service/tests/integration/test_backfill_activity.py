import pytest
from datetime import datetime, timezone, timedelta, date as DateType
from unittest.mock import AsyncMock, patch, MagicMock

from src.activities import FamilyBackfillActivities
from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    Registry,
    FamilyStatus,
    PipelineSettings,
)
from src.aggregations import AggregationFunction
from src.shared import FamilyBackfillDetails

from src.sources.batch.base import BatchSource
from src.stores import OfflineStore, OnlineStore


@pytest.fixture
def test_family_config():
    """Configuration for the test family."""
    return FamilyConfig(
        source=SourceConfig(
            batch=BatchSourceConfig(
                table="test_events", late_arriving_data_lag_seconds=3600
            ),
            query="SELECT event_id, ts, user_id, value FROM {{ ref }}",
        ),
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[
            FeatureConfig(
                column="value",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_SUM],
            ),
            FeatureConfig(
                column="event_id",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_COUNT],
            ),
        ],
    )


@pytest.fixture
def multi_agg_family_config():
    """Configuration for a family with multiple aggregation functions."""
    return FamilyConfig(
        source=SourceConfig(
            batch=BatchSourceConfig(
                table="test_events", late_arriving_data_lag_seconds=3600
            ),
            query="SELECT event_id, ts, user_id, value FROM {{ ref }}",
        ),
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[
            FeatureConfig(
                column="value",
                aggregations=[
                    AggregationFunction.AGGREGATION_FUNCTION_SUM,
                    AggregationFunction.AGGREGATION_FUNCTION_AVG,
                    AggregationFunction.AGGREGATION_FUNCTION_STDDEV,
                ],
            ),
            FeatureConfig(
                column="event_id",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_COUNT],
            ),
        ],
    )


@pytest.fixture
def test_pipeline_settings():
    """Simulated pipeline settings from the 'plan' activity."""
    return PipelineSettings(
        num_partitions=2,
        max_window_seconds=3600 * 24 * 7,
        increment_interval_seconds=3600 * 4,
    )


@pytest.fixture
def multi_partition_pipeline_settings():
    """Pipeline settings with more partitions for testing partition handling."""
    return PipelineSettings(
        num_partitions=4,
        max_window_seconds=3600 * 24 * 7,
        increment_interval_seconds=3600 * 4,
    )


@pytest.fixture
def test_family(test_family_config, test_pipeline_settings) -> Family:
    """Creates the test family object directly."""
    family = Family(
        name="test_backfill_family",
        config=test_family_config,
        pipeline_settings=test_pipeline_settings,
        status=FamilyStatus.FAMILY_STATUS_BACKFILLING,
        id=123,
    )
    return family


@pytest.fixture
def multi_agg_family(multi_agg_family_config, test_pipeline_settings) -> Family:
    """Creates a family with multiple aggregation functions."""
    family = Family(
        name="multi_agg_family",
        config=multi_agg_family_config,
        pipeline_settings=test_pipeline_settings,
        status=FamilyStatus.FAMILY_STATUS_BACKFILLING,
        id=456,
    )
    return family


@pytest.fixture
def multi_partition_family(
    test_family_config, multi_partition_pipeline_settings
) -> Family:
    """Creates a family with multiple partitions."""
    family = Family(
        name="multi_partition_family",
        config=test_family_config,
        pipeline_settings=multi_partition_pipeline_settings,
        status=FamilyStatus.FAMILY_STATUS_BACKFILLING,
        id=789,
    )
    return family


@pytest.fixture
def registry(test_family, multi_agg_family, multi_partition_family) -> MagicMock:
    """Mocks the Registry and configures fetch_one."""
    mock_reg = MagicMock(spec=Registry)

    families = {
        test_family.name: test_family,
        multi_agg_family.name: multi_agg_family,
        multi_partition_family.name: multi_partition_family,
    }

    async def mock_fetch_one(name):
        if name in families:
            return Family.from_dict(families[name].to_dict())
        else:
            raise Exception(f"Mock Registry received unexpected family name: {name}")

    mock_reg.fetch_one = AsyncMock(side_effect=mock_fetch_one)
    return mock_reg


@pytest.fixture
def mock_online_store_backfill() -> MagicMock:
    """Mocks OnlineStore specifically for backfill tests."""
    mock_store = MagicMock(spec=OnlineStore)
    mock_store.write_rows = AsyncMock(return_value=[])
    return mock_store


@pytest.fixture
def mock_offline_store():
    """Mocks the OfflineStore with an AsyncMock for write_batch."""
    mock_store = MagicMock(spec=OfflineStore)

    mock_store.write_batch_calls = []

    async def mock_write_batch(family, df, partition, date):
        mock_store.write_batch_calls.append((family, df, partition, date))
        return None

    mock_store.write_batch = AsyncMock(side_effect=mock_write_batch)
    return mock_store


@pytest.fixture
def failing_offline_store():
    """Mocks the OfflineStore with a write_batch method that raises an exception."""
    mock_store = MagicMock(spec=OfflineStore)

    mock_store.write_batch_calls = []

    async def mock_write_batch(family, df, partition, date):
        mock_store.write_batch_calls.append((family, df, partition, date))
        raise Exception("Simulated S3 write failure")

    mock_store.write_batch = AsyncMock(side_effect=mock_write_batch)
    return mock_store


@pytest.fixture
def backfill_input_data_list():
    """List of dicts representing data fetched by BatchSource for the test."""
    base_time = datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
    data = [
        {
            "event_id": "u1_e1",
            "ts": base_time + timedelta(minutes=1),
            "user_id": "user_1",
            "value": 10,
        },
        {
            "event_id": "u2_e1",
            "ts": base_time + timedelta(minutes=2),
            "user_id": "user_2",
            "value": 100,
        },
        {
            "event_id": "u1_e2",
            "ts": base_time + timedelta(minutes=5),
            "user_id": "user_1",
            "value": 5,
        },
        {
            "event_id": "u2_e2",
            "ts": base_time + timedelta(days=1, minutes=1),
            "user_id": "user_2",
            "value": 50,
        },
        {
            "event_id": "u1_e3",
            "ts": base_time + timedelta(days=1, minutes=10),
            "user_id": "user_1",
            "value": 2,
        },
        {
            "event_id": "u1_e4",
            "ts": base_time + timedelta(days=1, minutes=20),
            "user_id": "user_1",
            "value": 1,
        },
    ]
    return data


@pytest.fixture
def backfill_input_data_with_various_timestamps():
    """List of dicts with various timestamp formats to test timestamp handling."""
    base_time = datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
    data = [
        {
            "event_id": "ts_1",
            "ts": base_time,
            "user_id": "user_1",
            "value": 10,
        },
        {
            "event_id": "ts_2",
            "ts": base_time + timedelta(minutes=1),
            "user_id": "user_1",
            "value": 20,
        },
        {
            "event_id": "ts_3",
            "ts": base_time + timedelta(minutes=5),
            "user_id": "user_1",
            "value": 30,
        },
        {
            "event_id": "ts_4",
            "ts": base_time + timedelta(minutes=10),
            "user_id": "user_1",
            "value": 40,
        },
        {
            "event_id": "ts_5",
            "ts": base_time + timedelta(minutes=15),
            "user_id": "user_1",
            "value": 50,
        },
    ]
    return data


@pytest.fixture
def backfill_input_data_with_missing_fields():
    """List of dicts with missing fields to test error handling."""
    base_time = datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
    data = [
        {
            "event_id": "missing_1",
            "ts": base_time,
            "user_id": "user_1",
        },
        {
            "event_id": "missing_2",
            "ts": base_time + timedelta(minutes=5),
            "value": 20,
        },
        {
            "event_id": "missing_3",
            "user_id": "user_1",
            "value": 30,
        },
        {
            "ts": base_time + timedelta(minutes=15),
            "user_id": "user_1",
            "value": 40,
        },
        {
            "event_id": "complete",
            "ts": base_time + timedelta(minutes=20),
            "user_id": "user_1",
            "value": 50,
        },
    ]
    return data


@pytest.fixture
def backfill_input_data_with_null_values():
    """List of dicts with null values to test handling of nulls."""
    base_time = datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
    data = [
        {
            "event_id": "null_1",
            "ts": base_time,
            "user_id": "user_1",
            "value": None,
        },
        {
            "event_id": "null_2",
            "ts": base_time + timedelta(minutes=5),
            "user_id": None,
            "value": 20,
        },
        {
            "event_id": "null_3",
            "ts": None,
            "user_id": "user_1",
            "value": 30,
        },
        {
            "event_id": "complete",
            "ts": base_time + timedelta(minutes=20),
            "user_id": "user_1",
            "value": 50,
        },
    ]
    return data


@pytest.fixture
def multi_day_data_list():
    """List of dicts spanning multiple days to test date grouping."""
    base_time = datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
    data = []

    for day in range(3):
        for hour in range(0, 24, 6):
            data.append(
                {
                    "event_id": f"day{day}_hour{hour}",
                    "ts": base_time + timedelta(days=day, hours=hour),
                    "user_id": f"user_{day % 2 + 1}",
                    "value": day * 10 + hour,
                }
            )

    return data


@pytest.mark.asyncio
async def test_backfill_activity_success_writes_list_of_dicts(
    registry: MagicMock,
    test_family: Family,
    mock_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
    backfill_input_data_list: list,
):
    """
    Tests the successful execution of the backfill activity,
    verifying it calls offline_store.write_batch with lists of dicts.
    """
    test_batch_size = 3
    num_input_rows = len(backfill_input_data_list)
    num_batches_fetched = (num_input_rows + test_batch_size - 1) // test_batch_size
    assert num_batches_fetched == 2

    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        import polars as pl

        total_rows = len(backfill_input_data_list)
        for i in range(0, total_rows, test_batch_size):
            batch_to_yield = backfill_input_data_list[
                i : min(i + test_batch_size, total_rows)
            ]
            df = pl.DataFrame(batch_to_yield)
            yield df

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=mock_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )

    details = FamilyBackfillDetails(name=test_family.name, partition=0)

    with (
        patch("src.activities.activity_logger", MagicMock()) as mock_logger,
        patch("src.activities.activity.heartbeat", MagicMock()) as mock_heartbeat,
    ):
        await activities.backfill(details)

    mock_logger.info.assert_any_call(
        f"Starting backfill activity for {test_family.name}, partition 0"
    )
    mock_logger.info.assert_any_call(
        f"Processing batch DataFrame with {test_batch_size} rows for {test_family.name}/{details.partition}"
    )
    mock_logger.info.assert_any_call(
        f"Processing batch DataFrame with {num_input_rows - test_batch_size} rows for {test_family.name}/{details.partition}"
    )

    mock_logger.info.assert_any_call(
        f"Backfill successfully completed activity logic for {test_family.name}, partition 0"
    )

    assert mock_heartbeat.call_count == num_batches_fetched

    registry.fetch_one.assert_called_once_with(test_family.name)
    assert mock_offline_store.write_batch.call_count == 2

    calls = mock_offline_store.write_batch_calls
    results_by_date: dict[DateType, list[dict]] = {}

    for family, df, partition, date in calls:
        import polars as pl

        assert isinstance(df, (list, pl.DataFrame))
        if isinstance(df, list) and len(df) > 0:
            assert isinstance(df[0], dict)
        results_by_date[date] = df

    date1 = DateType(2023, 10, 26)
    assert date1 in results_by_date
    rows_date1 = results_by_date[date1]

    import polars as pl

    if isinstance(rows_date1, pl.DataFrame):
        assert rows_date1.shape[0] == 3
        row0_df = rows_date1.filter(pl.col("event_id") == "u1_e1")
        assert not row0_df.is_empty()
        agg_value_col = row0_df.select("agg_value_sum")
        assert not agg_value_col.is_empty()
        agg_count_col = row0_df.select("agg_event_id_count")
        assert not agg_count_col.is_empty()
    else:
        assert len(rows_date1) == 3
        row0 = next(r for r in rows_date1 if r["event_id"] == "u1_e1")
        agg_value = row0["agg_value_sum"]
        if isinstance(agg_value, list):
            assert agg_value[0] == 10.0
            assert abs(agg_value[1] - 1.0) < 0.001
        else:
            assert agg_value == (10.0, 1)
        agg_count = row0["agg_event_id_count"]
        if isinstance(agg_count, list):
            assert agg_count[0] == 1
        else:
            assert agg_count == (1,)

    date2 = DateType(2023, 10, 27)
    assert date2 in results_by_date
    rows_date2 = results_by_date[date2]

    if isinstance(rows_date2, pl.DataFrame):
        assert rows_date2.shape[0] == 3
        row5_df = rows_date2.filter(pl.col("event_id") == "u1_e4")
        assert not row5_df.is_empty()
        assert row5_df.select("ts").dtypes[0].is_temporal()
        assert "agg_value_sum" in row5_df.columns
        assert "agg_event_id_count" in row5_df.columns
        assert not row5_df.select("agg_value_sum").is_empty()
        assert not row5_df.select("agg_event_id_count").is_empty()
    else:
        assert len(rows_date2) == 3
        row5 = next(r for r in rows_date2 if r["event_id"] == "u1_e4")
        assert isinstance(row5["ts"], datetime)
        expected_avg_u1_e3 = ((7.5 * 2) + 2) / 3
        expected_avg_u1_e4 = ((expected_avg_u1_e3 * 3) + 1) / 4
        assert row5["agg_value_sum"][0] == pytest.approx(expected_avg_u1_e4)
        assert row5["agg_value_sum"][1] == 4
        agg_count = row5["agg_event_id_count"]
        if isinstance(agg_count, list):
            assert agg_count[0] == 4
        else:
            assert agg_count == (4,)


@pytest.mark.asyncio
async def test_backfill_activity_empty_input_list(
    registry: MagicMock,
    test_family: Family,
    mock_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
):
    """Tests behavior when the batch source returns no data (empty list)."""
    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        import polars as pl

        yield pl.DataFrame()

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=mock_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )
    details = FamilyBackfillDetails(name=test_family.name, partition=0)

    with (
        patch("src.activities.activity_logger", MagicMock()) as mock_logger,
        patch("src.activities.activity.heartbeat", MagicMock()) as mock_heartbeat,
    ):
        await activities.backfill(details)

    mock_logger.info.assert_any_call(
        f"Starting backfill activity for {test_family.name}, partition 0"
    )
    mock_logger.info.assert_any_call(
        f"Backfill successfully completed activity logic for {test_family.name}, partition 0"
    )

    registry.fetch_one.assert_called_once_with(test_family.name)
    mock_offline_store.write_batch.assert_not_called()
    mock_heartbeat.assert_not_called()


@pytest.mark.asyncio
async def test_backfill_activity_timestamp_handling(
    registry: MagicMock,
    test_family: Family,
    mock_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
    backfill_input_data_with_various_timestamps: list,
):
    """Tests the backfill activity's handling of various timestamp formats."""
    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        import polars as pl

        df = pl.DataFrame(backfill_input_data_with_various_timestamps)
        yield df

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=mock_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )

    details = FamilyBackfillDetails(name=test_family.name, partition=0)

    with (
        patch("src.activities.activity_logger", MagicMock()) as mock_logger,
        patch("src.activities.activity.heartbeat", MagicMock()),
    ):
        await activities.backfill(details)

    mock_logger.info.assert_any_call(
        f"Starting backfill activity for {test_family.name}, partition 0"
    )
    mock_logger.info.assert_any_call(
        f"Backfill successfully completed activity logic for {test_family.name}, partition 0"
    )
    assert mock_offline_store.write_batch.call_count == 1
    assert len(mock_offline_store.write_batch_calls) > 0
    _, rows, _, _ = mock_offline_store.write_batch_calls[0]

    assert "ts" in rows.columns
    assert rows["ts"].dtype.is_temporal()


@pytest.mark.asyncio
async def test_backfill_activity_missing_fields(
    registry: MagicMock,
    test_family: Family,
    mock_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
    backfill_input_data_with_missing_fields: list,
):
    """Tests the backfill activity's handling of rows with missing fields."""
    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        import polars as pl

        df = pl.DataFrame(backfill_input_data_with_missing_fields)
        yield df

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=mock_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )

    details = FamilyBackfillDetails(name=test_family.name, partition=0)

    with (
        patch("src.activities.activity_logger", MagicMock()) as mock_logger,
        patch("src.activities.activity.heartbeat", MagicMock()),
    ):
        await activities.backfill(details)

    mock_logger.info.assert_any_call(
        f"Starting backfill activity for {test_family.name}, partition 0"
    )
    mock_logger.info.assert_any_call(
        f"Backfill successfully completed activity logic for {test_family.name}, partition 0"
    )
    assert mock_offline_store.write_batch.call_count >= 1
    assert len(mock_offline_store.write_batch_calls) > 0
    _, rows, _, _ = mock_offline_store.write_batch_calls[0]
    assert len(rows) >= 1

    import polars as pl

    if isinstance(rows, pl.DataFrame):
        assert "event_id" in rows.columns
        complete_rows = rows.filter(pl.col("event_id") == "complete")
        assert not complete_rows.is_empty()
        assert "agg_value_sum" in complete_rows.columns
        assert "agg_event_id_count" in complete_rows.columns
    else:
        complete_row = next((r for r in rows if r.get("event_id") == "complete"), None)
        assert complete_row is not None
        assert "agg_value_sum" in complete_row
        assert "agg_event_id_count" in complete_row


@pytest.mark.asyncio
async def test_backfill_activity_null_values(
    registry: MagicMock,
    test_family: Family,
    mock_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
    backfill_input_data_with_null_values: list,
):
    """Tests the backfill activity's handling of rows with null values."""
    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        import polars as pl

        df = pl.DataFrame(backfill_input_data_with_null_values)
        yield df

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=mock_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )

    details = FamilyBackfillDetails(name=test_family.name, partition=0)

    with (
        patch("src.activities.activity_logger", MagicMock()) as mock_logger,
        patch("src.activities.activity.heartbeat", MagicMock()),
    ):
        await activities.backfill(details)

    mock_logger.info.assert_any_call(
        f"Starting backfill activity for {test_family.name}, partition 0"
    )
    mock_logger.info.assert_any_call(
        f"Backfill successfully completed activity logic for {test_family.name}, partition 0"
    )
    assert mock_offline_store.write_batch.call_count >= 1
    assert len(mock_offline_store.write_batch_calls) > 0
    _, rows, _, _ = mock_offline_store.write_batch_calls[0]
    assert len(rows) >= 1

    import polars as pl

    if isinstance(rows, pl.DataFrame):
        assert "event_id" in rows.columns
        complete_rows = rows.filter(pl.col("event_id") == "complete")
        assert not complete_rows.is_empty()
        assert "agg_value_sum" in complete_rows.columns
        assert "agg_event_id_count" in complete_rows.columns
    else:
        complete_row = next((r for r in rows if r.get("event_id") == "complete"), None)
        assert complete_row is not None
        assert "agg_value_sum" in complete_row
        assert "agg_event_id_count" in complete_row


@pytest.mark.asyncio
async def test_backfill_activity_multiple_partitions(
    registry: MagicMock,
    multi_partition_family: Family,
    mock_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
    backfill_input_data_list: list,
):
    """Tests the backfill activity with multiple partitions."""
    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        try:
            import re

            match = re.search(r"% 4\)=(\d+)", query)
            partition_num = int(match.group(1)) if match else 0
        except Exception:
            partition_num = 0

        import polars as pl

        partition_data = [
            row
            for i, row in enumerate(backfill_input_data_list)
            if i % 4 == partition_num
        ]
        yield pl.DataFrame(partition_data) if partition_data else pl.DataFrame()

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=mock_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )

    results = []
    for partition in range(4):
        details = FamilyBackfillDetails(
            name=multi_partition_family.name, partition=partition
        )

        with (
            patch("src.activities.activity_logger", MagicMock()) as mock_logger,
            patch("src.activities.activity.heartbeat", MagicMock()),
        ):
            await activities.backfill(details)
            mock_logger.info.assert_any_call(
                f"Starting backfill activity for {multi_partition_family.name}, partition {partition}"
            )
            mock_logger.info.assert_any_call(
                f"Backfill successfully completed activity logic for {multi_partition_family.name}, partition {partition}"
            )
            results.append(
                {
                    "partition": partition,
                    "call_count": mock_offline_store.write_batch.call_count,
                    "write_batch_calls": list(mock_offline_store.write_batch_calls),
                }
            )
            mock_offline_store.write_batch.reset_mock()
            mock_offline_store.write_batch_calls = []

    assert len(results) == 4
    for result in results:
        assert result["call_count"] > 0
        for _, _, partition_num, _ in result["write_batch_calls"]:
            assert partition_num == result["partition"]


@pytest.mark.asyncio
async def test_backfill_activity_multiple_aggregations(
    registry: MagicMock,
    multi_agg_family: Family,
    mock_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
    backfill_input_data_list: list,
):
    """Tests the backfill activity with multiple aggregation functions."""
    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        import polars as pl

        df = pl.DataFrame(backfill_input_data_list)
        yield df

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=mock_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )

    details = FamilyBackfillDetails(name=multi_agg_family.name, partition=0)

    with (
        patch("src.activities.activity_logger", MagicMock()) as mock_logger,
        patch("src.activities.activity.heartbeat", MagicMock()),
    ):
        await activities.backfill(details)

    mock_logger.info.assert_any_call(
        f"Starting backfill activity for {multi_agg_family.name}, partition 0"
    )
    mock_logger.info.assert_any_call(
        f"Backfill successfully completed activity logic for {multi_agg_family.name}, partition 0"
    )
    assert mock_offline_store.write_batch.call_count == 2

    all_dfs = []
    for _, df, _, _ in mock_offline_store.write_batch_calls:
        import polars as pl

        if isinstance(df, pl.DataFrame):
            all_dfs.append(df)
        else:
            pass

    import polars as pl

    if all_dfs:
        for df in all_dfs:
            assert "agg_value_sum" in df.columns
            assert "agg_value_avg" in df.columns
            assert "agg_value_stddev" in df.columns
            assert "agg_event_id_count" in df.columns
    else:
        pytest.fail("No DataFrames were written to offline store")


@pytest.mark.asyncio
async def test_backfill_activity_multi_day_grouping(
    registry: MagicMock,
    test_family: Family,
    mock_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
    multi_day_data_list: list,
):
    """Tests the backfill activity's date grouping logic with data spanning multiple days."""
    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        import polars as pl

        df = pl.DataFrame(multi_day_data_list)
        yield df

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=mock_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )

    details = FamilyBackfillDetails(name=test_family.name, partition=0)

    with (
        patch("src.activities.activity_logger", MagicMock()) as mock_logger,
        patch("src.activities.activity.heartbeat", MagicMock()),
    ):
        await activities.backfill(details)

    mock_logger.info.assert_any_call(
        f"Starting backfill activity for {test_family.name}, partition 0"
    )
    mock_logger.info.assert_any_call(
        f"Backfill successfully completed activity logic for {test_family.name}, partition 0"
    )
    assert mock_offline_store.write_batch.call_count >= 3

    dates_written = set()
    for _, rows, _, date_arg in mock_offline_store.write_batch_calls:
        dates_written.add(date_arg)
        import polars as pl

        if isinstance(rows, pl.DataFrame):
            assert "ts" in rows.columns
            dates = rows.select(pl.col("ts").dt.date()).to_series().unique().to_list()
            assert len(dates) == 1
            assert dates[0] == date_arg
        else:
            for row in rows:
                row_date = row["ts"].date()
                assert row_date == date_arg

    assert len(dates_written) >= 3
    base_date = DateType(2023, 10, 26)
    expected_dates = {base_date, DateType(2023, 10, 27), DateType(2023, 10, 28)}
    assert expected_dates.issubset(dates_written)


@pytest.mark.asyncio
async def test_backfill_activity_offline_store_error(
    registry: MagicMock,
    test_family: Family,
    failing_offline_store: MagicMock,
    mock_online_store_backfill: MagicMock,
    backfill_input_data_list: list,
):
    """Tests the backfill activity's error handling when the offline store write fails."""
    mock_batch_source = MagicMock(spec=BatchSource)

    async def mock_async_fetch_batches(query, requested_b_size):
        import polars as pl

        df = pl.DataFrame(backfill_input_data_list)
        yield df

    mock_batch_source.fetch_batches = mock_async_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=failing_offline_store,
        online_store=mock_online_store_backfill,
        family_persistence_activities=MagicMock(),
    )

    details = FamilyBackfillDetails(name=test_family.name, partition=0)

    with (
        patch("src.activities.activity_logger", MagicMock()) as mock_logger,
        patch("src.activities.activity.heartbeat", MagicMock()),
        pytest.raises(Exception) as excinfo,
    ):
        await activities.backfill(details)

    assert excinfo.value is not None
    assert "Simulated S3 write failure" in str(excinfo.value)

    mock_logger.info.assert_any_call(
        f"Starting backfill activity for {test_family.name}, partition 0"
    )
    assert mock_logger.error.call_count > 0
    registry.fetch_one.assert_called_once_with(test_family.name)
