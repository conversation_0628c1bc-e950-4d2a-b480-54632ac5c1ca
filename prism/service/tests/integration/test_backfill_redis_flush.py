import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, patch, MagicMock

import polars as pl

from src.activities import FamilyBackfillActivities
from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    Registry,
    FamilyStatus,
    PipelineSettings,
)
from src.aggregations import (
    AggregationFunction,
    CountAggregation,
    SumAggregation,
    AggregationDescriptor,
)
from src.shared import FamilyBackfillDetails
from src.stores import OnlineStore, OfflineStore, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
from src.persistence_activities import FamilyPersistenceActivities


# --- Fixtures ---


@pytest.fixture
def test_family_config():
    """Configuration for the test family."""
    return FamilyConfig(
        source=SourceConfig(
            batch=BatchSourceConfig(
                table="test_events", late_arriving_data_lag_seconds=3600
            ),
            query="SELECT event_id, ts, user_id, value FROM {{ ref }}",
        ),
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[
            FeatureConfig(
                column="value",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_SUM],
            ),
            FeatureConfig(
                column="event_id",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_COUNT],
            ),
        ],
    )


@pytest.fixture
def test_family(test_family_config):
    """Create a test family with pipeline settings."""
    family = Family(
        name="test_family",
        config=test_family_config,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
        pipeline_settings=PipelineSettings(
            num_partitions=4,
            max_window_seconds=3600 * 24 * 7,
            increment_interval_seconds=3600 * 4,
        ),
    )
    family.id = "test_family_id"

    # Add aggregation descriptors
    family.aggregation_descriptors = [
        AggregationDescriptor(column="value", aggregation=SumAggregation),
        AggregationDescriptor(column="event_id", aggregation=CountAggregation),
    ]

    return family


@pytest.fixture
def registry(test_family):
    """Mock registry that returns the test family."""
    registry = MagicMock(spec=Registry)
    registry.fetch_one = AsyncMock(return_value=test_family)
    return registry


@pytest.fixture
def mock_redis():
    """Create a fakeredis client for testing."""
    import fakeredis.aioredis

    return fakeredis.aioredis.FakeRedis(decode_responses=False)


@pytest.fixture
def online_store(mock_redis):
    """Create an online store with fakeredis."""
    return OnlineStore(mock_redis)


@pytest.fixture
def mock_s3_client():
    """Create a mock S3 client."""
    s3_client = MagicMock()
    s3_client.put_object = AsyncMock()
    return s3_client


@pytest.fixture
def offline_store(mock_s3_client):
    """Create an offline store with mock S3 client."""
    store = OfflineStore(bucket_name="test-bucket", s3_config={})

    # Create a simple list to store the arguments
    store.write_batch_calls = []

    async def mock_write_batch(family, df, partition, date):
        # Record the arguments
        store.write_batch_calls.append((family, df, partition, date))
        return None

    store.write_batch = AsyncMock(side_effect=mock_write_batch)

    # Create a context manager that returns the mock S3 client
    class MockContextManager:
        async def __aenter__(self):
            return mock_s3_client

        async def __aexit__(self, *args):
            pass

    store._get_s3_client = MagicMock(return_value=MockContextManager())
    return store


@pytest.fixture
def persistence_activities(registry, online_store, offline_store):
    """Create family persistence activities with real dependencies."""
    activities = FamilyPersistenceActivities(
        registry=registry,
        online_store=online_store,
        offline_store=offline_store,
    )

    # Create a list to track flush calls
    activities.flush_calls = []

    # Save the original method
    original_flush = activities.flush_entity_redis_to_s3

    # Create a wrapper that tracks calls
    async def flush_wrapper(details):
        activities.flush_calls.append(details)
        return await original_flush(details)

    # Replace the method with our wrapper
    activities.flush_entity_redis_to_s3 = flush_wrapper

    return activities


@pytest.fixture
def backfill_input_data_with_multiple_entities():
    """Create test data with multiple entities, some exceeding the threshold."""
    base_time = datetime(2023, 1, 1, tzinfo=timezone.utc)

    # Create data for each entity
    entity1_data = []
    for i in range(MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10):
        entity1_data.append(
            {
                "ts": base_time + timedelta(hours=i),
                "event_id": f"user1_event_{i}",
                "user_id": "user1",
                "value": i * 10,
            }
        )

    entity2_data = []
    for i in range(MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 10):
        entity2_data.append(
            {
                "ts": base_time + timedelta(hours=i),
                "event_id": f"user2_event_{i}",
                "user_id": "user2",
                "value": i * 10,
            }
        )

    entity3_data = []
    for i in range(MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 5):
        entity3_data.append(
            {
                "ts": base_time + timedelta(hours=i),
                "event_id": f"user3_event_{i}",
                "user_id": "user3",
                "value": i * 10,
            }
        )

    # Combine all data
    all_data = entity1_data + entity2_data + entity3_data

    return all_data


@pytest.mark.asyncio
async def test_backfill_with_real_redis_and_flush(
    registry,
    test_family,
    offline_store,
    online_store,
    mock_redis,
    persistence_activities,
    backfill_input_data_with_multiple_entities,
):
    """
    Integration test for backfill activity with real Redis and flush functionality.

    This test:
    1. Uses the real FamilyBackfillActivities implementation
    2. Uses the real OnlineStore (backed by fakeredis)
    3. Uses the real FamilyPersistenceActivities
    4. Uses a mocked batch source to feed controlled data
    5. Verifies that entities exceeding the threshold are correctly flushed
    """
    # Configure mock batch source
    mock_batch_source = MagicMock()

    async def mock_fetch_batches(*_):
        yield pl.DataFrame(backfill_input_data_with_multiple_entities)

    mock_batch_source.fetch_batches = mock_fetch_batches
    mock_batch_source._transpile_query = MagicMock(side_effect=lambda q: q)

    # Create backfill activities with real dependencies
    activities = FamilyBackfillActivities(
        registry=registry,
        batch_source=mock_batch_source,
        offline_store=offline_store,
        online_store=online_store,
        family_persistence_activities=persistence_activities,
    )

    # Execute backfill activity
    details = FamilyBackfillDetails(name=test_family.name, partition=0)

    with (
        patch("src.activities.activity.logger", MagicMock()),
        patch("src.activities.activity.heartbeat", MagicMock()),
    ):
        await activities.backfill(details)

    # Verify registry was called to fetch the family
    assert registry.fetch_one.call_count > 0
    registry.fetch_one.assert_any_call(test_family.name)

    # Verify Redis keys have the expected number of rows
    for entity_id, expected_length in [
        ("user1", MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS),  # Should be trimmed
        ("user2", MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 10),  # Below threshold
        ("user3", MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS),  # Should be trimmed
    ]:
        key = online_store._key_for(test_family, entity_id)
        length = await mock_redis.llen(key)
        assert length == expected_length, (
            f"Expected {expected_length} rows for {entity_id}, got {length}"
        )

    # Verify flush was called for entities that needed it
    assert len(persistence_activities.flush_calls) == 2

    # Check that the correct entities were flushed
    flushed_entities = [
        details.packed_identifiers for details in persistence_activities.flush_calls
    ]
    assert "user1" in flushed_entities
    assert "user3" in flushed_entities
    assert "user2" not in flushed_entities

    # Verify offline_store.write_batch was called
    assert len(offline_store.write_batch_calls) > 0

    # Verify the correct family name was used
    for family, _, _, _ in offline_store.write_batch_calls:
        assert family.name == test_family.name
