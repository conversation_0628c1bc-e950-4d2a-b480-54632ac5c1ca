import pytest
from datetime import datetime, timezone, timedelta, date as DateType
from unittest.mock import AsyncMock, patch, MagicMock

import polars as pl
from temporalio.testing import ActivityEnvironment

from src.persistence_activities import FamilyPersistenceActivities
from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    Registry,
    FamilyStatus,
    PipelineSettings,
)
from src.aggregations import AggregationFunction
from src.shared import FlushDetails
from src.stores import OnlineStore, OfflineStore, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
from src.utils import calculate_partition
from src.csv_utils import format_row_to_csv_string


@pytest.fixture
def test_family_config():
    """Configuration for the test family."""
    return FamilyConfig(
        source=SourceConfig(
            batch=BatchSourceConfig(
                table="test_events", late_arriving_data_lag_seconds=3600
            ),
            query="SELECT event_id, ts, user_id, value FROM {{ ref }}",
        ),
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[
            FeatureConfig(
                column="value",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_SUM],
            ),
            FeatureConfig(
                column="event_id",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_COUNT],
            ),
        ],
    )


@pytest.fixture
def test_family(test_family_config):
    """Test family with pipeline settings."""
    family = Family(
        name="test_family",
        config=test_family_config,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
        pipeline_settings=PipelineSettings(
            num_partitions=4,
            max_window_seconds=3600 * 24 * 7,
            increment_interval_seconds=3600 * 4,
        ),
    )
    family.id = "test_family_id"
    return family


@pytest.fixture
def mock_registry(test_family):
    """Mock registry that returns the test family."""
    registry = MagicMock(spec=Registry)
    registry.fetch_one = AsyncMock(return_value=test_family)
    return registry


@pytest.fixture
def mock_offline_store():
    """Mock offline store with write_batch method."""
    offline_store = MagicMock(spec=OfflineStore)
    offline_store.write_batch = AsyncMock()
    return offline_store


@pytest.fixture
def mock_redis():
    """Mock Redis client with required methods."""
    redis = MagicMock()
    redis.llen = AsyncMock()
    redis.lrange = AsyncMock()
    redis.ltrim = AsyncMock()
    return redis


@pytest.fixture
def mock_online_store(mock_redis):
    """Mock online store with Redis client."""
    online_store = MagicMock(spec=OnlineStore)
    online_store.redis = mock_redis
    online_store._key_for = MagicMock(return_value="test_family_id:user_1")
    online_store._derive_column_order_for_read = MagicMock(
        return_value=[
            "ts",
            "event_id",
            "user_id",
            "value",
            "agg_value_sum",
            "agg_event_id_count",
        ]
    )
    return online_store


@pytest.fixture
def activity_environment():
    """Temporal activity environment for testing."""
    return ActivityEnvironment()


@pytest.fixture
def persistence_activities(mock_registry, mock_online_store, mock_offline_store):
    """Persistence activities instance with mocked dependencies."""
    return FamilyPersistenceActivities(
        registry=mock_registry,
        online_store=mock_online_store,
        offline_store=mock_offline_store,
    )


@pytest.fixture
def generate_test_rows():
    """Generate test rows with timestamps spanning multiple days."""

    def _generate(num_rows=MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10, user_id="user_1"):
        base_time = datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
        rows = []

        for i in range(num_rows):
            ts = base_time + timedelta(hours=i * 4)
            rows.append(
                {
                    "ts": ts,
                    "event_id": f"event_{i}",
                    "user_id": user_id,
                    "value": i * 10,
                    "agg_value_sum": (float(i * 10), 1),
                    "agg_event_id_count": (1,),
                }
            )

        return rows

    return _generate


@pytest.fixture
def prepare_redis_data(mock_redis, test_family, mock_online_store, generate_test_rows):
    """Prepare Redis data for testing."""

    def _prepare(num_rows=MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10, user_id="user_1"):
        rows = generate_test_rows(num_rows, user_id)

        column_order = [
            "ts",
            "event_id",
            "user_id",
            "value",
            "agg_value_sum",
            "agg_event_id_count",
        ]
        csv_rows = [
            format_row_to_csv_string(row, column_order).encode("utf-8") for row in rows
        ]

        redis_key = f"test_family_id:{user_id}"
        mock_redis.llen.return_value = len(csv_rows)

        async def mock_lrange(key, start, end):
            if start == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS and end == -1:
                return csv_rows[MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS:]
            return []

        mock_redis.lrange = AsyncMock(side_effect=mock_lrange)

        return rows, csv_rows, redis_key

    return _prepare


@pytest.mark.asyncio
async def test_flush_entity_redis_to_s3_success(
    activity_environment,
    persistence_activities,
    mock_registry,
    mock_online_store,
    mock_offline_store,
    mock_redis,
    prepare_redis_data,
    test_family,
):
    """Test successful flush from Redis to S3."""
    packed_identifiers = "user_1"
    rows, csv_rows, redis_key = prepare_redis_data(
        num_rows=MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10, user_id=packed_identifiers
    )

    expected_partition = calculate_partition(
        packed_identifiers, test_family.pipeline_settings.num_partitions
    )

    details = FlushDetails(
        family_name=test_family.name,
        packed_identifiers=packed_identifiers,
    )

    with patch("src.persistence_activities.activity.logger", MagicMock()):
        await activity_environment.run(
            persistence_activities.flush_entity_redis_to_s3,
            details,
        )

    mock_registry.fetch_one.assert_called_once_with(test_family.name)
    mock_redis.llen.assert_called_once_with(redis_key)
    mock_redis.lrange.assert_called_once_with(
        redis_key, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS, -1
    )
    mock_redis.ltrim.assert_called_once_with(
        redis_key, 0, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 1
    )
    assert mock_offline_store.write_batch.call_count > 0

    for call_args in mock_offline_store.write_batch.call_args_list:
        args, kwargs = call_args
        assert kwargs["partition"] == expected_partition
        assert isinstance(kwargs["date"], DateType)
        assert isinstance(kwargs["df"], pl.DataFrame)
        assert not kwargs["df"].is_empty()


@pytest.mark.asyncio
async def test_flush_entity_redis_to_s3_s3_write_failure(
    activity_environment,
    persistence_activities,
    mock_registry,
    mock_online_store,
    mock_offline_store,
    mock_redis,
    prepare_redis_data,
):
    """Test handling of S3 write failure."""
    packed_identifiers = "user_1"
    rows, csv_rows, redis_key = prepare_redis_data(
        num_rows=MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10, user_id=packed_identifiers
    )

    mock_offline_store.write_batch.side_effect = Exception("S3 write failed")

    details = FlushDetails(
        family_name="test_family",
        packed_identifiers=packed_identifiers,
    )

    with (
        patch("src.persistence_activities.activity.logger", MagicMock()),
        pytest.raises(Exception) as excinfo,
    ):
        await activity_environment.run(
            persistence_activities.flush_entity_redis_to_s3,
            details,
        )

    assert "S3 write failed" in str(excinfo.value)
    mock_redis.llen.assert_called_once_with(redis_key)
    mock_redis.lrange.assert_called_once_with(
        redis_key, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS, -1
    )
    mock_redis.ltrim.assert_not_called()


@pytest.mark.asyncio
async def test_flush_entity_redis_to_s3_below_threshold(
    activity_environment,
    persistence_activities,
    mock_registry,
    mock_online_store,
    mock_redis,
):
    """Test case where number of rows is below the threshold."""
    packed_identifiers = "user_1"
    redis_key = f"test_family_id:{packed_identifiers}"
    mock_redis.llen.return_value = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 5

    details = FlushDetails(
        family_name="test_family",
        packed_identifiers=packed_identifiers,
    )

    with patch("src.persistence_activities.activity.logger", MagicMock()):
        await activity_environment.run(
            persistence_activities.flush_entity_redis_to_s3,
            details,
        )

    mock_redis.llen.assert_called_once_with(redis_key)
    mock_redis.lrange.assert_not_called()
    mock_redis.ltrim.assert_not_called()


@pytest.mark.asyncio
async def test_flush_entity_redis_to_s3_empty_result(
    activity_environment,
    persistence_activities,
    mock_registry,
    mock_online_store,
    mock_redis,
):
    """Test case where lrange returns empty result."""
    packed_identifiers = "user_1"
    redis_key = f"test_family_id:{packed_identifiers}"
    mock_redis.llen.return_value = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 10
    mock_redis.lrange.return_value = []

    details = FlushDetails(
        family_name="test_family",
        packed_identifiers=packed_identifiers,
    )

    with patch("src.persistence_activities.activity.logger", MagicMock()):
        await activity_environment.run(
            persistence_activities.flush_entity_redis_to_s3,
            details,
        )

    mock_redis.llen.assert_called_once_with(redis_key)
    mock_redis.lrange.assert_called_once_with(
        redis_key, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS, -1
    )
    mock_redis.ltrim.assert_called_once_with(
        redis_key, 0, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS - 1
    )


@pytest.mark.parametrize(
    "packed_identifiers,num_partitions,expected_partition",
    [
        ("user_1", 4, calculate_partition("user_1", 4)),
        ("user_2", 8, calculate_partition("user_2", 8)),
        ("user_3|123", 16, calculate_partition("user_3|123", 16)),
        ("complex|id|with|pipes", 32, calculate_partition("complex|id|with|pipes", 32)),
    ],
)
def test_partition_calculation(packed_identifiers, num_partitions, expected_partition):
    """Test partition calculation logic."""
    partition = calculate_partition(packed_identifiers, num_partitions)
    assert partition == expected_partition
    assert 0 <= partition < num_partitions
