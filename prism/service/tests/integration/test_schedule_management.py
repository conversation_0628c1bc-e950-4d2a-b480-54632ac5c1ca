import pytest
import pytest_asyncio
import uuid


from src.shared import FamilyDetails

from tests.integration.mock_schedule_activities import MockScheduleActivities


@pytest_asyncio.fixture
async def temporal_client():
    """Create and provide a Temporal client."""
    from unittest.mock import AsyncMock, MagicMock

    mock_temporal_client = MagicMock()
    mock_temporal_client.connect = AsyncMock(return_value=mock_temporal_client)
    mock_temporal_client.get_workflow_handle = MagicMock()
    mock_workflow_handle = MagicMock()
    mock_workflow_handle.result = AsyncMock(return_value=None)
    mock_workflow_handle.fetch_history = AsyncMock(return_value=MagicMock())
    mock_temporal_client.get_workflow_handle.return_value = mock_workflow_handle

    return mock_temporal_client


@pytest_asyncio.fixture
async def grpc_channel(channel):
    """Provide the gRPC channel from the session fixture."""
    return channel


@pytest.fixture
def test_family_name():
    """Generate a unique test family name."""
    return f"test-schedule-family-{uuid.uuid4().hex[:8]}"


def create_test_family_config():
    """Create a test family configuration for integration tests."""
    # Use integer values directly instead of importing AggregationFunction enum
    # 1 = SUM, 2 = COUNT based on the proto definition
    AGGREGATION_FUNCTION_SUM = 1
    AGGREGATION_FUNCTION_COUNT = 2

    # Create a minimal dictionary representation of the config
    config_dict = {
        "source": {
            "batch": {"table": "test_events", "late_arriving_data_lag_seconds": 3600},
            "query": "SELECT event_id, ts, user_id, value FROM {{ ref }}",
        },
        "id_column": "event_id",
        "timestamp_column": "ts",
        "identifier_columns": ["user_id"],
        "features": [
            {"column": "value", "aggregations": [AGGREGATION_FUNCTION_SUM]},
            {"column": "event_id", "aggregations": [AGGREGATION_FUNCTION_COUNT]},
        ],
    }

    return config_dict


@pytest.mark.asyncio
async def test_schedule_creation_on_family_create(temporal_client, test_family_name):
    """Test that a schedule is created when a family is created."""
    # Create a mock schedule activities instance
    mock_schedule_activities = MockScheduleActivities(temporal_client)

    # Create a family details object
    family_details = FamilyDetails(name=test_family_name)

    # Directly call the create_or_update_schedule method
    await mock_schedule_activities.create_or_update_schedule(
        family_details, 3600, "FamilyIncrementalProcessingWorkflow"
    )

    # Verify that a schedule was created
    schedule_id = f"schedule-family-incremental-{test_family_name}"
    schedule = await mock_schedule_activities.get_schedule(schedule_id)

    assert schedule is not None, f"Schedule '{schedule_id}' was not created"
    assert schedule["workflow_type_name"] == "FamilyIncrementalProcessingWorkflow"
    assert schedule["interval_seconds"] == 3600
    assert not schedule["paused"]

    print(f"Schedule '{schedule_id}' verified.")


@pytest.mark.asyncio
async def test_schedule_deletion_on_family_delete(temporal_client, test_family_name):
    """Test that a schedule is deleted when a family is deleted."""
    # Create a mock schedule activities instance
    mock_schedule_activities = MockScheduleActivities(temporal_client)

    # Create a family details object
    family_details = FamilyDetails(name=test_family_name)

    # Create a schedule
    await mock_schedule_activities.create_or_update_schedule(
        family_details, 3600, "FamilyIncrementalProcessingWorkflow"
    )

    # Verify that a schedule was created
    schedule_id = f"schedule-family-incremental-{test_family_name}"
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert schedule is not None, f"Schedule '{schedule_id}' was not created"
    print(f"Schedule '{schedule_id}' exists.")

    # Delete the schedule
    await mock_schedule_activities.delete_schedule(family_details)

    # Verify that the schedule was deleted
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert schedule is None, (
        f"Schedule '{schedule_id}' still exists after family deletion"
    )
    print(f"Schedule '{schedule_id}' was correctly deleted.")


@pytest.mark.asyncio
async def test_schedule_invokes_incremental_workflow(temporal_client, test_family_name):
    """Test that the schedule invokes the FamilyIncrementalProcessingWorkflow."""
    # Create a mock schedule activities instance
    mock_schedule_activities = MockScheduleActivities(temporal_client)

    # Create a family details object
    family_details = FamilyDetails(name=test_family_name)

    # Create a schedule
    await mock_schedule_activities.create_or_update_schedule(
        family_details, 3600, "FamilyIncrementalProcessingWorkflow"
    )

    # Verify that a schedule was created
    schedule_id = f"schedule-family-incremental-{test_family_name}"
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert schedule is not None, f"Schedule '{schedule_id}' was not created"
    print(f"Schedule '{schedule_id}' exists.")

    # Trigger the schedule manually
    await mock_schedule_activities.trigger_schedule(schedule_id)
    print(f"Schedule '{schedule_id}' triggered manually.")

    # Verify the schedule was triggered
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert "last_triggered" in schedule, "Schedule was not triggered"
    assert "last_workflow_id" in schedule, "Workflow was not started"

    # Verify the workflow ID format
    workflow_id = schedule["last_workflow_id"]
    assert workflow_id.startswith(f"family-incremental-run-{test_family_name}"), (
        "Incorrect workflow ID format"
    )
    print(f"Verified workflow ID: {workflow_id}")

    # Clean up - delete the schedule
    await mock_schedule_activities.delete_schedule(family_details)

    # Verify the schedule was deleted
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert schedule is None, f"Schedule '{schedule_id}' still exists after deletion"
