import polars as pl
import pandas as pd


class SnowflakeConnection:
    """Mock Snowflake connection."""

    def __init__(self, *args, **kwargs):
        self.args = args
        self.kwargs = kwargs
        self.is_closed = False
        self._mock_results = []
        self._mock_batches = []

    def cursor(self, *args, **kwargs):
        """Return a mock cursor associated with this connection's mock data."""
        return SnowflakeCursor(self._mock_results, self._mock_batches)

    def close(self):
        """Close the connection."""
        self.is_closed = True

    def set_mock_results(self, results: list[tuple] | pd.DataFrame | pl.DataFrame):
        """Set data to be returned by fetchall/fetch_pandas_all/fetch_polars_all."""
        self._mock_results = results

    def set_mock_batches(
        self, batches: list[list[tuple] | pd.DataFrame | pl.DataFrame]
    ):
        """Set data to be returned by fetch_pandas_batches/fetch_polars_batches."""
        self._mock_batches = batches


class SnowflakeCursor:
    """Mock Snowflake cursor."""

    def __init__(self, mock_results, mock_batches):
        self.query = None
        self.params = None
        self.is_closed = False
        self._results = mock_results
        self._batches = mock_batches
        self._batch_iterator = None
        self.description = [("COL1",), ("COL2",)]

    def execute(self, query, params=None):
        """Execute a query."""
        self.query = query
        self.params = params
        self._batch_iterator = iter(self._batches)
        return self

    def fetchall(self):
        """Fetch all results (list of tuples)."""
        if isinstance(self._results, (pd.DataFrame, pl.DataFrame)):
            return list(self._results.to_records(index=False))
        return self._results

    def fetch_pandas_all(self):
        """Fetch all results as Pandas DataFrame."""
        if isinstance(self._results, pl.DataFrame):
            return self._results.to_pandas()
        elif isinstance(self._results, pd.DataFrame):
            return self._results
        else:
            colnames = (
                [desc[0] for desc in self.description] if self.description else []
            )
            return pd.DataFrame(self._results, columns=colnames)

    def fetch_polars_all(self):
        """Fetch all results as Polars DataFrame."""
        if isinstance(self._results, pd.DataFrame):
            return pl.from_pandas(self._results)
        elif isinstance(self._results, pl.DataFrame):
            return self._results
        else:
            colnames = (
                [desc[0] for desc in self.description] if self.description else []
            )
            return pl.DataFrame(self._results, schema=colnames)

    def fetch_pandas_batches(self):
        """Yield Pandas DataFrames."""
        for batch in self._batches:
            if isinstance(batch, pl.DataFrame):
                yield batch.to_pandas()
            elif isinstance(batch, pd.DataFrame):
                yield batch
            else:
                colnames = (
                    [desc[0] for desc in self.description] if self.description else []
                )
                yield pd.DataFrame(batch, columns=colnames)

    def fetch_polars_batches(self):
        """Yield Polars DataFrames."""
        for batch in self._batches:
            if isinstance(batch, pd.DataFrame):
                yield pl.from_pandas(batch)
            elif isinstance(batch, pl.DataFrame):
                yield batch
            else:
                colnames = (
                    [desc[0] for desc in self.description] if self.description else []
                )
                yield pl.DataFrame(batch, schema=colnames)

    def close(self):
        """Close the cursor."""
        self.is_closed = True


connector = type(
    "connector",
    (),
    {
        "connect": lambda *args, **kwargs: SnowflakeConnection(*args, **kwargs),
        "SnowflakeConnection": SnowflakeConnection,
        "SnowflakeCursor": SnowflakeCursor,
    },
)
snowflake = type(
    "snowflake",
    (),
    {
        "connector": connector,
        "SnowflakeConnection": SnowflakeConnection,
        "connect": lambda *args, **kwargs: SnowflakeConnection(*args, **kwargs),
    },
)
