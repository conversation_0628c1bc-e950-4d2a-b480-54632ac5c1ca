from datetime import datetime, timezone, timedelta
from temporalio import activity
from temporalio.exceptions import ApplicationError


class MockFamilyStatus:
    FAMILY_STATUS_RUNNING = 5
    FAMILY_STATUS_RUNNING_FAILED = 6
    FAMILY_STATUS_INITIALIZING_FAILED = 2
    FAMILY_STATUS_BACKFILLING_FAILED = 4


class MockActivities:
    """Container for mock activity functions used in workflow tests."""

    def __init__(self, initial_frontier=None):
        self.frontier = initial_frontier or datetime(
            2023, 1, 1, 0, 0, 0, tzinfo=timezone.utc
        )
        self.frontier_history = [self.frontier]
        self.call_counts = {
            "plan": 0,
            "update_status": 0,
            "backfill": 0,
            "get_frontier": 0,
            "set_frontier": 0,
            "process_batch": 0,
            "create_or_update_schedule": 0,
            "delete_schedule": 0,
            "get_family_config": 0,
        }
        self.last_status_update = None
        self.advance_frontier_on_process = True
        self.fail_process_batch_non_retryable = False
        self.simulated_backfill_failure = False
        self.iteration_count = 0
        self.continue_as_new_called = False

        self.should_fail = {
            "plan": False,
            "update_status": False,
            "backfill": False,
            "get_frontier": False,
            "set_frontier": False,
            "process_batch": False,
            "create_or_update_schedule": False,
            "delete_schedule": False,
            "get_family_config": False,
        }
        self.fail_on_call_number = {
            "plan": 0,
            "update_status": 0,
            "backfill": 0,
            "get_frontier": 0,
            "set_frontier": 0,
            "process_batch": 0,
            "create_or_update_schedule": 0,
            "delete_schedule": 0,
            "get_family_config": 0,
        }

    def configure_failure(
        self, activity_name, fail_on_call_number=1, non_retryable=False
    ):
        """Configure an activity to fail on a specific call number."""
        self.should_fail[activity_name] = True
        self.fail_on_call_number[activity_name] = fail_on_call_number
        if activity_name == "process_batch":
            self.fail_process_batch_non_retryable = non_retryable
        if activity_name == "backfill":
            self.simulated_backfill_failure = True

    def reset_call_counts(self):
        """Reset call counts for tests."""
        for key in self.call_counts:
            self.call_counts[key] = 0
        self.frontier_history = [self.frontier]
        self.last_status_update = None
        self.advance_frontier_on_process = True
        self.fail_process_batch_non_retryable = False
        self.simulated_backfill_failure = False
        self.iteration_count = 0
        self.continue_as_new_called = False
        for key in self.should_fail:
            self.should_fail[key] = False
            self.fail_on_call_number[key] = 0

    @activity.defn(name="plan")
    async def plan(self, family_details):
        """Mock implementation of the plan activity."""
        self.call_counts["plan"] += 1
        if (
            self.should_fail["plan"]
            and self.call_counts["plan"] == self.fail_on_call_number["plan"]
        ):
            raise Exception("Simulated plan failure")
        return {"num_partitions": 4, "increment_interval_seconds": 3600}

    @activity.defn(name="update_status")
    async def update_status(self, update_details):
        """Mock implementation of the update_status activity."""
        self.call_counts["update_status"] += 1
        name = getattr(update_details, "name", "unknown")
        status = getattr(update_details, "status", "unknown")
        if hasattr(status, "value"):
            status = status.value
        status_detail = getattr(update_details, "status_detail", "")
        self.last_status_update = {
            "name": name,
            "status": status,
            "detail": status_detail,
        }
        if (
            self.should_fail["update_status"]
            and self.call_counts["update_status"]
            == self.fail_on_call_number["update_status"]
        ):
            raise Exception("Simulated update_status failure")
        return None

    @activity.defn(name="backfill")
    async def backfill(self, family_backfill_details):
        """Mock implementation of the backfill activity."""
        self.call_counts["backfill"] += 1
        if (
            self.should_fail["backfill"]
            and self.call_counts["backfill"] == self.fail_on_call_number["backfill"]
        ):
            raise Exception("Simulated backfill activity failure")
        return None

    @activity.defn(name="get_frontier")
    async def get_frontier(self, family_details):
        """Mock implementation of the get_frontier activity."""
        self.call_counts["get_frontier"] += 1
        if (
            self.should_fail["get_frontier"]
            and self.call_counts["get_frontier"]
            == self.fail_on_call_number["get_frontier"]
        ):
            raise ApplicationError(
                "Simulated non-retryable get_frontier failure",
                type="SimulatedFailure",
                non_retryable=True,
            )
        if self.frontier.tzinfo is None:
            self.frontier = self.frontier.replace(tzinfo=timezone.utc)
        return self.frontier.isoformat()

    @activity.defn(name="set_frontier")
    async def set_frontier(self, family_details, new_frontier_iso):
        """Mock implementation of the set_frontier activity."""
        self.call_counts["set_frontier"] += 1
        if (
            self.should_fail["set_frontier"]
            and self.call_counts["set_frontier"]
            == self.fail_on_call_number["set_frontier"]
        ):
            raise ApplicationError(
                "Simulated non-retryable set_frontier failure",
                type="SimulatedFailure",
                non_retryable=True,
            )
        try:
            new_frontier_dt = datetime.fromisoformat(new_frontier_iso)
            if new_frontier_dt.tzinfo is None:
                new_frontier_dt = new_frontier_dt.replace(tzinfo=timezone.utc)
            self.frontier = new_frontier_dt
            self.frontier_history.append(new_frontier_dt)
        except ValueError:
            raise
        return None

    @activity.defn(name="process_incremental_batch")
    async def process_incremental_batch(self, family_details, current_frontier_iso):
        """Mock implementation of the process_incremental_batch activity."""
        self.call_counts["process_batch"] += 1
        self.iteration_count += 1

        if (
            self.should_fail["process_batch"]
            and self.call_counts["process_batch"]
            == self.fail_on_call_number["process_batch"]
        ):
            if self.fail_process_batch_non_retryable:
                raise ApplicationError(
                    "Simulated non-retryable process_batch failure",
                    type="SimulatedFailure",
                    non_retryable=True,
                )
            else:
                raise Exception("Simulated retryable process_batch failure")

        try:
            current_frontier_dt = datetime.fromisoformat(current_frontier_iso)
            if current_frontier_dt.tzinfo is None:
                current_frontier_dt = current_frontier_dt.replace(tzinfo=timezone.utc)
        except ValueError:
            raise

        if self.advance_frontier_on_process:
            new_frontier_dt = current_frontier_dt + timedelta(hours=1)
            return new_frontier_dt.isoformat()
        else:
            return current_frontier_dt.isoformat()

    @activity.defn(name="create_or_update_schedule")
    async def create_or_update_schedule(
        self, family_details, interval_seconds, workflow_type_name
    ):
        """Mock implementation of the create_or_update_schedule activity."""
        self.call_counts["create_or_update_schedule"] += 1

        if (
            self.should_fail["create_or_update_schedule"]
            and self.call_counts["create_or_update_schedule"]
            == self.fail_on_call_number["create_or_update_schedule"]
        ):
            raise ApplicationError(
                "Simulated non-retryable create_or_update_schedule failure",
                type="SimulatedFailure",
                non_retryable=True,
            )
        return None

    @activity.defn(name="delete_schedule")
    async def delete_schedule(self, family_details):
        """Mock implementation of the delete_schedule activity."""
        self.call_counts["delete_schedule"] += 1

        if (
            self.should_fail["delete_schedule"]
            and self.call_counts["delete_schedule"]
            == self.fail_on_call_number["delete_schedule"]
        ):
            raise ApplicationError(
                "Simulated non-retryable delete_schedule failure",
                type="SimulatedFailure",
                non_retryable=True,
            )
        return None

    @activity.defn(name="get_family_config")
    async def get_family_config(self, family_details):
        """Mock implementation of the get_family_config activity."""
        self.call_counts["get_family_config"] += 1

        if (
            self.should_fail["get_family_config"]
            and self.call_counts["get_family_config"]
            == self.fail_on_call_number["get_family_config"]
        ):
            raise ApplicationError(
                "Simulated non-retryable get_family_config failure",
                type="SimulatedFailure",
                non_retryable=True,
            )

        return {"late_arriving_data_lag_seconds": 3600}
