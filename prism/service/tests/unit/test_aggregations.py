import pyarrow as pa
import numpy as np

from src.aggregations import (
    AverageAggregation,
    CountAggregation,
    StandardDeviationAggregation,
    SumAggregation,
)


def test_count():
    state = CountAggregation.accumulate("a", None)
    assert state == (1,)

    state = CountAggregation.accumulate("b", state)
    assert state == (2,)

    state = CountAggregation.accumulate(None, state)
    assert state == (2,)

    value = CountAggregation.compute(pa.array([1, 2, None, 4]))
    assert np.allclose(
        value.to_numpy(zero_copy_only=False), np.array([1, 2, 0, 4]), equal_nan=True
    )

    value = CountAggregation.compute_windowed(
        pa.array([1, 2, None, None]), pa.array([2, 4, None, 8])
    )
    assert np.allclose(
        value.to_numpy(zero_copy_only=False), np.array([1, 2, 0, 8]), equal_nan=True
    )


def test_average():
    state = AverageAggregation.accumulate(1, None)
    assert state == (1.0, 1)

    state = AverageAggregation.accumulate(2, state)
    assert state == (1.5, 2)

    state_before_none = state
    state = AverageAggregation.accumulate(None, state)
    assert state == state_before_none

    state_average = pa.array([1, 1.5, None])
    state_count = pa.array([1, 2, None])
    value = AverageAggregation.compute(state_average, state_count)
    assert np.allclose(
        value.to_numpy(zero_copy_only=False),
        np.array([1.0, 1.5, np.nan]),
        equal_nan=True,
    )

    start_state_average = pa.array([1, 1.5, None, None])
    start_state_count = pa.array([1, 2, None, None])
    end_state_average = pa.array([1.5, 3.0, None, 2.0])
    end_state_count = pa.array([2, 4, None, 1])
    value = AverageAggregation.compute_windowed(
        start_state_average, start_state_count, end_state_average, end_state_count
    )
    assert np.allclose(
        value.to_numpy(zero_copy_only=False),
        np.array([2.0, 4.5, np.nan, 2.0]),
        equal_nan=True,
    )


def test_sum():
    state = SumAggregation.accumulate(1, None)
    assert state == (1.0, 1)

    state = SumAggregation.accumulate(2, state)
    assert state == (1.5, 2)

    state_before_none = state
    state = SumAggregation.accumulate(None, state)
    assert state == state_before_none

    state_average = pa.array([1, 1.5, None])
    state_count = pa.array([1, 2, None])
    value = SumAggregation.compute(state_average, state_count)
    assert np.allclose(
        value.to_numpy(zero_copy_only=False), np.array([1.0, 3.0, 0.0]), equal_nan=True
    )

    start_state_average = pa.array([1, 1.5, None, None])
    start_state_count = pa.array([1, 2, None, None])
    end_state_average = pa.array([1.5, 3.0, None, 2.0])
    end_state_count = pa.array([2, 4, None, 1])
    value = SumAggregation.compute_windowed(
        start_state_average, start_state_count, end_state_average, end_state_count
    )
    assert np.allclose(
        value.to_numpy(zero_copy_only=False),
        np.array([2.0, 9.0, 0.0, 2.0]),
        equal_nan=True,
    )


def test_standard_deviation():
    state = StandardDeviationAggregation.accumulate(1, None)
    assert state == (1.0, 1.0, 1)

    state = StandardDeviationAggregation.accumulate(2, state)
    assert state == (1.5, 2.5, 2)

    state_before_none = state
    state = StandardDeviationAggregation.accumulate(None, state)
    assert state == state_before_none

    state_average = pa.array([1, 1.5, None])
    state_average_of_square = pa.array([1, 2.5, None])
    state_count = pa.array([1, 2, None])
    value = StandardDeviationAggregation.compute(
        state_average, state_average_of_square, state_count
    )
    assert np.allclose(
        value.to_numpy(zero_copy_only=False),
        np.array([0.0, 0.5, np.nan]),
        equal_nan=True,
    )

    start_state_average = pa.array([1, 1.5, None, None])
    start_state_average_of_square = pa.array([1, 2.5, None, None])
    start_state_count = pa.array([1, 2, None, None])
    end_state_average = pa.array([1.5, 3.0, None, 2.0])
    end_state_average_of_square = pa.array([2.5, 11.5, None, 4.0])
    end_state_count = pa.array([2, 4, None, 1])
    value = StandardDeviationAggregation.compute_windowed(
        start_state_average,
        start_state_average_of_square,
        start_state_count,
        end_state_average,
        end_state_average_of_square,
        end_state_count,
    )
    assert np.allclose(
        value.to_numpy(zero_copy_only=False),
        np.array([0.0, 0.5, np.nan, 0.0]),
        equal_nan=True,
    )
