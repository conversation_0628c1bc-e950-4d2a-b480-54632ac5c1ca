import pytest
from datetime import datetime

from src.shared import FamilyDetails
from tests.unit.mock_activities import MockActivities


@pytest.fixture
def mock_activities():
    """Provides a fresh MockActivities instance for each test."""
    return MockActivities()


@pytest.fixture
def family_details():
    """Provides standard FamilyDetails for workflow input."""
    return FamilyDetails(name="test-family-incremental")


@pytest.mark.asyncio
async def test_incremental_workflow_happy_path(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests the successful execution path of the incremental workflow by directly calling activities."""
    mock_activities.advance_frontier_on_process = True
    initial_frontier = mock_activities.frontier
    mock_activities.reset_call_counts()

    frontier_iso = await mock_activities.get_frontier(family_details)
    new_frontier_iso = await mock_activities.process_incremental_batch(
        family_details, frontier_iso
    )
    await mock_activities.set_frontier(family_details, new_frontier_iso)

    assert mock_activities.call_counts["get_frontier"] == 1
    assert mock_activities.call_counts["process_batch"] == 1
    assert mock_activities.call_counts["set_frontier"] == 1

    assert mock_activities.frontier > initial_frontier
    assert len(mock_activities.frontier_history) == 2


@pytest.mark.asyncio
async def test_incremental_workflow_no_new_data(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests the workflow when no new data is available."""
    mock_activities.reset_call_counts()

    frontier_iso = await mock_activities.get_frontier(family_details)
    new_frontier_iso = await mock_activities.process_incremental_batch(
        family_details, frontier_iso
    )

    new_frontier = datetime.fromisoformat(new_frontier_iso)
    frontier = datetime.fromisoformat(frontier_iso)

    if new_frontier > frontier:
        await mock_activities.set_frontier(family_details, new_frontier_iso)

    assert mock_activities.call_counts["get_frontier"] == 1
    assert mock_activities.call_counts["process_batch"] == 1

    assert len(mock_activities.frontier_history) >= 1


@pytest.mark.asyncio
async def test_incremental_workflow_get_frontier_failure(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that a failure in get_frontier is handled properly."""
    mock_activities.reset_call_counts()
    mock_activities.configure_failure(
        "get_frontier", fail_on_call_number=1, non_retryable=True
    )

    with pytest.raises(Exception) as excinfo:
        await mock_activities.get_frontier(family_details)

    assert "Simulated non-retryable get_frontier failure" in str(excinfo.value)

    assert mock_activities.call_counts["get_frontier"] == 1
    assert mock_activities.call_counts["process_batch"] == 0
    assert mock_activities.call_counts["set_frontier"] == 0


@pytest.mark.asyncio
async def test_incremental_workflow_process_batch_failure(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that a failure in process_incremental_batch is handled properly."""
    mock_activities.reset_call_counts()
    mock_activities.configure_failure(
        "process_batch", fail_on_call_number=1, non_retryable=True
    )

    frontier_iso = await mock_activities.get_frontier(family_details)

    with pytest.raises(Exception) as excinfo:
        await mock_activities.process_incremental_batch(family_details, frontier_iso)

    assert "Simulated non-retryable process_batch failure" in str(excinfo.value)

    assert mock_activities.call_counts["get_frontier"] == 1
    assert mock_activities.call_counts["process_batch"] == 1
    assert mock_activities.call_counts["set_frontier"] == 0


@pytest.mark.asyncio
async def test_incremental_workflow_set_frontier_failure(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that a failure in set_frontier is handled properly."""
    mock_activities.advance_frontier_on_process = True
    mock_activities.reset_call_counts()
    mock_activities.configure_failure(
        "set_frontier", fail_on_call_number=1, non_retryable=True
    )

    frontier_iso = await mock_activities.get_frontier(family_details)

    new_frontier_iso = await mock_activities.process_incremental_batch(
        family_details, frontier_iso
    )

    with pytest.raises(Exception) as excinfo:
        await mock_activities.set_frontier(family_details, new_frontier_iso)

    assert "Simulated non-retryable set_frontier failure" in str(excinfo.value)

    assert mock_activities.call_counts["get_frontier"] == 1
    assert mock_activities.call_counts["process_batch"] == 1
    assert mock_activities.call_counts["set_frontier"] == 1
