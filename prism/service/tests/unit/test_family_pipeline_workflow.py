import pytest
from datetime import timedelta, datetime, timezone

from temporalio import activity

from src.shared import FamilyDetails, FamilyBackfillDetails, UpdateFamilyStatusDetails
from src.families import FamilyStatus

from tests.unit.mock_activities import MockActivities


@pytest.fixture
def mock_activities():
    """Provides a fresh MockActivities instance for each test."""
    return MockActivities()


@pytest.fixture
def family_details():
    """Provides standard FamilyDetails for workflow input."""
    return FamilyDetails(name="test-family-workflow")


@pytest.mark.asyncio
async def test_family_pipeline_happy_path(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests the successful execution path including setup and schedule creation by directly calling activities."""
    mock_activities.reset_call_counts()

    update_details_initializing = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_INITIALIZING,
        status_detail="Starting family pipeline",
    )
    await mock_activities.update_status(update_details_initializing)

    plan_result = await mock_activities.plan(family_details)
    num_partitions = plan_result["num_partitions"]
    increment_interval_seconds = plan_result["increment_interval_seconds"]

    update_details_backfilling = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_BACKFILLING,
        status_detail=f"Backfilling {num_partitions} partitions",
    )
    await mock_activities.update_status(update_details_backfilling)

    for partition in range(num_partitions):
        backfill_details = FamilyBackfillDetails(
            name=family_details.name, partition=partition
        )
        await mock_activities.backfill(backfill_details)

    await mock_activities.create_or_update_schedule(
        family_details,
        increment_interval_seconds,
        "FamilyIncrementalProcessingWorkflow",
    )

    family_config = await mock_activities.get_family_config(family_details)

    current_time = datetime.now(timezone.utc)
    new_frontier = current_time - timedelta(
        seconds=family_config["late_arriving_data_lag_seconds"]
    )
    await mock_activities.set_frontier(family_details, new_frontier.isoformat())

    update_details_running = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
        status_detail="Initial setup and backfill complete. Incremental processing scheduled.",
    )
    await mock_activities.update_status(update_details_running)

    assert mock_activities.call_counts["plan"] == 1
    assert mock_activities.call_counts["update_status"] == 3
    assert mock_activities.call_counts["backfill"] == num_partitions
    assert mock_activities.call_counts["create_or_update_schedule"] == 1
    assert mock_activities.call_counts["get_family_config"] == 1
    assert mock_activities.call_counts["set_frontier"] == 1


@pytest.mark.asyncio
async def test_family_pipeline_schedule_creation_parameters(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that the workflow correctly passes parameters to schedule creation."""
    mock_activities.reset_call_counts()

    custom_interval = 7200
    original_plan = mock_activities.plan

    @activity.defn(name="plan")
    async def custom_plan(family_details):
        result = await original_plan(family_details)
        result["increment_interval_seconds"] = custom_interval
        return result

    mock_activities.plan = custom_plan

    plan_result = await mock_activities.plan(family_details)

    await mock_activities.create_or_update_schedule(
        family_details,
        plan_result["increment_interval_seconds"],
        "FamilyIncrementalProcessingWorkflow",
    )

    assert plan_result["increment_interval_seconds"] == custom_interval
    assert mock_activities.call_counts["create_or_update_schedule"] == 1

    mock_activities.plan = original_plan


@pytest.mark.asyncio
async def test_family_pipeline_schedule_creation_failure(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that a failure in schedule creation is handled properly."""
    mock_activities.reset_call_counts()

    mock_activities.should_fail["create_or_update_schedule"] = True
    mock_activities.fail_on_call_number["create_or_update_schedule"] = 1

    update_details_initializing = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_INITIALIZING,
        status_detail="Starting family pipeline",
    )
    await mock_activities.update_status(update_details_initializing)

    plan_result = await mock_activities.plan(family_details)

    update_details_backfilling = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_BACKFILLING,
        status_detail="Starting backfill",
    )
    await mock_activities.update_status(update_details_backfilling)

    backfill_details = FamilyBackfillDetails(name=family_details.name, partition=0)
    await mock_activities.backfill(backfill_details)

    with pytest.raises(Exception) as excinfo:
        await mock_activities.create_or_update_schedule(
            family_details,
            plan_result["increment_interval_seconds"],
            "FamilyIncrementalProcessingWorkflow",
        )

    assert "Simulated non-retryable create_or_update_schedule failure" in str(
        excinfo.value
    )

    update_details_failed = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_INITIALIZING_FAILED,
        status_detail="Failed to create schedule",
    )
    await mock_activities.update_status(update_details_failed)

    assert mock_activities.call_counts["plan"] == 1
    assert mock_activities.call_counts["backfill"] == 1
    assert mock_activities.call_counts["create_or_update_schedule"] == 1
    assert mock_activities.call_counts["update_status"] == 3
    assert (
        mock_activities.last_status_update["status"]
        == FamilyStatus.FAMILY_STATUS_INITIALIZING_FAILED.value
    )


@pytest.mark.asyncio
async def test_family_pipeline_delete_schedule(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that the delete_schedule activity is called when needed."""
    mock_activities.reset_call_counts()

    await mock_activities.delete_schedule(family_details)

    assert mock_activities.call_counts["delete_schedule"] == 1


@pytest.mark.asyncio
async def test_family_pipeline_delete_schedule_failure(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that a failure in delete_schedule is handled properly."""
    mock_activities.reset_call_counts()

    mock_activities.configure_failure(
        "delete_schedule", fail_on_call_number=1, non_retryable=True
    )

    with pytest.raises(Exception) as excinfo:
        await mock_activities.delete_schedule(family_details)

    assert "Simulated non-retryable delete_schedule failure" in str(excinfo.value)

    assert mock_activities.call_counts["delete_schedule"] == 1


@pytest.mark.asyncio
async def test_family_pipeline_plan_failure(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that a failure during the initial plan activity is handled properly."""
    mock_activities.reset_call_counts()

    mock_activities.should_fail["plan"] = True
    mock_activities.fail_on_call_number["plan"] = 1

    update_details_initializing = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_INITIALIZING,
        status_detail="Starting family pipeline",
    )
    await mock_activities.update_status(update_details_initializing)

    with pytest.raises(Exception) as excinfo:
        await mock_activities.plan(family_details)

    assert "Simulated plan failure" in str(excinfo.value)

    update_details_failed = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_INITIALIZING_FAILED,
        status_detail="Failed to plan family",
    )
    await mock_activities.update_status(update_details_failed)

    assert mock_activities.call_counts["plan"] == 1
    assert mock_activities.call_counts["update_status"] == 2


@pytest.mark.asyncio
async def test_family_pipeline_backfill_failure(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that a failure during the backfill phase is handled properly."""
    mock_activities.reset_call_counts()

    mock_activities.should_fail["backfill"] = True
    mock_activities.fail_on_call_number["backfill"] = 1

    update_details_initializing = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_INITIALIZING,
        status_detail="Starting family pipeline",
    )
    await mock_activities.update_status(update_details_initializing)

    await mock_activities.plan(family_details)

    update_details_backfilling = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_BACKFILLING,
        status_detail="Starting backfill",
    )
    await mock_activities.update_status(update_details_backfilling)

    backfill_details = FamilyBackfillDetails(name=family_details.name, partition=0)
    with pytest.raises(Exception) as excinfo:
        await mock_activities.backfill(backfill_details)

    assert "Simulated backfill activity failure" in str(excinfo.value)

    update_details_failed = UpdateFamilyStatusDetails(
        name=family_details.name,
        status=FamilyStatus.FAMILY_STATUS_BACKFILLING_FAILED,
        status_detail="Failed to backfill family",
    )
    await mock_activities.update_status(update_details_failed)

    assert mock_activities.call_counts["plan"] == 1
    assert mock_activities.call_counts["backfill"] == 1
    assert mock_activities.call_counts["update_status"] == 3


@pytest.mark.asyncio
async def test_family_pipeline_workflow_type_name(
    mock_activities: MockActivities,
    family_details: FamilyDetails,
):
    """Tests that the correct workflow type name is passed to schedule creation."""
    mock_activities.reset_call_counts()

    captured_workflow_type = None

    @activity.defn(name="create_or_update_schedule")
    async def custom_create_schedule(
        family_details, interval_seconds, workflow_type_name
    ):
        nonlocal captured_workflow_type
        captured_workflow_type = workflow_type_name
        return None

    await custom_create_schedule(
        family_details,
        3600,
        "FamilyIncrementalProcessingWorkflow",
    )

    assert captured_workflow_type == "FamilyIncrementalProcessingWorkflow"
