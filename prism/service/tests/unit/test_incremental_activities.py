import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, patch, MagicMock

import polars as pl

from src.activities import FamilyIncrementalActivities
from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    Registry,
    FamilyStatus,
    PipelineSettings,
    FamilyNotFoundError,
)
from src.aggregations import AggregationFunction
from src.shared import FamilyDetails, FlushDetails
from src.sources.batch.base import BatchSource
from src.stores import OnlineStore, OfflineStore


@pytest.fixture
def test_family_config():
    """Configuration for the test family."""
    return FamilyConfig(
        source=SourceConfig(
            batch=BatchSourceConfig(
                table="test_events", late_arriving_data_lag_seconds=3600
            ),
            query="SELECT event_id, ts, user_id, value FROM {{ ref }}",
        ),
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[
            FeatureConfig(
                column="value",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_SUM],
            ),
            FeatureConfig(
                column="event_id",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_COUNT],
            ),
        ],
    )


@pytest.fixture
def test_family(test_family_config):
    """Test family with pipeline settings."""
    family = Family(
        name="test_family",
        config=test_family_config,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
        pipeline_settings=PipelineSettings(
            num_partitions=4,
            max_window_seconds=3600 * 24 * 7,
            increment_interval_seconds=3600 * 4,
        ),
    )
    family.id = "test_family_id"
    family.frontier = datetime(2023, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    return family


@pytest.fixture
def family_details():
    """Family details for activity input."""
    return FamilyDetails(name="test_family")


@pytest.fixture
def mock_registry(test_family):
    """Mock registry that returns the test family."""
    registry = MagicMock(spec=Registry)
    registry.fetch_one = AsyncMock(return_value=test_family)
    registry.update_frontier = AsyncMock()
    return registry


@pytest.fixture
def mock_batch_source():
    """Mock batch source."""
    batch_source = MagicMock(spec=BatchSource)
    batch_source._transpile_query = MagicMock(side_effect=lambda q: q)
    return batch_source


@pytest.fixture
def mock_online_store():
    """Mock online store."""
    online_store = MagicMock(spec=OnlineStore)
    online_store.write_rows = AsyncMock(return_value=[])
    online_store.read_recent_rows = AsyncMock(return_value=[])
    return online_store


@pytest.fixture
def mock_offline_store():
    """Mock offline store."""
    offline_store = MagicMock(spec=OfflineStore)
    offline_store.write_batch = AsyncMock()
    return offline_store


@pytest.fixture
def mock_persistence_activities():
    """Mock persistence activities."""
    persistence_activities = MagicMock()
    persistence_activities.flush_entity_redis_to_s3 = AsyncMock()
    return persistence_activities


@pytest.fixture
def incremental_activities(
    mock_registry,
    mock_batch_source,
    mock_online_store,
    mock_offline_store,
    mock_persistence_activities,
):
    """Create incremental activities with mock dependencies."""
    return FamilyIncrementalActivities(
        registry=mock_registry,
        batch_source=mock_batch_source,
        online_store=mock_online_store,
        offline_store=mock_offline_store,
        persistence_activities=mock_persistence_activities,
    )


@pytest.fixture
def incremental_data():
    """Generate incremental data for testing."""
    base_time = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
    data = [
        {
            "event_id": "u1_e1",
            "ts": base_time + timedelta(hours=1),
            "user_id": "user_1",
            "value": 10,
        },
        {
            "event_id": "u2_e1",
            "ts": base_time + timedelta(hours=2),
            "user_id": "user_2",
            "value": 100,
        },
        {
            "event_id": "u1_e2",
            "ts": base_time + timedelta(hours=3),
            "user_id": "user_1",
            "value": 5,
        },
    ]
    return data


@pytest.mark.asyncio
async def test_get_frontier_success(
    incremental_activities, family_details, test_family
):
    """Test successful retrieval of frontier timestamp."""
    result = await incremental_activities.get_frontier(family_details)
    assert result == test_family.frontier.isoformat()
    incremental_activities.registry.fetch_one.assert_called_once_with(
        family_details.name
    )


@pytest.mark.asyncio
async def test_get_frontier_error(incremental_activities, family_details):
    """Test error handling when fetching frontier fails."""
    incremental_activities.registry.fetch_one = AsyncMock(
        side_effect=FamilyNotFoundError("Family not found")
    )
    with pytest.raises(FamilyNotFoundError, match="Family not found"):
        await incremental_activities.get_frontier(family_details)
    incremental_activities.registry.fetch_one.assert_called_once_with(
        family_details.name
    )


@pytest.mark.asyncio
async def test_set_frontier_success(incremental_activities, family_details):
    """Test successful update of frontier timestamp."""
    new_frontier_dt = datetime(2023, 1, 2, 0, 0, 0, tzinfo=timezone.utc)
    new_frontier_iso = new_frontier_dt.isoformat()
    await incremental_activities.set_frontier(family_details, new_frontier_iso)
    incremental_activities.registry.update_frontier.assert_called_once_with(
        family_details.name, new_frontier_dt
    )


@pytest.mark.asyncio
async def test_set_frontier_invalid_input(incremental_activities, family_details):
    """Test handling of invalid frontier timestamp string."""
    invalid_iso_string = "not-a-valid-iso-string"
    with pytest.raises(ValueError):
        await incremental_activities.set_frontier(family_details, invalid_iso_string)


@pytest.mark.asyncio
async def test_set_frontier_error(incremental_activities, family_details):
    """Test error handling when updating frontier fails in the registry."""
    new_frontier_dt = datetime(2023, 1, 2, 0, 0, 0, tzinfo=timezone.utc)
    new_frontier_iso = new_frontier_dt.isoformat()
    incremental_activities.registry.update_frontier = AsyncMock(
        side_effect=Exception("Failed to update frontier")
    )
    with pytest.raises(Exception, match="Failed to update frontier"):
        await incremental_activities.set_frontier(family_details, new_frontier_iso)
    incremental_activities.registry.update_frontier.assert_called_once_with(
        family_details.name, new_frontier_dt
    )


@pytest.mark.asyncio
async def test_process_incremental_batch_no_new_data(
    incremental_activities, family_details, test_family
):
    """Test behavior when no new data is available."""
    current_frontier_dt = test_family.frontier
    current_frontier_iso = current_frontier_dt.isoformat()

    async def mock_fetch_batches(*args, **kwargs):
        yield pl.DataFrame()

    incremental_activities.batch_source.fetch_batches = mock_fetch_batches
    with patch("temporalio.activity.heartbeat"):
        result_iso = await incremental_activities.process_incremental_batch(
            family_details, current_frontier_iso
        )
    assert result_iso == current_frontier_iso
    incremental_activities.online_store.write_rows.assert_not_called()
    incremental_activities.offline_store.write_batch.assert_not_called()
    incremental_activities.persistence_activities.flush_entity_redis_to_s3.assert_not_called()


@pytest.mark.asyncio
async def test_process_incremental_batch_basic_new_data(
    incremental_activities, family_details, test_family, incremental_data
):
    """Test processing of basic new data for existing entities."""
    current_frontier_dt = test_family.frontier
    current_frontier_iso = current_frontier_dt.isoformat()

    async def mock_fetch_batches(*args, **kwargs):
        yield pl.DataFrame(incremental_data)

    incremental_activities.batch_source.fetch_batches = mock_fetch_batches
    incremental_activities.online_store.read_recent_rows.return_value = []
    with patch("temporalio.activity.heartbeat"):
        result_iso = await incremental_activities.process_incremental_batch(
            family_details, current_frontier_iso
        )
    expected_frontier_dt = max(row["ts"] for row in incremental_data)
    assert result_iso == expected_frontier_dt.isoformat()
    assert incremental_activities.online_store.read_recent_rows.call_count > 0
    assert incremental_activities.online_store.write_rows.call_count == 1
    assert incremental_activities.offline_store.write_batch.call_count > 0


@pytest.mark.asyncio
async def test_process_incremental_batch_flush_triggered(
    incremental_activities, family_details, test_family, incremental_data
):
    """Test flush triggering when an entity exceeds the threshold."""
    current_frontier_dt = test_family.frontier
    current_frontier_iso = current_frontier_dt.isoformat()

    async def mock_fetch_batches(*args, **kwargs):
        yield pl.DataFrame(incremental_data)

    incremental_activities.batch_source.fetch_batches = mock_fetch_batches
    incremental_activities.online_store.write_rows.return_value = ["user_1"]
    with patch("temporalio.activity.heartbeat"):
        result_iso = await incremental_activities.process_incremental_batch(  # noqa: F841
            family_details, current_frontier_iso
        )
    incremental_activities.persistence_activities.flush_entity_redis_to_s3.assert_called_once()
    call_args = incremental_activities.persistence_activities.flush_entity_redis_to_s3.call_args[
        0
    ][0]
    assert isinstance(call_args, FlushDetails)
    assert call_args.family_name == family_details.name
    assert call_args.packed_identifiers == "user_1"


@pytest.mark.asyncio
async def test_process_incremental_batch_timestamp_handling(
    incremental_activities, family_details, test_family
):
    """Test handling of various timestamp formats."""
    current_frontier_dt = test_family.frontier
    current_frontier_iso = current_frontier_dt.isoformat()
    base_time = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
    data_with_various_timestamps = [
        {
            "event_id": "ts1",
            "ts": base_time + timedelta(hours=1),
            "user_id": "user_1",
            "value": 10,
        },
        {
            "event_id": "ts2",
            "ts": base_time + timedelta(hours=2),
            "user_id": "user_1",
            "value": 20,
        },
        {
            "event_id": "ts3",
            "ts": None,
            "user_id": "user_1",
            "value": 30,
        },
    ]

    async def mock_fetch_batches(*args, **kwargs):
        yield pl.DataFrame(data_with_various_timestamps)

    incremental_activities.batch_source.fetch_batches = mock_fetch_batches
    with patch("temporalio.activity.heartbeat"):
        result_iso = await incremental_activities.process_incremental_batch(
            family_details, current_frontier_iso
        )
    expected_frontier_dt = base_time + timedelta(hours=2)
    assert result_iso == expected_frontier_dt.isoformat()


@pytest.mark.asyncio
async def test_process_incremental_batch_error_handling(
    incremental_activities, family_details, test_family, incremental_data
):
    """Test error handling during incremental batch processing."""
    current_frontier_dt = test_family.frontier
    current_frontier_iso = current_frontier_dt.isoformat()

    async def mock_fetch_batches(*args, **kwargs):
        yield pl.DataFrame(incremental_data)

    incremental_activities.batch_source.fetch_batches = mock_fetch_batches
    incremental_activities.online_store.write_rows = AsyncMock(
        side_effect=Exception("Failed to write to online store")
    )
    with (
        patch("temporalio.activity.heartbeat"),
        pytest.raises(Exception, match="Failed to write to online store"),
    ):
        await incremental_activities.process_incremental_batch(
            family_details, current_frontier_iso
        )


@pytest.mark.asyncio
async def test_process_incremental_batch_large_batch(
    incremental_activities, family_details, test_family
):
    """Test processing of multiple batches from fetch_batches."""
    current_frontier_dt = test_family.frontier
    current_frontier_iso = current_frontier_dt.isoformat()
    base_time = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
    batch1 = [
        {
            "event_id": "b1_e1",
            "ts": base_time + timedelta(hours=1),
            "user_id": "user_1",
            "value": 10,
        },
        {
            "event_id": "b1_e2",
            "ts": base_time + timedelta(hours=2),
            "user_id": "user_2",
            "value": 20,
        },
    ]
    batch2 = [
        {
            "event_id": "b2_e1",
            "ts": base_time + timedelta(hours=3),
            "user_id": "user_1",
            "value": 30,
        },
        {
            "event_id": "b2_e2",
            "ts": base_time + timedelta(hours=4),
            "user_id": "user_2",
            "value": 40,
        },
    ]

    async def mock_fetch_batches(*args, **kwargs):
        yield pl.DataFrame(batch1)
        yield pl.DataFrame(batch2)

    incremental_activities.batch_source.fetch_batches = mock_fetch_batches
    with patch("temporalio.activity.heartbeat") as mock_heartbeat:
        result_iso = await incremental_activities.process_incremental_batch(
            family_details, current_frontier_iso
        )
    expected_frontier_dt = base_time + timedelta(hours=4)
    assert result_iso == expected_frontier_dt.isoformat()
    assert mock_heartbeat.call_count >= 2
