import pytest
from datetime import datetime, timezone
from src.row import FeatureRow
from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    FamilyStatus,
)
from src.aggregations import AggregationFunction


@pytest.fixture
def sample_family_config():
    """Configuration for a test family."""
    return FamilyConfig(
        source=SourceConfig(
            batch=BatchSourceConfig(
                table="test_events", late_arriving_data_lag_seconds=3600
            ),
            query="SELECT event_id, ts, user_id, value FROM {{ ref }}",
        ),
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[
            FeatureConfig(
                column="value",
                aggregations=[AggregationFunction.AGGREGATION_FUNCTION_SUM],
            ),
        ],
    )


@pytest.fixture
def sample_family(sample_family_config) -> Family:
    """Create a sample Family object."""
    return Family(
        id=1,
        name="test_family",
        config=sample_family_config,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
    )


@pytest.fixture
def sample_row_dict():
    """Create a sample row dictionary."""
    return {
        "event_id": "e1",
        "ts": datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc),
        "user_id": "u1",
        "value": 10.0,
        "agg_value_sum": (10.0, 1),
    }


def test_feature_row_creation(sample_family, sample_row_dict):
    """Test creating a FeatureRow from a dictionary."""
    row = FeatureRow(sample_family, sample_row_dict)

    # Test property access
    assert row.id == "e1"
    assert row.timestamp == datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)

    # Test dictionary-like access
    assert row["event_id"] == "e1"
    assert row["ts"] == datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
    assert row["user_id"] == "u1"
    assert row["value"] == 10.0
    assert row["agg_value_sum"] == (10.0, 1)

    # Test get method
    assert row.get("event_id") == "e1"
    assert row.get("non_existent_key") is None
    assert row.get("non_existent_key", "default") == "default"


def test_feature_row_from_dict(sample_family, sample_row_dict):
    """Test creating a FeatureRow using the from_dict class method."""
    row = FeatureRow.from_dict(sample_family, sample_row_dict)

    assert row.id == "e1"
    assert row.timestamp == datetime(2023, 10, 26, 12, 0, 0, tzinfo=timezone.utc)
    assert row.get_identifier("user_id") == "u1"
    assert row.get_feature("value") == 10.0


def test_feature_row_to_dict(sample_family, sample_row_dict):
    """Test converting a FeatureRow back to a dictionary."""
    row = FeatureRow.from_dict(sample_family, sample_row_dict)
    row_dict = row.to_dict()

    assert row_dict == sample_row_dict


def test_feature_row_modification(sample_family, sample_row_dict):
    """Test modifying a FeatureRow."""
    row = FeatureRow.from_dict(sample_family, sample_row_dict)

    # Test property setters
    row.id = "e2"
    assert row.id == "e2"
    assert row["event_id"] == "e2"

    row.timestamp = datetime(2023, 10, 27, 12, 0, 0, tzinfo=timezone.utc)
    assert row.timestamp == datetime(2023, 10, 27, 12, 0, 0, tzinfo=timezone.utc)
    assert row["ts"] == datetime(2023, 10, 27, 12, 0, 0, tzinfo=timezone.utc)

    # Test dictionary-like setters
    row["value"] = 20.0
    assert row["value"] == 20.0
    assert row.get_feature("value") == 20.0

    # Test identifier setters
    row.set_identifier("user_id", "u2")
    assert row["user_id"] == "u2"
    assert row.get_identifier("user_id") == "u2"


def test_feature_row_copy(sample_family, sample_row_dict):
    """Test copying a FeatureRow."""
    row = FeatureRow.from_dict(sample_family, sample_row_dict)
    row_copy = row.copy()

    # Modify the copy
    row_copy.id = "e2"

    # Original should be unchanged
    assert row.id == "e1"
    assert row_copy.id == "e2"


def test_feature_row_ensure_timestamp_timezone(sample_family):
    """Test ensuring timestamp has timezone information."""
    # Create a row with a naive datetime
    row_dict = {
        "event_id": "e1",
        "ts": datetime(2023, 10, 26, 12, 0, 0),  # Naive datetime
        "user_id": "u1",
        "value": 10.0,
    }

    row = FeatureRow.from_dict(sample_family, row_dict)

    # Ensure timezone
    row.ensure_timestamp_timezone()

    # Timestamp should now have UTC timezone
    assert row.timestamp.tzinfo is not None
    assert row.timestamp.tzinfo == timezone.utc
