"""Unit tests for schedule management functionality."""

import pytest
import uuid
from unittest.mock import patch, MagicMock

from temporalio.client import <PERSON><PERSON> as TemporalClient

from src.shared import FamilyDetails

from tests.integration.mock_schedule_activities import MockScheduleActivities


@pytest.fixture
def test_family_name():
    """Generate a unique test family name."""
    return f"test-schedule-family-{uuid.uuid4().hex[:8]}"


@pytest.fixture
def temporal_client():
    """Create a mock Temporal client."""
    return MagicMock(spec=TemporalClient)


@pytest.fixture
def mock_schedule_activities(temporal_client):
    """Create a mock schedule activities instance."""
    return MockScheduleActivities(temporal_client)


@pytest.mark.asyncio
async def test_schedule_creation(mock_schedule_activities, test_family_name):
    """Test that a schedule can be created."""
    # Create a family
    family_details = FamilyDetails(name=test_family_name)

    # Create a schedule
    await mock_schedule_activities.create_or_update_schedule(
        family_details, 3600, "FamilyIncrementalProcessingWorkflow"
    )

    # Verify that a schedule was created
    schedule_id = f"schedule-family-incremental-{test_family_name}"
    schedule = await mock_schedule_activities.get_schedule(schedule_id)

    assert schedule is not None, f"Schedule '{schedule_id}' was not created"
    assert schedule["workflow_type_name"] == "FamilyIncrementalProcessingWorkflow"
    assert schedule["interval_seconds"] == 3600
    assert not schedule["paused"]


@pytest.mark.asyncio
async def test_schedule_deletion(mock_schedule_activities, test_family_name):
    """Test that a schedule can be deleted."""
    # Create a family
    family_details = FamilyDetails(name=test_family_name)

    # Create a schedule
    await mock_schedule_activities.create_or_update_schedule(
        family_details, 3600, "FamilyIncrementalProcessingWorkflow"
    )

    # Verify that a schedule was created
    schedule_id = f"schedule-family-incremental-{test_family_name}"
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert schedule is not None, f"Schedule '{schedule_id}' was not created"

    # Delete the schedule
    await mock_schedule_activities.delete_schedule(family_details)

    # Verify that the schedule was deleted
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert schedule is None, f"Schedule '{schedule_id}' still exists after deletion"


@pytest.mark.asyncio
async def test_schedule_trigger(mock_schedule_activities, test_family_name):
    """Test that a schedule can be triggered."""
    # Create a family
    family_details = FamilyDetails(name=test_family_name)

    # Create a schedule
    await mock_schedule_activities.create_or_update_schedule(
        family_details, 3600, "FamilyIncrementalProcessingWorkflow"
    )

    # Verify that a schedule was created
    schedule_id = f"schedule-family-incremental-{test_family_name}"
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert schedule is not None, f"Schedule '{schedule_id}' was not created"

    # Trigger the schedule
    await mock_schedule_activities.trigger_schedule(schedule_id)

    # Verify the schedule was triggered
    schedule = await mock_schedule_activities.get_schedule(schedule_id)
    assert "last_triggered" in schedule, "Schedule was not triggered"
    assert "last_workflow_id" in schedule, "Workflow was not started"

    # Verify the workflow ID format
    workflow_id = schedule["last_workflow_id"]
    assert workflow_id.startswith(f"family-incremental-run-{test_family_name}"), (
        "Incorrect workflow ID format"
    )


@pytest.mark.asyncio
async def test_family_pipeline_creates_schedule():
    """Test that FamilyPipeline creates a schedule."""
    # Create a mock schedule activities
    mock_schedule_activities = MockScheduleActivities(MagicMock())

    # Create a family
    family_details = FamilyDetails(name="test-family")

    # Mock the FamilyPipeline.run method
    with patch("src.workflows.FamilyPipeline.run") as mock_run:
        # Set up the mock to call our mock activities
        async def mock_pipeline_run(self, family_details):
            # Simulate the pipeline creating a schedule
            await mock_schedule_activities.create_or_update_schedule(
                family_details, 3600, "FamilyIncrementalProcessingWorkflow"
            )
            return None

        mock_run.side_effect = mock_pipeline_run

        # Call the mock pipeline
        await mock_pipeline_run(None, family_details)

        # Verify that a schedule was created
        schedule_id = f"schedule-family-incremental-{family_details.name}"
        schedule = await mock_schedule_activities.get_schedule(schedule_id)

        assert schedule is not None, f"Schedule '{schedule_id}' was not created"
        assert schedule["workflow_type_name"] == "FamilyIncrementalProcessingWorkflow"
        assert schedule["interval_seconds"] == 3600
