"""
Tests for the Snowflake configuration module.
"""

import os
from urllib.parse import urlparse, parse_qs


from tests.config.snowflake_config import (
    SNOWFLAKE_URL,
    SNOWFLAKE_USER,
    SNOWFLAKE_ACCOUNT,
    SNOWFLAKE_DATABASE,
    SNOWFLAKE_SCHEMA,
    SNOWFLAKE_WAREHOUSE,
    SNOWFLAKE_ROLE,
    setup_snowflake_env,
)


def test_snowflake_url_parsing():
    """Test parsing the Snowflake URL."""
    parsed_url = urlparse(SNOWFLAKE_URL)

    assert parsed_url.scheme == "snowflake", (
        f"Expected scheme 'snowflake', got '{parsed_url.scheme}'"
    )

    assert parsed_url.hostname is not None, "Hostname is missing from the URL"
    expected_hostname = f"{SNOWFLAKE_ACCOUNT}.snowflakecomputing.com"
    assert parsed_url.hostname == expected_hostname, (
        f"Expected hostname '{expected_hostname}', got '{parsed_url.hostname}'"
    )

    assert parsed_url.username is not None, "Userna<PERSON> is missing from the URL"
    assert parsed_url.username == SNOWFLAKE_USER, (
        f"Expected username '{SNOWFLAKE_USER}', got '{parsed_url.username}'"
    )

    query_params = parse_qs(parsed_url.query)

    assert "warehouse" in query_params, "Warehouse parameter is missing from the URL"
    assert query_params["warehouse"][0] == SNOWFLAKE_WAREHOUSE, (
        f"Expected warehouse '{SNOWFLAKE_WAREHOUSE}', got '{query_params['warehouse'][0]}'"
    )

    assert "role" in query_params, "Role parameter is missing from the URL"
    assert query_params["role"][0] == SNOWFLAKE_ROLE, (
        f"Expected role '{SNOWFLAKE_ROLE}', got '{query_params['role'][0]}'"
    )

    path_parts = parsed_url.path.strip("/").split("/")
    assert len(path_parts) >= 2, (
        f"Expected at least 2 path parts (database/schema), got {len(path_parts)}"
    )
    assert path_parts[0] == SNOWFLAKE_DATABASE, (
        f"Expected database '{SNOWFLAKE_DATABASE}', got '{path_parts[0]}'"
    )
    assert path_parts[1] == SNOWFLAKE_SCHEMA, (
        f"Expected schema '{SNOWFLAKE_SCHEMA}', got '{path_parts[1]}'"
    )


def test_setup_snowflake_env():
    """Test setting up Snowflake environment variables."""
    original_batch_kind = os.environ.get("SOURCE_BATCH_KIND")
    original_batch_url = os.environ.get("SOURCE_BATCH_URL")

    try:
        if "SOURCE_BATCH_KIND" in os.environ:
            del os.environ["SOURCE_BATCH_KIND"]
        if "SOURCE_BATCH_URL" in os.environ:
            del os.environ["SOURCE_BATCH_URL"]

        setup_snowflake_env()

        assert os.environ.get("SOURCE_BATCH_KIND") == "snowflake"
        assert os.environ.get("SOURCE_BATCH_URL") == SNOWFLAKE_URL

    finally:
        if original_batch_kind is not None:
            os.environ["SOURCE_BATCH_KIND"] = original_batch_kind
        elif "SOURCE_BATCH_KIND" in os.environ:
            del os.environ["SOURCE_BATCH_KIND"]

        if original_batch_url is not None:
            os.environ["SOURCE_BATCH_URL"] = original_batch_url
        elif "SOURCE_BATCH_URL" in os.environ:
            del os.environ["SOURCE_BATCH_URL"]
