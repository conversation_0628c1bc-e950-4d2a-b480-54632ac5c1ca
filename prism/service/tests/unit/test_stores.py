import pytest
import fakeredis.aioredis
from datetime import datetime, timezone, timedelta
import json
import logging

from src.stores import OnlineStore, MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS
from src.families import (
    Family,
    FamilyConfig,
    SourceConfig,
    BatchSourceConfig,
    FeatureConfig,
    FamilyStatus,
)
from src.aggregations import AggregationFunction, CountAggregation, SumAggregation
from src.csv_utils import (
    get_csv_column_order,
    format_row_to_csv_string,
    parse_csv_string_to_row,
)


# --- Fixtures ---


@pytest.fixture
def mock_redis():
    return fakeredis.aioredis.FakeRedis(decode_responses=False)


@pytest.fixture
def online_store(mock_redis):
    return OnlineStore(mock_redis)


@pytest.fixture
def sample_family_config():
    return FamilyConfig(
        source=SourceConfig(
            batch=BatchSourceConfig(table="dummy", late_arriving_data_lag_seconds=60)
        ),
        id_column="event_id",
        timestamp_column="ts",
        identifier_columns=["user_id"],
        features=[
            FeatureConfig(
                column="value",
                aggregations=[
                    AggregationFunction.AGGREGATION_FUNCTION_SUM,
                    AggregationFunction.AGGREGATION_FUNCTION_COUNT,
                ],
            ),
        ],
    )


@pytest.fixture
def sample_family(sample_family_config) -> Family:
    family = Family(
        id=1,
        name="test_family",
        config=sample_family_config,
        status=FamilyStatus.FAMILY_STATUS_RUNNING,
    )
    assert len(family.aggregation_descriptors) > 0
    assert any(
        d.column == "value" and d.aggregation == SumAggregation
        for d in family.aggregation_descriptors
    )
    assert any(
        d.column == "value" and d.aggregation == CountAggregation
        for d in family.aggregation_descriptors
    )
    return family


@pytest.fixture
def sample_rows(sample_family: Family):
    ts_now = datetime.now(timezone.utc)
    desc_map = {
        (d.column, d.aggregation.SHORT_NAME): d.state_key
        for d in sample_family.aggregation_descriptors
    }
    state_key_value_sum = desc_map[("value", "sum")]
    state_key_value_count = desc_map[("value", "count")]

    rows = [
        {
            "ts": ts_now - timedelta(minutes=2),
            "event_id": "e1",
            "user_id": "u1",
            "value": 10,
            state_key_value_sum: (10.0, 1),
            state_key_value_count: (1,),
        },
        {
            "ts": ts_now - timedelta(minutes=1),
            "event_id": "e2",
            "user_id": "u1",
            "value": 5,
            state_key_value_sum: (7.5, 2),
            state_key_value_count: (2,),
        },
        {
            "ts": ts_now - timedelta(minutes=1),
            "event_id": "e3",
            "user_id": "u2",
            "value": 100,
            state_key_value_sum: (100.0, 1),
            state_key_value_count: (1,),
        },
        {
            "ts": ts_now,
            "event_id": "e4",
            "user_id": "u1",
            "value": 2,
            state_key_value_sum: (round(17 / 3, 8), 3),
            state_key_value_count: (3,),
        },
    ]
    rows.append(
        {
            "ts": ts_now + timedelta(seconds=10),
            "event_id": "e5",
            "user_id": "u1",
            "value": None,
            state_key_value_sum: rows[-1][state_key_value_sum],
            state_key_value_count: rows[-1][state_key_value_count],
        }
    )
    return rows


# --- Tests ---


@pytest.mark.asyncio
async def test_online_store_key_for(online_store, sample_family):
    packed_id = "u1"
    expected_key = f"p:{sample_family.id}:{packed_id}"
    assert online_store._key_for(sample_family, packed_id) == expected_key


@pytest.mark.asyncio
async def test_online_store_format_parse_csv_via_utils(
    online_store, sample_family, sample_rows
):
    row = sample_rows[3]
    column_order = get_csv_column_order(sample_family, row)
    assert column_order

    csv_string = format_row_to_csv_string(row, column_order)
    assert isinstance(csv_string, str)
    assert len(csv_string) > 0
    state_key = next(k for k in row if k.startswith("agg_"))
    assert json.dumps(row[state_key]) in csv_string

    parsed_row = parse_csv_string_to_row(csv_string, column_order, sample_family)

    assert (
        parsed_row[sample_family.config.timestamp_column].isoformat()
        == row[sample_family.config.timestamp_column].isoformat()
    )

    for key, expected_value in row.items():
        if key == sample_family.config.timestamp_column:
            continue
        parsed_value = parsed_row.get(key)
        if isinstance(expected_value, tuple):
            assert isinstance(parsed_value, tuple) or isinstance(parsed_value, list)
            parsed_tuple = tuple(parsed_value)
            assert len(parsed_tuple) == len(expected_value)
            for i, item in enumerate(expected_value):
                if isinstance(item, float):
                    assert parsed_tuple[i] == pytest.approx(item)
                else:
                    assert parsed_tuple[i] == item
        elif isinstance(expected_value, float):
            assert parsed_value == pytest.approx(expected_value)
        else:
            if expected_value is None:
                assert parsed_value is None or parsed_value == ""
            else:
                assert type(parsed_value)(expected_value) == parsed_value


@pytest.mark.asyncio
async def test_online_store_write_rows(
    online_store,
    mock_redis: fakeredis.aioredis.FakeRedis,
    sample_family: Family,
    sample_rows: list,
):
    await online_store.write_rows(sample_family, sample_rows)

    key_u1 = online_store._key_for(sample_family, "u1")
    rows_u1_expected_dicts = [r for r in reversed(sample_rows) if r["user_id"] == "u1"]
    assert len(rows_u1_expected_dicts) == 4

    col_order_expected = get_csv_column_order(sample_family, rows_u1_expected_dicts[0])

    redis_content_u1_bytes = await mock_redis.lrange(key_u1, 0, -1)
    assert len(redis_content_u1_bytes) == 4

    parsed_redis_rows = []
    for csv_bytes in redis_content_u1_bytes:
        decoded_string = csv_bytes.decode("utf-8")
        parsed_dict = parse_csv_string_to_row(
            decoded_string, col_order_expected, sample_family
        )
        parsed_redis_rows.append(parsed_dict)

    for expected_row in rows_u1_expected_dicts:
        found = False
        for parsed_row in parsed_redis_rows:
            if parsed_row["event_id"] == expected_row["event_id"]:
                found = True
                assert (
                    parsed_row[sample_family.config.timestamp_column].isoformat()
                    == expected_row[sample_family.config.timestamp_column].isoformat()
                )
                break
        assert found, (
            f"Expected row with event_id {expected_row['event_id']} not found in Redis"
        )

    key_u2 = online_store._key_for(sample_family, "u2")
    rows_u2_expected_dicts = [r for r in reversed(sample_rows) if r["user_id"] == "u2"]
    assert len(rows_u2_expected_dicts) == 1
    col_order_u2_expected = get_csv_column_order(
        sample_family, rows_u2_expected_dicts[0]
    )

    redis_content_u2_bytes = await mock_redis.lrange(key_u2, 0, -1)
    assert len(redis_content_u2_bytes) == 1

    parsed_redis_rows = []
    for csv_bytes in redis_content_u2_bytes:
        decoded_string = csv_bytes.decode("utf-8")
        parsed_dict = parse_csv_string_to_row(
            decoded_string, col_order_u2_expected, sample_family
        )
        parsed_redis_rows.append(parsed_dict)

    for expected_row in rows_u2_expected_dicts:
        found = False
        for parsed_row in parsed_redis_rows:
            if parsed_row["event_id"] == expected_row["event_id"]:
                found = True
                assert (
                    parsed_row[sample_family.config.timestamp_column].isoformat()
                    == expected_row[sample_family.config.timestamp_column].isoformat()
                )
                break
        assert found, (
            f"Expected row with event_id {expected_row['event_id']} not found in Redis"
        )


@pytest.mark.asyncio
async def test_online_store_write_rows_trimming(
    online_store, mock_redis: fakeredis.aioredis.FakeRedis, sample_family: Family
):
    num_rows_to_write = MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS + 5
    rows = []
    ts = datetime.now(timezone.utc)
    desc_map = {
        (d.column, d.aggregation.SHORT_NAME): d.state_key
        for d in sample_family.aggregation_descriptors
    }
    state_key_value_sum = desc_map[("value", "sum")]
    state_key_value_count = desc_map[("value", "count")]

    for i in range(num_rows_to_write):
        rows.append(
            {
                "ts": ts + timedelta(seconds=i),
                "event_id": f"e{i}",
                "user_id": "u1",
                "value": i,
                state_key_value_sum: (float(i), 1),
                state_key_value_count: (1,),
            }
        )

    await online_store.write_rows(sample_family, rows)

    key_u1 = online_store._key_for(sample_family, "u1")
    redis_content_bytes = await mock_redis.lrange(key_u1, 0, -1)

    assert len(redis_content_bytes) == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS

    col_order = online_store._derive_column_order_for_read(sample_family)
    assert col_order

    parsed_rows = []
    for csv_bytes in redis_content_bytes:
        decoded_string = csv_bytes.decode("utf-8")
        parsed_dict = parse_csv_string_to_row(decoded_string, col_order, sample_family)
        parsed_rows.append(parsed_dict)

    event_ids = [row["event_id"] for row in parsed_rows]

    assert len(event_ids) == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS

    assert len(event_ids) == MAX_RECENT_ROWS_PER_ENTITY_IN_REDIS

    for event_id in event_ids:
        assert event_id.startswith("e"), f"Invalid event ID format: {event_id}"
        index = int(event_id[1:])
        assert 0 <= index < num_rows_to_write, (
            f"Event ID {event_id} outside valid range"
        )


@pytest.mark.asyncio
async def test_online_store_read_recent_rows(
    online_store: OnlineStore,
    mock_redis: fakeredis.aioredis.FakeRedis,
    sample_family: Family,
    sample_rows: list,
    caplog,
):
    await online_store.write_rows(sample_family, sample_rows)

    packed_id_u1 = "u1"
    caplog.set_level(logging.DEBUG)

    read_rows_u1 = await online_store.read_recent_rows(sample_family, packed_id_u1)

    expected_rows_u1_data = [r for r in reversed(sample_rows) if r["user_id"] == "u1"]
    assert len(expected_rows_u1_data) == 4

    assert len(read_rows_u1) == len(expected_rows_u1_data), (
        f"Expected {len(expected_rows_u1_data)} rows, got {len(read_rows_u1)}. Logs: {caplog.text}"
    )

    expected_event_ids = [row["event_id"] for row in expected_rows_u1_data]
    read_event_ids = [row["event_id"] for row in read_rows_u1]

    for event_id in expected_event_ids:
        assert event_id in read_event_ids, (
            f"Expected event ID {event_id} not found in read rows"
        )

    for read_row in read_rows_u1:
        for expected_row in expected_rows_u1_data:
            if read_row["event_id"] == expected_row["event_id"]:
                assert (
                    read_row.get(sample_family.config.timestamp_column).isoformat()
                    == expected_row.get(
                        sample_family.config.timestamp_column
                    ).isoformat()
                )
                break

        for key, expected_value in expected_row.items():
            if key == sample_family.config.timestamp_column:
                continue
            parsed_value = read_row.get(key)
            if isinstance(expected_value, tuple):
                assert isinstance(parsed_value, tuple) or isinstance(parsed_value, list)
                parsed_tuple = tuple(parsed_value)
                assert len(parsed_tuple) == len(expected_value)
                for idx, item in enumerate(expected_value):
                    if isinstance(item, float):
                        assert parsed_tuple[idx] == pytest.approx(item, abs=1e-6)
                    else:
                        assert parsed_tuple[idx] == item
            elif isinstance(expected_value, float):
                assert parsed_value == pytest.approx(expected_value, abs=1e-6)
            else:
                if expected_value is None:
                    assert parsed_value is None or parsed_value == ""
                else:
                    try:
                        assert type(expected_value)(parsed_value) == expected_value
                    except (ValueError, TypeError):
                        assert str(parsed_value) == str(expected_value)

    read_rows_u1_limited = await online_store.read_recent_rows(
        sample_family, packed_id_u1, num_rows=2
    )
    assert len(read_rows_u1_limited) == 2

    limited_event_ids = [row["event_id"] for row in read_rows_u1_limited]
    for event_id in limited_event_ids:
        assert event_id in read_event_ids, (
            f"Limited read returned event ID {event_id} not in full read"
        )

    packed_id_u2 = "u2"
    read_rows_u2 = await online_store.read_recent_rows(sample_family, packed_id_u2)
    expected_rows_u2_data = [r for r in reversed(sample_rows) if r["user_id"] == "u2"]
    assert len(read_rows_u2) == len(expected_rows_u2_data)
    if read_rows_u2:
        assert read_rows_u2[0]["event_id"] == expected_rows_u2_data[0]["event_id"]

    read_rows_u3 = await online_store.read_recent_rows(sample_family, "u3")
    assert read_rows_u3 == []
