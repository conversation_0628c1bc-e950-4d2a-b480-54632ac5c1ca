import pytest
import struct
import hashlib

from src.utils import calculate_partition, utcnow, to_utc_timestamp


def test_calculate_partition_basic():
    """Test basic partition calculation."""
    # Test with a single partition
    assert calculate_partition("user_1", 1) == 0

    # Test with multiple partitions
    partition = calculate_partition("user_1", 4)
    assert 0 <= partition < 4


def test_calculate_partition_consistency():
    """Test that the same input always produces the same partition."""
    # The same input should always map to the same partition
    packed_id = "user_123"
    num_partitions = 16

    partition1 = calculate_partition(packed_id, num_partitions)
    partition2 = calculate_partition(packed_id, num_partitions)

    assert partition1 == partition2


def test_calculate_partition_distribution():
    """Test that partitions are reasonably distributed."""
    num_partitions = 8
    user_ids = [f"user_{i}" for i in range(1000)]

    partitions = [calculate_partition(user_id, num_partitions) for user_id in user_ids]
    partition_counts = {i: partitions.count(i) for i in range(num_partitions)}

    # Check that all partitions are used
    assert len(partition_counts) == num_partitions

    # Check that distribution is reasonably even (within 30% of expected)
    expected_per_partition = len(user_ids) / num_partitions
    for count in partition_counts.values():
        assert 0.7 * expected_per_partition <= count <= 1.3 * expected_per_partition


def test_calculate_partition_invalid_inputs():
    """Test handling of invalid inputs."""
    # Test with zero partitions
    with pytest.raises(ValueError, match="Number of partitions must be positive"):
        calculate_partition("user_1", 0)

    # Test with negative partitions
    with pytest.raises(ValueError, match="Number of partitions must be positive"):
        calculate_partition("user_1", -5)


def test_calculate_partition_complex_identifiers():
    """Test partition calculation with complex identifiers."""
    # Test with identifiers containing special characters
    partition = calculate_partition("user|with|pipes", 16)
    assert 0 <= partition < 16

    # Test with empty string
    partition = calculate_partition("", 16)
    assert 0 <= partition < 16

    # Test with very long string
    long_id = "x" * 1000
    partition = calculate_partition(long_id, 16)
    assert 0 <= partition < 16


def test_calculate_partition_algorithm():
    """Test that the algorithm matches the expected implementation."""
    packed_id = "test_user"
    num_partitions = 16

    # Manual implementation of the algorithm
    md5_hash = hashlib.md5(packed_id.encode("utf-8"), usedforsecurity=False).hexdigest()
    hex_substring = md5_hash[16:32]
    hash_bytes = bytes.fromhex(hex_substring)
    int_value = struct.unpack(">q", hash_bytes)[0]
    expected_partition = int_value % num_partitions
    if expected_partition < 0:
        expected_partition += num_partitions

    # Compare with the function implementation
    actual_partition = calculate_partition(packed_id, num_partitions)

    assert actual_partition == expected_partition


def test_utcnow():
    """Test that utcnow returns a datetime with UTC timezone."""
    dt = utcnow()
    assert dt.tzinfo is not None
    assert dt.tzinfo.tzname(dt) == "UTC"


def test_to_utc_timestamp():
    """Test conversion to UTC timestamp."""
    from datetime import datetime, timezone, timedelta

    # Create a datetime with a non-UTC timezone
    dt = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone(timedelta(hours=5)))

    # Convert to UTC timestamp
    ts = to_utc_timestamp(dt)

    # The function replaces the timezone with UTC rather than converting the time
    # So the expected timestamp is the same datetime but with UTC timezone
    expected_ts = datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc).timestamp()

    assert ts == expected_ts
