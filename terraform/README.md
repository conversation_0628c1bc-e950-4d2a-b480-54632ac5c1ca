# ![Terraform](https://img.shields.io/badge/terraform-%235835CC.svg?style=for-the-badge&logo=terraform&logoColor=white)
This directory contains the Terraform code that powers the Gametime Data infrastructure. It provides
the foundation for managing resources across AWS and Snowflake in a consistent and scalable manner.

---


## 📖 Documentation Index
1. Terraform Setup
- Get started with Terraform and Terragrunt by following the setup guide:
  - [Terraform Setup](https://github.com/gametimesf/gametime-data/blob/master/docs/terraform/terragrunt-setup.md)
