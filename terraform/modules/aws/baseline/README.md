# baseline
<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> 1.11 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.88 |
| <a name="requirement_local"></a> [local](#requirement\_local) | ~> 2.4 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.88 |
| <a name="provider_aws.databricks_bucket_region"></a> [aws.databricks\_bucket\_region](#provider\_aws.databricks\_bucket\_region) | ~> 5.88 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_dynamodb_table.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/dynamodb_table) | resource |
| [aws_ecs_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_cluster) | resource |
| [aws_ecs_service.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_service) | resource |
| [aws_ecs_task_definition.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_task_definition) | resource |
| [aws_iam_policy.execution](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.image](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.job](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.studio](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.task](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_role.execution](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.image](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.job](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.studio](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.task](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_lb.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb) | resource |
| [aws_lb_listener.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_target_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_route53_record.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_s3_bucket.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket) | resource |
| [aws_s3_bucket_public_access_block.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_public_access_block) | resource |
| [aws_s3_bucket_server_side_encryption_configuration.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_server_side_encryption_configuration) | resource |
| [aws_s3_bucket_versioning.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_versioning) | resource |
| [aws_sagemaker_app_image_config.cpu](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_app_image_config) | resource |
| [aws_sagemaker_app_image_config.gpu](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_app_image_config) | resource |
| [aws_sagemaker_domain.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_domain) | resource |
| [aws_sagemaker_image.cpu](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_image) | resource |
| [aws_sagemaker_image.gpu](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_image) | resource |
| [aws_sagemaker_image_version.cpu](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_image_version) | resource |
| [aws_sagemaker_image_version.gpu](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_image_version) | resource |
| [aws_sagemaker_space.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_space) | resource |
| [aws_sagemaker_studio_lifecycle_config.v1](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_studio_lifecycle_config) | resource |
| [aws_sagemaker_studio_lifecycle_config.v2](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_studio_lifecycle_config) | resource |
| [aws_sagemaker_studio_lifecycle_config.v3](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_studio_lifecycle_config) | resource |
| [aws_sagemaker_user_profile.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sagemaker_user_profile) | resource |
| [aws_security_group.alb](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group.sagemaker](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_security_group.service](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/security_group) | resource |
| [aws_caller_identity.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/caller_identity) | data source |
| [aws_ecr_repository.core](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ecr_repository) | data source |
| [aws_ecr_repository.service](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ecr_repository) | data source |
| [aws_iam_policy_document.ecs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.sagemaker](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_partition.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/partition) | data source |
| [aws_route53_zone.gteng](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/route53_zone) | data source |
| [aws_s3_bucket.databricks_development](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/s3_bucket) | data source |
| [aws_subnets.private](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/subnets) | data source |
| [aws_vpc.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/vpc) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_api_credentials_arn"></a> [api\_credentials\_arn](#input\_api\_credentials\_arn) | The Secrets Manager Secret ARN for the API credentials | `string` | n/a | yes |
| <a name="input_aws_region"></a> [aws\_region](#input\_aws\_region) | The AWS region to deploy the resources in | `string` | `"us-west-1"` | no |
| <a name="input_certificate_arn"></a> [certificate\_arn](#input\_certificate\_arn) | The ACM Certificate ARN to attach to the service's ALB Listener | `string` | n/a | yes |
| <a name="input_cpu_image_tag"></a> [cpu\_image\_tag](#input\_cpu\_image\_tag) | ECR image tag that should be used as the default CPU sagemaker image version | `string` | n/a | yes |
| <a name="input_default_instance_type"></a> [default\_instance\_type](#input\_default\_instance\_type) | The default instance type used by Sagemaker Studio | `string` | `"ml.m5.large"` | no |
| <a name="input_ebs_volume_size_in_gb"></a> [ebs\_volume\_size\_in\_gb](#input\_ebs\_volume\_size\_in\_gb) | The size (in GB) of the EBS volume attached to each Sagemaker Studio Space | `number` | `100` | no |
| <a name="input_environment"></a> [environment](#input\_environment) | Name of environment (testing, staging, production) | `string` | n/a | yes |
| <a name="input_force_new_deployment"></a> [force\_new\_deployment](#input\_force\_new\_deployment) | (Optional) Enable to force a new task deployment of the service. This can be used to update tasks to use a newer Docker image with same image/tag combination (e.g. `myimage:latest`), roll Fargate tasks onto a newer platform version, or immediately deploy `ordered_placement_strategy` and `placement_constraints` updates. | `bool` | `null` | no |
| <a name="input_github_access_token_arn"></a> [github\_access\_token\_arn](#input\_github\_access\_token\_arn) | The Secrets Manager Secret ARN for the GitHub personal access token | `string` | n/a | yes |
| <a name="input_gpu_image_tag"></a> [gpu\_image\_tag](#input\_gpu\_image\_tag) | ECR image tag that should be used as the default GPU sagemaker image version | `string` | n/a | yes |
| <a name="input_idle_timeout_minutes"></a> [idle\_timeout\_minutes](#input\_idle\_timeout\_minutes) | Idle timeout in minutes for SageMaker JupyterLab applications (between 60 and 525600) | `number` | `480` | no |
| <a name="input_mwaa_vpc_cidr_blocks"></a> [mwaa\_vpc\_cidr\_blocks](#input\_mwaa\_vpc\_cidr\_blocks) | List of CIDR blocks for the MWAA VPC | `list(string)` | n/a | yes |
| <a name="input_notebook_repo"></a> [notebook\_repo](#input\_notebook\_repo) | The GitHub repo where baseline notebooks live | `string` | n/a | yes |
| <a name="input_service_cpu"></a> [service\_cpu](#input\_service\_cpu) | The number of cpu units used by each service task | `number` | `256` | no |
| <a name="input_service_desired_count"></a> [service\_desired\_count](#input\_service\_desired\_count) | The number of tasks to run for the service | `number` | `1` | no |
| <a name="input_service_image_tag"></a> [service\_image\_tag](#input\_service\_image\_tag) | The ECR image tag to use for the service | `string` | n/a | yes |
| <a name="input_service_memory"></a> [service\_memory](#input\_service\_memory) | The amount (in MiB) of memory for each service task | `number` | `512` | no |
| <a name="input_service_port"></a> [service\_port](#input\_service\_port) | The port that the baseline service should listen on | `number` | `8080` | no |
| <a name="input_users"></a> [users](#input\_users) | List of gametime.co emails that should have access to baseline | `set(string)` | n/a | yes |
| <a name="input_users_arn"></a> [users\_arn](#input\_users\_arn) | The Secrets Manager Secret ARN for the service's user config | `string` | n/a | yes |
| <a name="input_vpn_cidr_blocks"></a> [vpn\_cidr\_blocks](#input\_vpn\_cidr\_blocks) | List of source CIDR blocks for VPN traffic | `list(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_dynamo_table_arn"></a> [dynamo\_table\_arn](#output\_dynamo\_table\_arn) | ARN of the DynamoDB Table |
| <a name="output_dynamo_table_name"></a> [dynamo\_table\_name](#output\_dynamo\_table\_name) | Name of the DynamoDB Table |
| <a name="output_ecr_repository_url"></a> [ecr\_repository\_url](#output\_ecr\_repository\_url) | URL for the core ECR Repo |
| <a name="output_ecr_service_repository_url"></a> [ecr\_service\_repository\_url](#output\_ecr\_service\_repository\_url) | URL for the service ECR Repo |
| <a name="output_job_iam_role_arn"></a> [job\_iam\_role\_arn](#output\_job\_iam\_role\_arn) | ARN of the Sagemaker Training Job execution role |
| <a name="output_s3_bucket_name"></a> [s3\_bucket\_name](#output\_s3\_bucket\_name) | Name of the s3 bucket |
| <a name="output_sagemaker_cpu_image_arn"></a> [sagemaker\_cpu\_image\_arn](#output\_sagemaker\_cpu\_image\_arn) | ARN of the CPU Sagemaker Image |
| <a name="output_sagemaker_default_cpu_image_url"></a> [sagemaker\_default\_cpu\_image\_url](#output\_sagemaker\_default\_cpu\_image\_url) | URL for the default CPU Sagemaker Image Version |
| <a name="output_sagemaker_default_cpu_image_version_arn"></a> [sagemaker\_default\_cpu\_image\_version\_arn](#output\_sagemaker\_default\_cpu\_image\_version\_arn) | ARN of the default CPU Sagemaker Image Version |
| <a name="output_sagemaker_default_gpu_image_url"></a> [sagemaker\_default\_gpu\_image\_url](#output\_sagemaker\_default\_gpu\_image\_url) | URL for the default GPU Sagemaker Image Version |
| <a name="output_sagemaker_default_gpu_image_version_arn"></a> [sagemaker\_default\_gpu\_image\_version\_arn](#output\_sagemaker\_default\_gpu\_image\_version\_arn) | ARN of the default GPU Sagemaker Image Version |
| <a name="output_sagemaker_domain_arn"></a> [sagemaker\_domain\_arn](#output\_sagemaker\_domain\_arn) | ARN of the Sagemaker Domain |
| <a name="output_sagemaker_domain_id"></a> [sagemaker\_domain\_id](#output\_sagemaker\_domain\_id) | ID of the Sagemaker Domain |
| <a name="output_sagemaker_domain_url"></a> [sagemaker\_domain\_url](#output\_sagemaker\_domain\_url) | URL for the Sagemaker Domain |
| <a name="output_sagemaker_gpu_image_arn"></a> [sagemaker\_gpu\_image\_arn](#output\_sagemaker\_gpu\_image\_arn) | ARN of the GPU Sagemaker Image |
| <a name="output_sagemaker_security_group_id"></a> [sagemaker\_security\_group\_id](#output\_sagemaker\_security\_group\_id) | ID of the Security Group |
| <a name="output_sagemaker_studio_lifecycle_config_arn"></a> [sagemaker\_studio\_lifecycle\_config\_arn](#output\_sagemaker\_studio\_lifecycle\_config\_arn) | ARN of the Sagemaker Studio Lifecycle Config |
| <a name="output_subnet_ids"></a> [subnet\_ids](#output\_subnet\_ids) | IDs for subnets used by Sagemaker Studio |
<!-- END_TF_DOCS -->
