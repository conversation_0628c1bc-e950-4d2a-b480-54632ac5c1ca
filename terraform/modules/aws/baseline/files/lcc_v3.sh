#!/bin/bash
set -eux

GITHUB_USERNAME="gt-mlp-machine"

# Set global Git user settings
git config --global user.name "$GITHUB_USERNAME"
git config --global user.email "<EMAIL>"

# Create the AWS credential helper script
cat > ~/.aws-credential-helper.py <<'EOL'
#!/usr/bin/env python
import sys
import os
import boto3
import botocore

aws_region = os.environ.get("aws_region", "us-west-1")
environment = os.environ.get("environment", "staging")
GITHUB_USERNAME = os.environ.get("GITHUB_USERNAME", "gt-mlp-machine")

if len(sys.argv) < 2 or sys.argv[1] != 'get':
    exit(0)

credentials = {}
for line in sys.stdin:
    if line.strip() == "":
        break
    key, value = line.split('=', 1)
    credentials[key.strip()] = value.strip()

if credentials.get('host', '') == 'github.com' and credentials.get('username', '') == GITHUB_USERNAME:
    client = boto3.client('secretsmanager', region_name=aws_region)
    try:
        response = client.get_secret_value(SecretId=f'baseline-system/{environment}/GITHUB_PASSWORD')
    except botocore.exceptions.ClientError:
        exit(1)
    if 'SecretString' in response:
        credentials['password'] = response['SecretString']

for key, value in credentials.items():
    print(f'{key}={value}')
EOL

chmod +x ~/.aws-credential-helper.py
git config --global credential.helper ~/.aws-credential-helper.py

# Clone the repository if it does not exist
if [ ! -d "/home/<USER>/notebooks" ]; then
    echo "Cloning notebooks repo..."
    git -C /home/<USER>//$<EMAIL>/gametimesf/notebooks.git
fi

# Always update the local repository configuration
cd /home/<USER>/notebooks

echo "Configuring hooks..."
git config core.hooksPath .githooks

echo "Configuring filter..."
git config filter.strip.clean "jupyter nbconvert --to=notebook --ClearOutputPreprocessor.enabled=True --stdin --stdout --log-level=ERROR"
git config filter.strip.smudge cat
git config filter.strip.required true
