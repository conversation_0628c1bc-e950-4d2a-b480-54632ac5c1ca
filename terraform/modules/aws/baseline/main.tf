locals {
  service_name       = "baseline"
  full_name          = "${local.service_name}-${var.environment}"
  container_name     = "service"
  zone_name          = "${var.environment}.gteng.co"
  lb_dns_name        = "${local.service_name}.${local.zone_name}"
  log_group_name     = "/ecs/${var.environment}-baseline"
  https_port         = 443
  user_profile_names = { for u in var.users : replace(replace(u, "@gametime.co", ""), ".", "") => u }
  common_tags = {
    Environment = var.environment
    Project     = local.service_name
  }
}

data "aws_partition" "this" {}

data "aws_caller_identity" "this" {}

data "aws_vpc" "this" {
  filter {
    name   = "tag:Name"
    values = [var.environment]
  }
}

data "aws_subnets" "private" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.this.id]
  }

  tags = {
    Private = "true"
  }
}

data "aws_s3_bucket" "databricks_development" {
  provider = aws.databricks_bucket_region
  bucket   = "databricks-development"
}

data "aws_iam_policy_document" "sagemaker" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["sagemaker.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "ecs" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

data "aws_route53_zone" "gteng" {
  name         = local.zone_name
  private_zone = false
}

data "aws_ecr_repository" "core" {
  name = "${local.service_name}-production"
}

data "aws_ecr_repository" "service" {
  name = "${local.service_name}-service-production"
}

module "kms" {
  source = "git::https://github.com/gametimesf/tf-aws-kms.git?ref=7f896fb8ffa6cd2ba51010e4ec8ed7ed85663f19"

  env     = var.environment
  group   = "data"
  purpose = "encrypt-decrypt-${local.service_name}-bucket"
  repo    = "mlplatform"
  service = local.service_name
  squad   = "ml-platform"
  additional_key_users = [
    # Give cross-account access to the private curation service accounts
    "arn:aws:iam::************:role/internal/eks/curation-private-service-account", # development
    "arn:aws:iam::************:role/internal/eks/curation-private-service-account", # staging
    "arn:aws:iam::************:role/internal/eks/curation-private-service-account", # production
    # Give cross-account access to the curation service accounts
    "arn:aws:iam::************:role/internal/eks/curation-service-account", # development
    "arn:aws:iam::************:role/internal/eks/curation-service-account", # staging
    "arn:aws:iam::************:role/internal/eks/curation-service-account", # production
  ]
}

resource "aws_s3_bucket" "this" {
  #checkov:skip=CKV_AWS_18:no need for access logs
  #checkov:skip=CKV2_AWS_61:no need for lifecycle config
  #checkov:skip=CKV2_AWS_62:no need for event notifications
  #checkov:skip=CKV_AWS_144:no need for cross-region replication
  bucket_prefix = local.full_name

  lifecycle {
    prevent_destroy = true
  }

  tags = local.common_tags
}

resource "aws_s3_bucket_versioning" "this" {
  bucket = aws_s3_bucket.this.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "this" {
  #checkov:skip=CKV2_AWS_67:false positive
  bucket = aws_s3_bucket.this.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = "aws:kms"
      kms_master_key_id = module.kms.kms_key_id
    }
  }
}

resource "aws_s3_bucket_public_access_block" "this" {
  bucket = aws_s3_bucket.this.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_policy" "baseline_cross_account" {
  bucket = aws_s3_bucket.this.id
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          AWS = [
            "arn:aws:iam::************:root", # production account
            "arn:aws:iam::************:root", # staging account
            "arn:aws:iam::************:root"  # development account
          ]
        },
        Action = [
          "s3:ListBucket",
          "s3:GetObject",
          "s3:GetObjectVersion"
        ],
        Resource = [
          aws_s3_bucket.this.arn,
          "${aws_s3_bucket.this.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_dynamodb_table" "this" {
  name         = local.full_name
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "pk"
  range_key    = "sk"

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "sk"
    type = "S"
  }

  point_in_time_recovery {
    enabled = true
  }

  #checkov:skip=CKV_AWS_119:this rule is overly prescriptive
  server_side_encryption {
    enabled = true
  }

  lifecycle {
    prevent_destroy = true
  }

  tags = local.common_tags
}

resource "aws_iam_policy" "image" {
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Sid = "AllowECRReadAccess"
      Action = [
        "ecr:BatchCheckLayerAvailability",
        "ecr:BatchGetImage",
        "ecr:GetDownloadUrlForLayer"
      ]
      Effect   = "Allow"
      Resource = data.aws_ecr_repository.core.arn
    }]
  })

  tags = local.common_tags
}

resource "aws_iam_role" "image" {
  assume_role_policy  = data.aws_iam_policy_document.sagemaker.json
  managed_policy_arns = [aws_iam_policy.image.arn]

  tags = local.common_tags
}

resource "aws_iam_policy" "studio" {
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid      = "AllowS3GetObject"
        Action   = ["s3:GetObject"]
        Effect   = "Allow"
        Resource = "${aws_s3_bucket.this.arn}/jobs/*/assets.tar.gz"
        }, {
        # Allow access to the databricks development bucket. This is a temporary workaround.
        Sid = "AllowDatabricksDevelopmentS3Access"
        Action = [
          "s3:GetObject",
          "s3:ListBucket",
        ]
        Effect = "Allow"
        Resource = [
          data.aws_s3_bucket.databricks_development.arn,
          "${data.aws_s3_bucket.databricks_development.arn}/*",
        ]
        }, {
        Sid    = "AllowSecretsManagerGetSecretValue"
        Action = ["secretsmanager:GetSecretValue"]
        Effect = "Allow"
        Resource = [
          "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline/production/*",
          "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline-system/production/*",
          "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline/staging/*",
          "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline-system/staging/*"
        ]
        }, {
        # Add KMS permissions for decrypting objects
        Sid = "AllowKMSDecrypt"
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Effect   = "Allow"
        Resource = module.kms.kms_key_arn
      }
    ]
  })
  tags = local.common_tags
}

resource "aws_iam_role" "studio" {
  assume_role_policy = data.aws_iam_policy_document.sagemaker.json
  managed_policy_arns = [
    "arn:aws:iam::aws:policy/AmazonSageMakerFullAccess",
    aws_iam_policy.studio.arn
  ]

  tags = local.common_tags
}

resource "aws_iam_policy" "job" {
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "AllowS3Access"
        Action = [
          "s3:GetObject",
          "s3:PutObject"
        ]
        Effect   = "Allow"
        Resource = "${aws_s3_bucket.this.arn}/*"
      },
      {
        # Allow access to the databricks development bucket. This is a temporary workaround.
        Sid = "AllowDatabricksDevelopmentS3Access"
        Action = [
          "s3:GetObject",
          "s3:ListBucket",
        ]
        Effect = "Allow"
        Resource = [
          data.aws_s3_bucket.databricks_development.arn,
          "${data.aws_s3_bucket.databricks_development.arn}/*",
        ]
      },
      {
        Sid    = "AllowSecretsManagerGetSecretValue"
        Action = ["secretsmanager:GetSecretValue"]
        Effect = "Allow"
        Resource = [
          "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline/production/*",
          "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline-system/production/*",
          "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline/staging/*",
          "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline-system/staging/*"
        ]
      },
      {
        # Add KMS permissions for encrypting and decrypting objects
        Sid = "AllowKMSEncryptDecrypt"
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Effect   = "Allow"
        Resource = module.kms.kms_key_arn
      },
      {
        Sid = "AllowEFSAccess"
        Action = [
          "elasticfilesystem:ClientMount",
          "elasticfilesystem:ClientWrite",
          "elasticfilesystem:DescribeFileSystems",
          "elasticfilesystem:DescribeMountTargets",
        ]
        Effect   = "Allow"
        Resource = module.efs.arn
      }
    ]
  })
  tags = local.common_tags
}

resource "aws_iam_role" "job" {
  assume_role_policy = data.aws_iam_policy_document.sagemaker.json
  managed_policy_arns = [
    "arn:aws:iam::aws:policy/AmazonSageMakerFullAccess",
    aws_iam_policy.job.arn
  ]

  tags = local.common_tags
}

resource "aws_security_group" "sagemaker" {
  #checkov:skip=CKV2_AWS_5:this security group is used by the sagemaker domain
  description = "security group for the ${local.service_name} ${var.environment} sagemaker domain"
  vpc_id      = data.aws_vpc.this.id
  egress {
    description      = "Allow all outbound traffic"
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = local.common_tags
}

resource "aws_sagemaker_studio_lifecycle_config" "v1" {
  studio_lifecycle_config_name     = local.full_name
  studio_lifecycle_config_app_type = "JupyterLab"
  studio_lifecycle_config_content = base64encode(templatefile("${path.module}/files/lcc.sh", {
    environment = var.environment
    aws_region  = var.aws_region
  }))

  tags = local.common_tags
}

resource "aws_sagemaker_studio_lifecycle_config" "v2" {
  studio_lifecycle_config_name     = "${local.full_name}-v2"
  studio_lifecycle_config_app_type = "JupyterLab"
  studio_lifecycle_config_content = base64encode(templatefile("${path.module}/files/lcc_v2.sh", {
    environment = var.environment
    aws_region  = var.aws_region
  }))

  tags = local.common_tags
}

resource "aws_sagemaker_studio_lifecycle_config" "v3" {
  studio_lifecycle_config_name     = "${local.full_name}-v3"
  studio_lifecycle_config_app_type = "JupyterLab"
  studio_lifecycle_config_content = base64encode(templatefile("${path.module}/files/lcc_v3.sh", {
    environment = var.environment
    aws_region  = var.aws_region
  }))

  tags = local.common_tags
}

resource "aws_sagemaker_image" "cpu" {
  image_name = "${local.full_name}-cpu"
  role_arn   = aws_iam_role.image.arn

  tags = local.common_tags
}

resource "aws_sagemaker_image" "gpu" {
  image_name = "${local.full_name}-gpu"
  role_arn   = aws_iam_role.image.arn

  tags = local.common_tags
}

resource "aws_sagemaker_image_version" "cpu" {
  image_name = aws_sagemaker_image.cpu.id
  base_image = "${data.aws_ecr_repository.core.repository_url}:${var.cpu_image_tag}"
}

resource "aws_sagemaker_image_version" "gpu" {
  image_name = aws_sagemaker_image.gpu.id
  base_image = "${data.aws_ecr_repository.core.repository_url}:${var.gpu_image_tag}"
}

resource "aws_sagemaker_app_image_config" "cpu" {
  app_image_config_name = "${local.full_name}-cpu"
  jupyter_lab_image_config {
    container_config {
      container_environment_variables = {
        "JUPYTER_ENABLE_LAB" : "yes",
        "BASELINE_S3_BUCKET" : aws_s3_bucket.this.id,
        "BASELINE_ENVIRONMENT" : var.environment
      }
    }
  }

  tags = local.common_tags
}

resource "aws_sagemaker_app_image_config" "gpu" {
  app_image_config_name = "${local.full_name}-gpu"
  jupyter_lab_image_config {
    container_config {
      container_environment_variables = {
        "JUPYTER_ENABLE_LAB" : "yes",
        "BASELINE_S3_BUCKET" : aws_s3_bucket.this.id,
        "BASELINE_ENVIRONMENT" : var.environment
      }
    }
  }

  tags = local.common_tags
}

resource "aws_sagemaker_domain" "this" {
  #checkov:skip=CKV_AWS_187:this rule is outdated in the new version of sagemaker studio as there is no EFS by default
  domain_name             = local.full_name
  auth_mode               = "IAM"
  app_network_access_type = "VpcOnly"
  vpc_id                  = data.aws_vpc.this.id
  subnet_ids              = data.aws_subnets.private.ids

  default_user_settings {
    execution_role      = aws_iam_role.studio.arn
    default_landing_uri = "studio::"
    studio_web_portal   = "ENABLED"
    security_groups     = [aws_security_group.sagemaker.id]
    space_storage_settings {
      default_ebs_storage_settings {
        default_ebs_volume_size_in_gb = var.ebs_volume_size_in_gb
        maximum_ebs_volume_size_in_gb = var.ebs_volume_size_in_gb
      }
    }
    jupyter_lab_app_settings {
      custom_image {
        app_image_config_name = aws_sagemaker_app_image_config.cpu.app_image_config_name
        image_name            = aws_sagemaker_image.cpu.id
      }
      custom_image {
        app_image_config_name = aws_sagemaker_app_image_config.gpu.app_image_config_name
        image_name            = aws_sagemaker_image.gpu.id
      }
      default_resource_spec {
        instance_type               = var.default_instance_type
        lifecycle_config_arn        = aws_sagemaker_studio_lifecycle_config.v3.arn
        sagemaker_image_arn         = aws_sagemaker_image.cpu.arn
        sagemaker_image_version_arn = aws_sagemaker_image_version.cpu.arn
      }
      app_lifecycle_management {
        idle_settings {
          lifecycle_management        = "ENABLED"
          idle_timeout_in_minutes     = var.idle_timeout_minutes
          max_idle_timeout_in_minutes = 525600 # max allowed by AWS
          min_idle_timeout_in_minutes = 60     # min allowed by AWS
        }
      }
      lifecycle_config_arns = [aws_sagemaker_studio_lifecycle_config.v1.arn, aws_sagemaker_studio_lifecycle_config.v2.arn, aws_sagemaker_studio_lifecycle_config.v3.arn]
    }
  }

  tags = local.common_tags
}

resource "aws_sagemaker_user_profile" "this" {
  for_each          = local.user_profile_names
  domain_id         = aws_sagemaker_domain.this.id
  user_profile_name = each.key

  tags = merge(local.common_tags, {
    "BaselineJupyterRunBy" = each.value
  })
}

resource "aws_sagemaker_space" "this" {
  for_each   = local.user_profile_names
  domain_id  = aws_sagemaker_domain.this.id
  space_name = "${local.service_name}-jupyter--${each.key}"
  ownership_settings {
    owner_user_profile_name = each.key
  }
  space_sharing_settings {
    sharing_type = "Private"
  }
  space_settings {
    app_type = "JupyterLab"
    jupyter_lab_app_settings {
      default_resource_spec {
        instance_type               = var.default_instance_type
        lifecycle_config_arn        = aws_sagemaker_studio_lifecycle_config.v3.arn
        sagemaker_image_arn         = aws_sagemaker_image.cpu.arn
        sagemaker_image_version_arn = aws_sagemaker_image_version.cpu.arn
      }
      app_lifecycle_management {
        idle_settings {
          idle_timeout_in_minutes = var.idle_timeout_minutes
        }
      }
    }
  }

  depends_on = [aws_sagemaker_user_profile.this]

  tags = merge(local.common_tags, {
    "BaselineJupyterRunBy" = each.value
  })
}

resource "aws_iam_policy" "task" {
  #checkov:skip=CKV_AWS_290
  #checkov:skip=CKV_AWS_355
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Sid = "AllowSagemakerAccess"
      Action = [
        "sagemaker:AddTags",
        "sagemaker:DescribeApp",
        "sagemaker:CreateApp",
        "sagemaker:DeleteApp",
        "sagemaker:CreateTrainingJob",
        "sagemaker:StopTrainingJob",
        "sagemaker:CreatePresignedDomainUrl",
        "secretsmanager:ListSecrets"
      ]
      Effect   = "Allow"
      Resource = "*"
      }, {
      Sid = "AllowSecretsManagerWriteAccess"
      Action = [
        "secretsmanager:CreateSecret",
        "secretsmanager:UpdateSecret",
        "secretsmanager:DeleteSecret"
      ]
      Effect   = "Allow"
      Resource = "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline/${var.environment}/*"
      }, {
      Sid      = "AllowS3ReadAccess"
      Action   = ["s3:GetObject"]
      Effect   = "Allow"
      Resource = "${aws_s3_bucket.this.arn}/*"
      }, {
      Sid = "AllowDynamoAccess"
      Action = [
        "dynamodb:GetItem",
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:Query"
      ]
      Effect   = "Allow"
      Resource = aws_dynamodb_table.this.arn
      }, {
      Sid      = "AllowIAMPassRole"
      Action   = ["iam:passrole"]
      Effect   = "Allow"
      Resource = aws_iam_role.job.arn
      Condition = {
        StringEquals = {
          "iam:PassedToService" : "sagemaker.amazonaws.com"
        }
      }
      }, {
      # Add KMS permissions for reading encrypted objects
      Sid = "AllowKMSDecrypt"
      Action = [
        "kms:Decrypt",
        "kms:DescribeKey"
      ]
      Effect   = "Allow"
      Resource = module.kms.kms_key_arn
      }, {
      Sid = "AllowCloudWatchLogsAccess"
      Action = [
        "logs:DescribeLogStreams",
        "logs:GetLogEvents",
        "logs:FilterLogEvents"
      ]
      Effect = "Allow"
      Resource = [
        "arn:${data.aws_partition.this.partition}:logs:${var.aws_region}:${data.aws_caller_identity.this.account_id}:log-group:/aws/sagemaker/TrainingJobs",
        "arn:${data.aws_partition.this.partition}:logs:${var.aws_region}:${data.aws_caller_identity.this.account_id}:log-group:/aws/sagemaker/TrainingJobs:*"
      ]
    }]
  })
  tags = local.common_tags
}

resource "aws_iam_role" "task" {
  assume_role_policy = data.aws_iam_policy_document.ecs.json
  managed_policy_arns = [
    aws_iam_policy.task.arn
  ]

  tags = local.common_tags
}

resource "aws_iam_policy" "execution" {
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Sid = "AllowECRGetAuthorizationToken"
      Action = [
        "ecr:GetAuthorizationToken",
      ]
      Effect   = "Allow"
      Resource = "*"
      }, {
      Sid = "AllowCloudwatchLogsWriteAccess"
      Action = [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ]
      Effect   = "Allow"
      Resource = "arn:${data.aws_partition.this.partition}:logs:${var.aws_region}:${data.aws_caller_identity.this.account_id}:log-group:${local.log_group_name}:*"
      }, {
      Sid = "AllowSecretsManagerGetSecretValue"
      Action = [
        "secretsmanager:GetSecretValue"
      ]
      Effect   = "Allow"
      Resource = "arn:${data.aws_partition.this.partition}:secretsmanager:${var.aws_region}:${data.aws_caller_identity.this.account_id}:secret:baseline-system/${var.environment}/*"
      }, {
      Sid = "AllowECRReadAccess"
      Action = [
        "ecr:BatchCheckLayerAvailability",
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchGetImage"
      ]
      Effect   = "Allow"
      Resource = data.aws_ecr_repository.service.arn
    }]
  })

  tags = local.common_tags
}

resource "aws_iam_role" "execution" {
  assume_role_policy = data.aws_iam_policy_document.ecs.json
  managed_policy_arns = [
    aws_iam_policy.execution.arn
  ]

  tags = local.common_tags
}

resource "aws_security_group" "alb" {
  description = "security group for the ${local.service_name} ${var.environment} ALB"
  vpc_id      = data.aws_vpc.this.id

  ingress {
    from_port   = local.https_port
    to_port     = local.https_port
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.this.cidr_block]
    description = "vpc"
  }

  ingress {
    from_port   = local.https_port
    to_port     = local.https_port
    protocol    = "tcp"
    cidr_blocks = var.vpn_cidr_blocks
    description = "perimeter 81"
  }

  ingress {
    from_port   = local.https_port
    to_port     = local.https_port
    protocol    = "tcp"
    cidr_blocks = var.mwaa_vpc_cidr_blocks
    description = "Allow gRPC traffic on port 443 from MWAA VPC"
  }

  ingress {
    from_port       = local.https_port
    to_port         = local.https_port
    protocol        = "tcp"
    security_groups = [aws_security_group.sagemaker.id]
    description     = "Allow traffic from Sagemaker Studio"
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
    description      = "Allow all outbound traffic"
  }


  tags = local.common_tags
}

resource "aws_lb" "this" {
  #checkov:skip=CKV_AWS_150:unnecessary for internal service
  #checkov:skip=CKV_AWS_91:no need for access logs
  #checkov:skip=CKV_AWS_131:grpc
  name               = local.full_name
  internal           = true
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = data.aws_subnets.private.ids

  tags = local.common_tags
}

resource "aws_lb_target_group" "this" {
  name             = local.full_name
  port             = var.service_port
  protocol         = "HTTP"
  protocol_version = "GRPC"
  target_type      = "ip"
  vpc_id           = data.aws_vpc.this.id

  health_check {
    enabled  = true
    protocol = "HTTP"
    path     = "/"
    matcher  = "16"
  }

  tags = local.common_tags
}

resource "aws_lb_listener" "this" {
  load_balancer_arn = aws_lb.this.arn
  port              = local.https_port
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = var.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.this.arn
  }

  tags = local.common_tags
}

resource "aws_route53_record" "this" {
  zone_id = data.aws_route53_zone.gteng.id
  name    = local.lb_dns_name
  type    = "A"

  alias {
    name                   = aws_lb.this.dns_name
    zone_id                = aws_lb.this.zone_id
    evaluate_target_health = false
  }
}

resource "aws_ecs_task_definition" "this" {
  family                   = local.full_name
  execution_role_arn       = aws_iam_role.execution.arn
  task_role_arn            = aws_iam_role.task.arn
  network_mode             = "awsvpc"
  cpu                      = var.service_cpu
  memory                   = var.service_memory
  requires_compatibilities = ["FARGATE"]
  container_definitions = jsonencode([{
    name      = local.container_name
    image     = "${data.aws_ecr_repository.service.repository_url}:${var.service_image_tag}"
    essential = true

    portMappings = [{
      containerPort = var.service_port
    }]

    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-create-group  = "true"
        awslogs-group         = local.log_group_name
        awslogs-region        = var.aws_region
        awslogs-stream-prefix = "ecs"
      }
    }

    secrets = [{
      name      = "GITHUB_ACCESS_TOKEN"
      valueFrom = var.github_access_token_arn
      }, {
      name      = "USERS"
      valueFrom = var.users_arn
    }]

    environment = [{
      name  = "GITHUB_REPO"
      value = var.notebook_repo
      }, {
      name  = "PORT"
      value = tostring(var.service_port)
      }, {
      name  = "ENVIRONMENT"
      value = var.environment
      }, {
      name  = "API_URL"
      value = "${local.lb_dns_name}:${local.https_port}"
      }, {
      name  = "API_CREDENTIALS_ARN"
      value = var.api_credentials_arn
      }, {
      name  = "GITHUB_ACCESS_TOKEN_ARN"
      value = var.github_access_token_arn
      }, {
      name  = "DYNAMO_TABLE"
      value = aws_dynamodb_table.this.id
      }, {
      name  = "S3_BUCKET"
      value = aws_s3_bucket.this.id
      }, {
      name  = "SAGEMAKER_DOMAIN_ID"
      value = aws_sagemaker_domain.this.id
      }, {
      name  = "SAGEMAKER_LIFECYCLE_CONFIG_ARN"
      value = aws_sagemaker_studio_lifecycle_config.v3.arn
      }, {
      name  = "SAGEMAKER_CPU_IMAGE_ARN"
      value = aws_sagemaker_image.cpu.arn
      }, {
      name  = "SAGEMAKER_GPU_IMAGE_ARN"
      value = aws_sagemaker_image.gpu.arn
      }, {
      name  = "SAGEMAKER_DEFAULT_CPU_IMAGE_URL"
      value = aws_sagemaker_image_version.cpu.container_image
      }, {
      name  = "SAGEMAKER_DEFAULT_CPU_IMAGE_VERSION_ARN"
      value = aws_sagemaker_image_version.cpu.arn
      }, {
      name  = "SAGEMAKER_DEFAULT_GPU_IMAGE_URL"
      value = aws_sagemaker_image_version.gpu.container_image
      }, {
      name  = "SAGEMAKER_DEFAULT_GPU_IMAGE_VERSION_ARN"
      value = aws_sagemaker_image_version.gpu.arn
      }, {
      name  = "SAGEMAKER_VPC_SECURITY_GROUP_ID"
      value = aws_security_group.sagemaker.id
      }, {
      name  = "SAGEMAKER_VPC_SUBNETS"
      value = join(",", data.aws_subnets.private.ids)
      }, {
      name  = "SAGEMAKER_JOB_ROLE_ARN"
      value = aws_iam_role.job.arn
      }, {
      name  = "EFS_FILE_SYSTEM_ID"
      value = module.efs.id
      }, {
      name  = "EFS_JOBS_ACCESS_POINT_ID"
      value = module.efs.access_points["jobs"].id
    }]
  }])

  tags = local.common_tags
}

resource "aws_ecs_cluster" "this" {
  #checkov:skip=CKV_AWS_65:no need for container insights
  name = local.full_name

  tags = local.common_tags
}

resource "aws_security_group" "service" {
  description = "security group for the ${local.service_name} ${var.environment} ECS service"
  vpc_id      = data.aws_vpc.this.id

  ingress {
    from_port       = var.service_port
    to_port         = var.service_port
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
    description     = "alb"
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
    description      = "Allow all outbound traffic"
  }

  tags = local.common_tags
}

resource "aws_ecs_service" "this" {
  name                 = local.full_name
  cluster              = aws_ecs_cluster.this.id
  task_definition      = aws_ecs_task_definition.this.arn
  desired_count        = var.service_desired_count
  launch_type          = "FARGATE"
  force_new_deployment = var.force_new_deployment

  network_configuration {
    subnets          = data.aws_subnets.private.ids
    security_groups  = [aws_security_group.service.id]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.this.arn
    container_name   = local.container_name
    container_port   = var.service_port
  }

  tags = local.common_tags
}

resource "aws_security_group" "efs" {
  description = "security group for the ${local.service_name} ${var.environment} EFS"
  vpc_id      = data.aws_vpc.this.id

  ingress {
    description     = "Allow NFS traffic from SageMaker"
    from_port       = 2049
    to_port         = 2049
    protocol        = "tcp"
    security_groups = [aws_security_group.sagemaker.id]
  }

  egress {
    description      = "Allow all outbound traffic"
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = merge(local.common_tags, {
    Name = "${local.full_name}-efs"
  })
}

module "efs" {
  source           = "terraform-aws-modules/efs/aws"
  version          = "~> 1.8"
  name             = local.full_name
  encrypted        = true
  kms_key_arn      = module.kms.kms_key_arn
  performance_mode = "generalPurpose"
  throughput_mode  = "bursting"
  mount_targets = {
    for subnet_id in data.aws_subnets.private.ids : subnet_id => {
      subnet_id = subnet_id
    }
  }
  security_group_description = "Security group for ${local.full_name} EFS file system"
  security_group_vpc_id      = data.aws_vpc.this.id
  security_group_rules = {
    sagemaker = {
      description              = "Allow NFS traffic from SageMaker"
      type                     = "ingress"
      from_port                = 2049
      to_port                  = 2049
      protocol                 = "tcp"
      source_security_group_id = aws_security_group.sagemaker.id
    }
  }
  access_points = {
    jobs = {
      name = "jobs"
      posix_user = {
        gid = 100
        uid = 1000
      }
      root_directory = {
        path = "/jobs"
        creation_info = {
          owner_gid   = 100
          owner_uid   = 1000
          permissions = "0777"
        }
      }
    }
  }
  tags = local.common_tags
}
