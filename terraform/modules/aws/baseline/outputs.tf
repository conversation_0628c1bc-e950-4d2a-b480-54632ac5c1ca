output "sagemaker_domain_arn" {
  description = "ARN of the Sagemaker Domain"
  value       = aws_sagemaker_domain.this.arn
}

output "sagemaker_domain_id" {
  description = "ID of the Sagemaker Domain"
  value       = aws_sagemaker_domain.this.id
}

output "sagemaker_domain_url" {
  description = "URL for the Sagemaker Domain"
  value       = aws_sagemaker_domain.this.url
}

output "sagemaker_studio_lifecycle_config_arn" {
  description = "ARN of the Sagemaker Studio Lifecycle Config"
  value       = aws_sagemaker_studio_lifecycle_config.v3.arn
}

output "sagemaker_cpu_image_arn" {
  description = "ARN of the CPU Sagemaker Image"
  value       = aws_sagemaker_image.cpu.arn
}

output "sagemaker_gpu_image_arn" {
  description = "ARN of the GPU Sagemaker Image"
  value       = aws_sagemaker_image.gpu.arn
}

output "sagemaker_default_cpu_image_version_arn" {
  description = "ARN of the default CPU Sagemaker Image Version"
  value       = aws_sagemaker_image_version.cpu.arn
}

output "sagemaker_default_gpu_image_version_arn" {
  description = "ARN of the default GPU Sagemaker Image Version"
  value       = aws_sagemaker_image_version.gpu.arn
}

output "sagemaker_default_cpu_image_url" {
  description = "URL for the default CPU Sagemaker Image Version"
  value       = aws_sagemaker_image_version.cpu.container_image
}

output "sagemaker_default_gpu_image_url" {
  description = "URL for the default GPU Sagemaker Image Version"
  value       = aws_sagemaker_image_version.gpu.container_image
}

output "ecr_repository_url" {
  description = "URL for the core ECR Repo"
  value       = data.aws_ecr_repository.core.repository_url
}

output "s3_bucket_name" {
  description = "Name of the s3 bucket"
  value       = aws_s3_bucket.this.id
}

output "job_iam_role_arn" {
  description = "ARN of the Sagemaker Training Job execution role"
  value       = aws_iam_role.job.arn
}

output "dynamo_table_arn" {
  description = "ARN of the DynamoDB Table"
  value       = aws_dynamodb_table.this.arn
}

output "dynamo_table_name" {
  description = "Name of the DynamoDB Table"
  value       = aws_dynamodb_table.this.id
}

output "sagemaker_security_group_id" {
  description = "ID of the Security Group"
  value       = aws_security_group.sagemaker.id
}

output "subnet_ids" {
  description = "IDs for subnets used by Sagemaker Studio"
  value       = data.aws_subnets.private.ids
}

output "ecr_service_repository_url" {
  description = "URL for the service ECR Repo"
  value       = data.aws_ecr_repository.service.repository_url
}

output "efs_file_system_id" {
  description = "The ID of the EFS file system"
  value       = module.efs.id
}

output "efs_jobs_access_point_id" {
  description = "The ID of the EFS access point for jobs"
  value       = module.efs.access_points["jobs"].id
}
