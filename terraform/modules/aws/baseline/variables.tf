variable "aws_region" {
  description = "The AWS region to deploy the resources in"
  type        = string
  default     = "us-west-1"
}

variable "ebs_volume_size_in_gb" {
  description = "The size (in GB) of the EBS volume attached to each Sagemaker Studio Space"
  type        = number
  default     = 100
}

variable "default_instance_type" {
  description = "The default instance type used by Sagemaker Studio"
  type        = string
  default     = "ml.m5.large"
}

variable "force_new_deployment" {
  description = "(Optional) Enable to force a new task deployment of the service. This can be used to update tasks to use a newer Docker image with same image/tag combination (e.g. `myimage:latest`), roll Fargate tasks onto a newer platform version, or immediately deploy `ordered_placement_strategy` and `placement_constraints` updates."
  type        = bool
  default     = null
}

variable "service_desired_count" {
  description = "The number of tasks to run for the service"
  type        = number
  default     = 1
}

variable "service_cpu" {
  description = "The number of cpu units used by each service task"
  type        = number
  default     = 256
}

variable "service_memory" {
  description = "The amount (in MiB) of memory for each service task"
  type        = number
  default     = 512
}

variable "service_port" {
  description = "The port that the baseline service should listen on"
  type        = number
  default     = 8080
}

variable "environment" {
  description = "Name of environment (testing, staging, production)"
  type        = string
}

variable "users" {
  description = "List of gametime.co emails that should have access to baseline"
  type        = set(string)
}

variable "cpu_image_tag" {
  description = "ECR image tag that should be used as the default CPU sagemaker image version"
  type        = string
}

variable "gpu_image_tag" {
  description = "ECR image tag that should be used as the default GPU sagemaker image version"
  type        = string
}

variable "service_image_tag" {
  description = "The ECR image tag to use for the service"
  type        = string
}

variable "idle_timeout_minutes" {
  description = "Idle timeout in minutes for SageMaker JupyterLab applications (between 60 and 525600)"
  type        = number
  default     = 480 # 8 hours
}

variable "notebook_repo" {
  description = "The GitHub repo where baseline notebooks live"
  type        = string
}

variable "api_credentials_arn" {
  description = "The Secrets Manager Secret ARN for the API credentials"
  type        = string
}

variable "github_access_token_arn" {
  description = "The Secrets Manager Secret ARN for the GitHub personal access token"
  type        = string
}

variable "users_arn" {
  description = "The Secrets Manager Secret ARN for the service's user config"
  type        = string
}

variable "certificate_arn" {
  description = "The ACM Certificate ARN to attach to the service's ALB Listener"
  type        = string
}

variable "vpn_cidr_blocks" {
  description = "List of source CIDR blocks for VPN traffic"
  type        = list(string)
}

variable "mwaa_vpc_cidr_blocks" {
  description = "List of CIDR blocks for the MWAA VPC"
  type        = list(string)
}
