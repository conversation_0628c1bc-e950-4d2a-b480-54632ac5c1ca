resource "aws_codeartifact_domain" "this" {
  domain = var.domain_name

  tags = var.tags
}

resource "aws_codeartifact_repository" "pypi_store" {
  repository = "pypi-store"
  domain     = aws_codeartifact_domain.this.domain

  description = "Provides PyPI artifacts from PyPI."

  external_connections {
    external_connection_name = "public:pypi"
  }

  tags = var.tags
}

resource "aws_codeartifact_repository" "this" {
  repository   = var.repository_name
  domain       = aws_codeartifact_domain.this.domain
  domain_owner = aws_codeartifact_domain.this.owner

  description = var.description

  # Use upstream block instead of separate resource
  upstream {
    repository_name = aws_codeartifact_repository.pypi_store.repository
  }

  tags = var.tags
}

resource "aws_iam_policy" "codeartifact_access" {
  name        = "codeartifact-${var.domain_name}-${var.repository_name}-access"
  description = "Policy for accessing CodeArtifact repository ${var.repository_name}"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "codeartifact:GetAuthorizationToken",
          "codeartifact:GetRepositoryEndpoint",
          "codeartifact:ReadFromRepository",
          "codeartifact:PublishPackageVersion",
          "codeartifact:PutPackageMetadata"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "sts:GetServiceBearerToken"
        ]
        Resource = "*"
        Condition = {
          StringEquals = {
            "sts:AWSServiceName" = "codeartifact.amazonaws.com"
          }
        }
      }
    ]
  })

  tags = var.tags
}

resource "aws_codeartifact_domain_permissions_policy" "cross_account" {
  domain = aws_codeartifact_domain.this.domain
  policy_document = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = formatlist("arn:aws:iam::%s:root", var.consumer_account_ids)
        }
        Action = [
          "codeartifact:GetDomainPermissionsPolicy",
          "codeartifact:ListRepositoriesInDomain",
          "codeartifact:GetAuthorizationToken"
        ]
        Resource = aws_codeartifact_domain.this.arn
      }
    ]
  })
}

resource "aws_codeartifact_repository_permissions_policy" "cross_account" {
  repository = aws_codeartifact_repository.this.repository
  domain     = aws_codeartifact_domain.this.domain
  policy_document = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = formatlist("arn:aws:iam::%s:root", var.consumer_account_ids)
        }
        Action = [
          "codeartifact:ReadFromRepository",
          "codeartifact:GetRepositoryEndpoint",
          "codeartifact:ListPackages",
          "codeartifact:ListPackageVersions",
          "codeartifact:DescribePackageVersion",
          "codeartifact:GetPackageVersionReadme",
          "codeartifact:GetPackageVersionAssets"
        ]
        Resource = aws_codeartifact_repository.this.arn
      }
    ]
  })
}

data "aws_caller_identity" "current" {}
