output "domain_name" {
  description = "Name of the CodeArtifact domain"
  value       = aws_codeartifact_domain.this.domain
}

output "domain_owner" {
  description = "Owner of the CodeArtifact domain"
  value       = aws_codeartifact_domain.this.owner
}

output "repository_name" {
  description = "Name of the CodeArtifact repository"
  value       = aws_codeartifact_repository.this.repository
}

output "repository_arn" {
  description = "ARN of the CodeArtifact repository"
  value       = aws_codeartifact_repository.this.arn
}

output "iam_policy_arn" {
  description = "ARN of the IAM policy for accessing the CodeArtifact repository"
  value       = aws_iam_policy.codeartifact_access.arn
}

output "cross_account_configuration_instructions" {
  description = "Instructions for configuring consumer accounts to access this CodeArtifact repository"
  value       = <<-EOT
    To access this CodeArtifact repository from other AWS accounts, follow these steps in each account:

    1. Create an IAM policy in the consumer account with the following content:

    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": [
            "codeartifact:GetAuthorizationToken",
            "codeartifact:GetRepositoryEndpoint",
            "codeartifact:ReadFromRepository"
          ],
          "Resource": "*"
        },
        {
          "Effect": "Allow",
          "Action": [
            "sts:GetServiceBearerToken"
          ],
          "Resource": "*",
          "Condition": {
            "StringEquals": {
              "sts:AWSServiceName": "codeartifact.amazonaws.com"
            }
          }
        }
      ]
    }

    2. Attach this policy to the IAM roles/users in the consumer account that need access

    3. Configure pip or twine in the consumer account to use:
       - Domain: ${aws_codeartifact_domain.this.domain}
       - Repository: ${aws_codeartifact_repository.this.repository}
       - Domain Owner: ${aws_codeartifact_domain.this.owner}

    4. To login with AWS CLI from the consumer account:
       aws codeartifact login --tool pip --domain ${aws_codeartifact_domain.this.domain} --domain-owner ${aws_codeartifact_domain.this.owner} --repository ${aws_codeartifact_repository.this.repository}
  EOT
}

output "authorized_consumer_accounts" {
  description = "List of AWS accounts authorized to access this CodeArtifact repository"
  value       = var.consumer_account_ids
}
