variable "domain_name" {
  description = "Name of the CodeArtifact domain"
  type        = string
}

variable "repository_name" {
  description = "Name of the CodeArtifact repository"
  type        = string
}

variable "description" {
  description = "Description of the CodeArtifact repository"
  type        = string
  default     = "CodeArtifact repository for Python packages"
}

variable "tags" {
  description = "A map of tags to assign to the resources"
  type        = map(string)
  default     = {}
}

variable "consumer_account_ids" {
  description = "List of AWS Account IDs that will consume the CodeArtifact repository"
  type        = list(string)
  default     = []
}
