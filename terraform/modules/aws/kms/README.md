# kms

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.6.1 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.39 |

## Providers

No providers.

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_kms"></a> [kms](#module\_kms) | git::https://github.com/terraform-aws-modules/terraform-aws-kms.git | fe1beca2118c0cb528526e022a53381535bb93cd |

## Resources

No resources.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_environment"></a> [environment](#input\_environment) | Environment the resources are being deployed to | `string` | n/a | yes |
| <a name="input_user_type"></a> [user\_type](#input\_user\_type) | Type of user that will have access to this kms key | `string` | n/a | yes |

## Outputs

No outputs.
<!-- END_TF_DOCS -->
