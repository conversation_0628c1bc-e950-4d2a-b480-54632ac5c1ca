module "kms" {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-kms.git?ref=fe1beca2118c0cb528526e022a53381535bb93cd"

  description = "Encryption key for Data Team Admins"
  key_usage   = "ENCRYPT_DECRYPT"
  aliases     = ["team/data/${var.user_type}"]

  tags = {
    env        = var.environment
    managed-by = "terraform"
    repo       = "gametime-data"
    team       = "data"
  }
}
