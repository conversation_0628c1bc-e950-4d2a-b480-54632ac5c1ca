#### Add awscc provider - must use override file to add a new required_provider https://developer.hashicorp.com/terraform/language/files/override
generate "awscc_versions_override.tf" {
  path      = "awscc_versions_override.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
terraform {
  required_providers {
    awscc = {
      source  = "hashicorp/awscc"
      version = "0.51.0"
    }
  }
}

provider "awscc" {
  region = "us-west-1"
}
EOF
}
