### Add datadog provider - must use override file to add a new required_provider https://developer.hashicorp.com/terraform/language/files/override
generate "datadog_versions_override.tf" {
  path      = "datadog_versions_override.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
terraform {
  required_providers {
    datadog = {
      source  = "DataDog/datadog"
      version = "3.43.1"
    }
  }
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
  api_url = "https://api.us5.datadoghq.com/"

  # Overridable tags that all Datadog resources will inherit
  default_tags {
    tags = var.default_tags
  }
}
EOF
}
