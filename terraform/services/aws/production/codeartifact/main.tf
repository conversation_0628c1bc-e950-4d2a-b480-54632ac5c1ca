module "codeartifact" {
  source = "../../../../modules/aws/codeartifact"
  providers = {
    aws = aws.us-west-2 # Explicitly use us-west-2 for CodeArtifact
  }

  domain_name     = "ml-artifacts"
  repository_name = "python-packages"
  description     = "Repository for Python ML packages"
  # TODO: Add the account IDs to terragrunt map!
  consumer_account_ids = [
    "************", # development
    "************", # staging
    "************", # production
  ]

  tags = {
    Environment = "production"
    Project     = "mlplatform"
    Team        = "ml-platform"
    ManagedBy   = "terraform"
  }
}
