#### Terragrunt Attributes
# Safety check in case someone lowers the versions
terraform_version_constraint  = "~> 1.11.0"
terragrunt_version_constraint = ">=0.71.2"


#### Locals - used for terragrunt specific operations or as variable inputs
locals {
  repo         = "mlplatform"
  environment  = basename(dirname(get_terragrunt_dir())) # Extract the environment from the directory
  service_name = basename(get_terragrunt_dir())          # Extract the service name from the directory
  account_id   = "************"

  # Global vars
  globals = {
    primary_region = "us-west-2"
  }

  # Read in secrets via SOPS
  secrets         = try(jsondecode(sops_decrypt_file(find_in_parent_folders("secrets.json"))), {})
  env_secrets     = try(jsondecode(sops_decrypt_file(find_in_parent_folders("env_secrets.json"))), {})
  project_secrets = try(jsondecode(sops_decrypt_file("project_secrets.json")), {}) # these live in the leaf directory, i.e. not found by find_in_parent_folders

  # Manual extractions since these are directly referenced
  primary_region = local.globals.primary_region

  role_arn       = get_env("TG_ROLE_ARN", "arn:aws:iam::${local.account_id}:role/internal/atlantis-tf")
  state_role_arn = get_env("TG_ROLE_ARN", "arn:aws:iam::${local.account_id}:role/internal/atlantis-tf-state")
}


#### Variable injection
# This allows us to inject the locals defined above as variables into the child modules
inputs = merge(
  local.globals,
  local.secrets,
  local.env_secrets,
  local.project_secrets,
  {
    environment = local.environment,
  }
)


#### File generation
# Remote state
remote_state {
  backend = "s3"
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
  config = {
    bucket         = "gt-ml-platform-tf-state"
    key            = "${local.repo}/aws/${path_relative_to_include()}/terraform.tfstate"
    region         = "us-west-2"
    dynamodb_table = "gt-ml-platform-tf-state-locking"
    encrypt        = true
    #     assume_role = {
    #       role_arn = local.state_role_arn
    #     }

    # KMS Key Alias: data/ml-platform/terraform
    kms_key_id = "arn:aws:kms:us-west-2:${local.account_id}:key/38f47b15-257a-4c87-9267-0c2ef677e278"
  }
}

# Default versions
# Ensures all subdirectories are using the same versions of Terraform and providers
generate "default_versions.tf" {
  path      = "default_versions.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
terraform {
  required_version = "~> 1.11"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.39"
    }
  }
}

provider "aws" {
  region = "${local.primary_region}"
#   assume_role {
#     role_arn = "${local.role_arn}"
#   }

  # Overridable tags that all AWS resources will inherit
  default_tags {
    tags = var.default_tags
  }
}

provider "aws" {
  region = "us-west-1"
  alias  = "us-west-1"
#   assume_role {
#     role_arn = "${local.role_arn}"
#   }

  # Overridable tags that all AWS resources will inherit
  default_tags {
    tags = var.default_tags
  }
}

provider "aws" {
  region = "us-west-2"
  alias  = "us-west-2"
#   assume_role {
#     role_arn = "${local.role_arn}"
#   }

  # Overridable tags that all AWS resources will inherit
  default_tags {
    tags = var.default_tags
  }
}
EOF
}

# Global variables
# These are variables that you'd like all your subdirectories to easily reference, if necessary
generate "global_variables.tf" {
  path      = "global_variables.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
variable "datadog_api_key" {
  description = "The API key for Datadog used to manage all Datadog resources using Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "The app key for Datadog used to manage all Datadog resources using Terraform"
  type        = string
  sensitive   = true
}

variable "default_tags" {
  description = "Default tags applied to all AWS resources"
  type        = map(string)
  default     = {
    env         = "${local.environment}"
    repo        = "${local.repo}"
    service     = "${local.service_name}"
    team        = "ml-platform"
    managed-by  = "terraform"
  }
}

variable "environment" {
  description = "The environment in which the resources are being deployed"
  type        = string
}
EOF
}
