# Define common configuration for ECR repositories
locals {
  # Repository definitions
  repositories = [
    {
      name = "baseline-staging"
    },
    {
      name = "baseline-service-staging"
    }
  ]

  # Common tags for all repositories
  common_tags = {
    Project     = "baseline"
    ManagedBy   = "terraform"
    Environment = "staging"
    Team        = "ml-platform"
  }
}

module "ecr_repositories" {
  providers = {
    aws = aws.us-west-1
  }
  source   = "../../../../modules/aws/ecr-repository"
  for_each = { for repo in local.repositories : repo.name => repo }
  name     = each.key
  tags     = local.common_tags
}
